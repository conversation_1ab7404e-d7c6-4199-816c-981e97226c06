import React, { useState } from 'react';
import { X, ChevronLeft, ChevronRight } from 'lucide-react';

interface GalleryImage {
  id: string;
  url: string;
  title: string;
  category: string;
}

const OurWork: React.FC = () => {
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Gallery images - 48 images (4 columns x 12 rows)
  const galleryImages: GalleryImage[] = [
    // Row 1
    { id: '1', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75', title: 'Business Card Design', category: 'Business Cards' },
    { id: '2', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75', title: 'Marketing Brochure', category: 'Marketing Materials' },
    { id: '3', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75', title: 'Event Banner', category: 'Signs & Banners' },
    { id: '4', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75', title: 'Wedding Invitation', category: 'Invitations & Stationery' },
    
    // Row 2
    { id: '5', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75', title: 'Logo Design', category: 'Design Services' },
    { id: '6', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75', title: 'Custom T-Shirt', category: 'Apparel' },
    { id: '7', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75', title: 'Product Labels', category: 'Stickers & Labels' },
    { id: '8', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75', title: 'Photo Canvas', category: 'Gifts & Décor' },
    
    // Row 3
    { id: '9', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75', title: 'Corporate Flyer', category: 'Marketing Materials' },
    { id: '10', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75', title: 'Promotional Poster', category: 'Signs & Banners' },
    { id: '11', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75', title: 'Brand Identity', category: 'Design Services' },
    { id: '12', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725923064812.jpg&w=1080&q=75', title: 'Business Stationery', category: 'Business Cards' },
    
    // Row 4
    { id: '13', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789523124.jpg&w=1080&q=75', title: 'Vehicle Wrap Design', category: 'Design Services' },
    { id: '14', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733181112948.jpg&w=1080&q=75', title: 'Trade Show Display', category: 'Signs & Banners' },
    { id: '15', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75', title: 'Custom Stickers', category: 'Stickers & Labels' },
    { id: '16', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75', title: 'Anniversary Invitation', category: 'Invitations & Stationery' },
    
    // Row 5
    { id: '17', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75', title: 'Restaurant Menu', category: 'Marketing Materials' },
    { id: '18', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75', title: 'Polo Shirt Design', category: 'Apparel' },
    { id: '19', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75', title: 'Custom Photo Book', category: 'Gifts & Décor' },
    { id: '20', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75', title: 'Professional Letterhead', category: 'Business Cards' },
    
    // Row 6
    { id: '21', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75', title: 'Event Poster', category: 'Signs & Banners' },
    { id: '22', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75', title: 'Package Design', category: 'Design Services' },
    { id: '23', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75', title: 'Vinyl Decals', category: 'Stickers & Labels' },
    { id: '24', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75', title: 'Birthday Invitation', category: 'Invitations & Stationery' },
    
    // Row 7
    { id: '25', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75', title: 'Catalog Design', category: 'Marketing Materials' },
    { id: '26', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725923064812.jpg&w=1080&q=75', title: 'Hoodie Design', category: 'Apparel' },
    { id: '27', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75', title: 'Personalized Mug', category: 'Gifts & Décor' },
    { id: '28', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75', title: 'Envelope Design', category: 'Business Cards' },
    
    // Row 8
    { id: '29', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75', title: 'Yard Sign', category: 'Signs & Banners' },
    { id: '30', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75', title: 'Website Design', category: 'Design Services' },
    { id: '31', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75', title: 'Product Stickers', category: 'Stickers & Labels' },
    { id: '32', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75', title: 'Graduation Announcement', category: 'Invitations & Stationery' },
    
    // Row 9
    { id: '33', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75', title: 'Door Hanger', category: 'Marketing Materials' },
    { id: '34', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75', title: 'Baseball Cap', category: 'Apparel' },
    { id: '35', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75', title: 'Wall Art', category: 'Gifts & Décor' },
    { id: '36', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75', title: 'Folder Design', category: 'Business Cards' },
    
    // Row 10
    { id: '37', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75', title: 'Banner Design', category: 'Signs & Banners' },
    { id: '38', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725923064812.jpg&w=1080&q=75', title: 'Illustration Art', category: 'Design Services' },
    { id: '39', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75', title: 'Label Design', category: 'Stickers & Labels' },
    { id: '40', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75', title: 'Save the Date', category: 'Invitations & Stationery' },
    
    // Row 11
    { id: '41', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75', title: 'Rack Card', category: 'Marketing Materials' },
    { id: '42', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75', title: 'Jacket Design', category: 'Apparel' },
    { id: '43', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75', title: 'Custom Calendar', category: 'Gifts & Décor' },
    { id: '44', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75', title: 'Presentation Folder', category: 'Business Cards' },
    
    // Row 12
    { id: '45', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75', title: 'Window Cling', category: 'Signs & Banners' },
    { id: '46', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75', title: 'Brand Guidelines', category: 'Design Services' },
    { id: '47', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75', title: 'Bumper Stickers', category: 'Stickers & Labels' },
    { id: '48', url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75', title: 'Corporate Event Invitation', category: 'Invitations & Stationery' },
  ];

  const openModal = (image: GalleryImage, index: number) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const navigateImage = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? (currentImageIndex - 1 + galleryImages.length) % galleryImages.length
      : (currentImageIndex + 1) % galleryImages.length;
    
    setCurrentImageIndex(newIndex);
    setSelectedImage(galleryImages[newIndex]);
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (selectedImage) {
      if (e.key === 'Escape') {
        closeModal();
      } else if (e.key === 'ArrowLeft') {
        navigateImage('prev');
      } else if (e.key === 'ArrowRight') {
        navigateImage('next');
      }
    }
  };

  React.useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedImage, currentImageIndex]);

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <section className="bg-warm-cream py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Our Work
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Explore our portfolio of professional designs and printing projects. 
              From business cards to large format displays, see the quality and creativity 
              we bring to every project.
            </p>
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {galleryImages.map((image, index) => (
              <div
                key={image.id}
                className="group cursor-pointer overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                onClick={() => openModal(image, index)}
              >
                <div className="aspect-square overflow-hidden bg-gray-100">
                  <img
                    src={image.url}
                    alt={image.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                </div>
                
                {/* Overlay with title on hover */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-60 transition-all duration-300 flex items-end">
                  <div className="p-4 w-full transform translate-y-full group-hover:translate-y-0 transition-transform duration-300">
                    <h3 className="text-white font-semibold text-sm mb-1">{image.title}</h3>
                    <p className="text-white/80 text-xs">{image.category}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Modal for enlarged image */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
          <div className="relative max-w-6xl max-h-full w-full">
            {/* Close button */}
            <button
              onClick={closeModal}
              className="absolute top-4 right-4 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 rounded-full p-2"
            >
              <X className="h-6 w-6" />
            </button>

            {/* Previous button */}
            <button
              onClick={() => navigateImage('prev')}
              className="absolute left-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 rounded-full p-2"
            >
              <ChevronLeft className="h-6 w-6" />
            </button>

            {/* Next button */}
            <button
              onClick={() => navigateImage('next')}
              className="absolute right-4 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 z-10 bg-black bg-opacity-50 rounded-full p-2"
            >
              <ChevronRight className="h-6 w-6" />
            </button>

            {/* Image */}
            <div className="flex items-center justify-center h-full">
              <img
                src={selectedImage.url}
                alt={selectedImage.title}
                className="max-w-full max-h-full object-contain rounded-lg"
              />
            </div>

            {/* Image info */}
            <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-75 text-white p-4 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">{selectedImage.title}</h3>
              <p className="text-gray-300">Category: {selectedImage.category}</p>
              <p className="text-sm text-gray-400 mt-2">
                Image {currentImageIndex + 1} of {galleryImages.length} • Use arrow keys to navigate
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Call to Action */}
      <section className="py-16 bg-warm-cream">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Ready to Create Something Amazing?
          </h2>
          <p className="text-lg text-gray-600 mb-8">
            Let our talented team bring your vision to life with professional design and printing services.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/design-services"
              className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
            >
              Get Design
            </a>
            <a
              href="/contact"
              className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
            >
              Need a hand?
            </a>
          </div>
        </div>
      </section>
    </div>
  );
};

export default OurWork;