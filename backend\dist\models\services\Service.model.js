"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceModel = exports.ServiceMetadata = exports.ServiceRating = exports.ServicePrice = void 0;
// Service Domain Model - Encapsulates service business logic and validation
const client_1 = require("@prisma/client");
const BaseModel_1 = require("../base/BaseModel");
const validation_schemas_1 = require("../schemas/validation.schemas");
// Value Objects for Service domain
class ServicePrice extends BaseModel_1.ValueObject {
    constructor(amount, type) {
        super();
        this.amount = amount;
        this.type = type;
        this.validate();
    }
    validate() {
        if (this.amount <= 0) {
            throw new Error('Service price must be positive');
        }
        if (this.amount > 100000) {
            throw new Error('Service price cannot exceed $100,000');
        }
        if (Math.round(this.amount * 100) !== this.amount * 100) {
            throw new Error('Service price must be in cents (max 2 decimal places)');
        }
    }
    getAmount() {
        return this.amount;
    }
    getType() {
        return this.type;
    }
    getFormattedPrice() {
        const formatter = new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        });
        switch (this.type) {
            case client_1.ServicePriceType.FIXED:
                return formatter.format(this.amount);
            case client_1.ServicePriceType.HOURLY:
                return `${formatter.format(this.amount)}/hour`;
            case client_1.ServicePriceType.QUOTE:
                return 'Quote on request';
            default:
                return formatter.format(this.amount);
        }
    }
    calculateEstimate(quantity = 1, hours) {
        switch (this.type) {
            case client_1.ServicePriceType.FIXED:
                return this.amount * quantity;
            case client_1.ServicePriceType.HOURLY:
                if (!hours) {
                    throw new Error('Hours required for hourly pricing');
                }
                return this.amount * hours * quantity;
            case client_1.ServicePriceType.QUOTE:
                throw new Error('Cannot calculate estimate for quote-based pricing');
            default:
                return this.amount * quantity;
        }
    }
    getEqualityComponents() {
        return [this.amount, this.type];
    }
}
exports.ServicePrice = ServicePrice;
class ServiceRating extends BaseModel_1.ValueObject {
    constructor(rating, totalReviews) {
        super();
        this.rating = rating;
        this.totalReviews = totalReviews;
        this.validate();
    }
    validate() {
        if (this.rating < 0 || this.rating > 5) {
            throw new Error('Rating must be between 0 and 5');
        }
        if (this.totalReviews < 0) {
            throw new Error('Total reviews cannot be negative');
        }
    }
    getRating() {
        return this.rating;
    }
    getTotalReviews() {
        return this.totalReviews;
    }
    getStars() {
        const fullStars = Math.floor(this.rating);
        const hasHalfStar = this.rating % 1 >= 0.5;
        const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
        return '★'.repeat(fullStars) +
            (hasHalfStar ? '☆' : '') +
            '☆'.repeat(emptyStars);
    }
    getDisplayRating() {
        if (this.totalReviews === 0) {
            return 'No reviews yet';
        }
        return `${this.rating.toFixed(1)} (${this.totalReviews} review${this.totalReviews !== 1 ? 's' : ''})`;
    }
    isHighlyRated() {
        return this.rating >= 4.0 && this.totalReviews >= 5;
    }
    getEqualityComponents() {
        return [this.rating, this.totalReviews];
    }
}
exports.ServiceRating = ServiceRating;
class ServiceMetadata extends BaseModel_1.ValueObject {
    constructor(images, tags, requirements, deliverables) {
        super();
        this.images = images;
        this.tags = tags;
        this.requirements = requirements;
        this.deliverables = deliverables;
        this.validate();
    }
    validate() {
        if (this.images.length > 10) {
            throw new Error('Maximum 10 images allowed per service');
        }
        this.images.forEach(image => {
            if (!/^https?:\/\/.+/.test(image)) {
                throw new Error('All images must be valid URLs');
            }
        });
        if (this.tags.length > 20) {
            throw new Error('Maximum 20 tags allowed per service');
        }
        this.tags.forEach(tag => {
            if (tag.trim().length === 0 || tag.length > 50) {
                throw new Error('Tags must be between 1 and 50 characters');
            }
        });
        if (this.requirements && this.requirements.length > 1000) {
            throw new Error('Requirements cannot exceed 1000 characters');
        }
        if (this.deliverables && this.deliverables.length > 1000) {
            throw new Error('Deliverables cannot exceed 1000 characters');
        }
    }
    getImages() {
        return [...this.images];
    }
    getTags() {
        return [...this.tags];
    }
    getRequirements() {
        return this.requirements;
    }
    getDeliverables() {
        return this.deliverables;
    }
    getPrimaryImage() {
        return this.images[0];
    }
    hasTag(tag) {
        return this.tags.some(t => t.toLowerCase() === tag.toLowerCase());
    }
    getEqualityComponents() {
        return [this.images, this.tags, this.requirements, this.deliverables];
    }
}
exports.ServiceMetadata = ServiceMetadata;
// Service Domain Model
class ServiceModel extends BaseModel_1.BaseModel {
    constructor(data) {
        super(data);
        this._validationSchema = validation_schemas_1.serviceSchema;
        this.price = new ServicePrice(data.price, data.priceType);
        this.rating = new ServiceRating(data.rating || 0, data.totalReviews);
        this.metadata = new ServiceMetadata(data.images, data.tags, data.requirements || undefined, data.deliverables || undefined);
    }
    // Business Logic Methods
    getName() {
        return this._data.name;
    }
    getDescription() {
        return this._data.description;
    }
    getPrice() {
        return this.price;
    }
    getRating() {
        return this.rating;
    }
    getMetadata() {
        return this.metadata;
    }
    getCategoryId() {
        return this._data.categoryId;
    }
    getProviderId() {
        return this._data.providerId;
    }
    getUserId() {
        return this._data.userId;
    }
    getDuration() {
        return this._data.duration || undefined;
    }
    getLocation() {
        return this._data.location || undefined;
    }
    isActive() {
        return this._data.isActive;
    }
    isFeatured() {
        return this._data.isFeatured;
    }
    getTotalOrders() {
        return this._data.totalOrders;
    }
    getConversionRate() {
        // Calculate based on some business logic
        // This is a placeholder - would typically come from analytics
        return this.rating.getRating() >= 4 ? 0.15 : 0.08;
    }
    getPopularityScore() {
        // Weighted score based on orders, rating, and reviews
        const orderWeight = Math.min(this._data.totalOrders / 100, 1) * 0.4;
        const ratingWeight = (this.rating.getRating() / 5) * 0.4;
        const reviewWeight = Math.min(this.rating.getTotalReviews() / 50, 1) * 0.2;
        return Math.round((orderWeight + ratingWeight + reviewWeight) * 100);
    }
    isEligibleForFeaturing() {
        return this._data.isActive &&
            this.rating.getRating() >= 4.0 &&
            this.rating.getTotalReviews() >= 3 &&
            this._data.totalOrders >= 5;
    }
    // Business Actions
    updateBasicInfo(updates) {
        try {
            if (updates.name && (updates.name.trim().length < 3 || updates.name.length > 200)) {
                return BaseModel_1.Result.failure('Service name must be between 3 and 200 characters');
            }
            if (updates.description && (updates.description.trim().length < 10 || updates.description.length > 2000)) {
                return BaseModel_1.Result.failure('Description must be between 10 and 2000 characters');
            }
            if (updates.duration && updates.duration <= 0) {
                return BaseModel_1.Result.failure('Duration must be positive');
            }
            this.updateData({
                ...this._data,
                name: updates.name?.trim() ?? this._data.name,
                description: updates.description?.trim() ?? this._data.description,
                location: updates.location?.trim() ?? this._data.location,
                duration: updates.duration ?? this._data.duration,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ServiceUpdated',
                aggregateId: this.id,
                data: { serviceId: this.id, providerId: this._data.providerId, changes: updates },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update service');
        }
    }
    updatePricing(amount, type) {
        try {
            const newPrice = new ServicePrice(amount, type);
            this.price = newPrice;
            this.updateData({
                ...this._data,
                price: amount,
                priceType: type,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ServicePriceUpdated',
                aggregateId: this.id,
                data: {
                    serviceId: this.id,
                    providerId: this._data.providerId,
                    oldPrice: this._data.price,
                    newPrice: amount,
                    oldType: this._data.priceType,
                    newType: type
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update pricing');
        }
    }
    updateMetadata(updates) {
        try {
            const newMetadata = new ServiceMetadata(updates.images ?? this.metadata.getImages(), updates.tags ?? this.metadata.getTags(), updates.requirements ?? this.metadata.getRequirements(), updates.deliverables ?? this.metadata.getDeliverables());
            this.metadata = newMetadata;
            this.updateData({
                ...this._data,
                images: newMetadata.getImages(),
                tags: newMetadata.getTags(),
                requirements: newMetadata.getRequirements() || null,
                deliverables: newMetadata.getDeliverables() || null,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ServiceMetadataUpdated',
                aggregateId: this.id,
                data: { serviceId: this.id, providerId: this._data.providerId, changes: updates },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update metadata');
        }
    }
    activate() {
        if (this._data.isActive) {
            return BaseModel_1.Result.failure('Service is already active');
        }
        const validation = this.validate();
        if (!validation.isValid) {
            return BaseModel_1.Result.failure(`Cannot activate service: ${validation.errors.join(', ')}`);
        }
        this.updateData({
            ...this._data,
            isActive: true,
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'ServiceActivated',
            aggregateId: this.id,
            data: { serviceId: this.id, providerId: this._data.providerId },
            timestamp: new Date(),
            version: 1,
        });
        return BaseModel_1.Result.success(undefined);
    }
    deactivate(reason) {
        if (!this._data.isActive) {
            return BaseModel_1.Result.failure('Service is already deactivated');
        }
        this.updateData({
            ...this._data,
            isActive: false,
            isFeatured: false, // Remove featuring when deactivated
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'ServiceDeactivated',
            aggregateId: this.id,
            data: { serviceId: this.id, providerId: this._data.providerId, reason },
            timestamp: new Date(),
            version: 1,
        });
        return BaseModel_1.Result.success(undefined);
    }
    feature() {
        if (!this.isEligibleForFeaturing()) {
            return BaseModel_1.Result.failure('Service is not eligible for featuring. Requirements: active, 4+ rating, 3+ reviews, 5+ orders');
        }
        if (this._data.isFeatured) {
            return BaseModel_1.Result.failure('Service is already featured');
        }
        this.updateData({
            ...this._data,
            isFeatured: true,
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'ServiceFeatured',
            aggregateId: this.id,
            data: { serviceId: this.id, providerId: this._data.providerId },
            timestamp: new Date(),
            version: 1,
        });
        return BaseModel_1.Result.success(undefined);
    }
    unfeature() {
        if (!this._data.isFeatured) {
            return BaseModel_1.Result.failure('Service is not featured');
        }
        this.updateData({
            ...this._data,
            isFeatured: false,
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'ServiceUnfeatured',
            aggregateId: this.id,
            data: { serviceId: this.id, providerId: this._data.providerId },
            timestamp: new Date(),
            version: 1,
        });
        return BaseModel_1.Result.success(undefined);
    }
    recordOrder() {
        this.updateData({
            ...this._data,
            totalOrders: this._data.totalOrders + 1,
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'ServiceOrderRecorded',
            aggregateId: this.id,
            data: { serviceId: this.id, providerId: this._data.providerId, totalOrders: this._data.totalOrders + 1 },
            timestamp: new Date(),
            version: 1,
        });
    }
    updateRating(newRating, totalReviews) {
        try {
            const rating = new ServiceRating(newRating, totalReviews);
            this.rating = rating;
            this.updateData({
                ...this._data,
                rating: newRating,
                totalReviews: totalReviews,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ServiceRatingUpdated',
                aggregateId: this.id,
                data: {
                    serviceId: this.id,
                    providerId: this._data.providerId,
                    rating: newRating,
                    totalReviews: totalReviews
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update rating');
        }
    }
    // Business Rule Validation
    validateBusinessRules() {
        const errors = [];
        // Basic validation
        if (!this._data.name || this._data.name.trim().length < 3) {
            errors.push('Service name must be at least 3 characters long');
        }
        if (!this._data.description || this._data.description.trim().length < 10) {
            errors.push('Service description must be at least 10 characters long');
        }
        if (!this._data.categoryId) {
            errors.push('Service must belong to a category');
        }
        if (!this._data.providerId) {
            errors.push('Service must have a provider');
        }
        // Business constraints
        if (this._data.isFeatured && !this.isEligibleForFeaturing()) {
            errors.push('Featured service must meet eligibility requirements');
        }
        if (this._data.isActive && this.metadata.getImages().length === 0) {
            errors.push('Active service must have at least one image');
        }
        if (this._data.priceType === client_1.ServicePriceType.HOURLY && !this._data.duration) {
            errors.push('Hourly services should specify expected duration');
        }
        return errors;
    }
    // Static Factory Methods
    static createService(serviceData) {
        try {
            // Validate price
            const price = new ServicePrice(serviceData.price, serviceData.priceType);
            // Validate metadata
            const metadata = new ServiceMetadata(serviceData.images || [], serviceData.tags || [], serviceData.requirements, serviceData.deliverables);
            const now = new Date();
            const service = new ServiceModel({
                id: '', // Will be set by the database
                name: serviceData.name.trim(),
                description: serviceData.description.trim(),
                categoryId: serviceData.categoryId,
                providerId: serviceData.providerId,
                userId: serviceData.userId,
                price: serviceData.price,
                priceType: serviceData.priceType,
                duration: serviceData.duration || null,
                location: serviceData.location?.trim() || null,
                isActive: true,
                isFeatured: false,
                images: metadata.getImages(),
                tags: metadata.getTags(),
                requirements: metadata.getRequirements() || null,
                deliverables: metadata.getDeliverables() || null,
                rating: null,
                totalReviews: 0,
                totalOrders: 0,
                createdAt: now,
                updatedAt: now,
            });
            const validation = service.validate();
            if (!validation.isValid) {
                return BaseModel_1.Result.failure(`Validation failed: ${validation.errors.join(', ')}`);
            }
            return BaseModel_1.Result.success(service);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to create service');
        }
    }
    static fromPrismaService(prismaService) {
        return new ServiceModel(prismaService);
    }
    // Serialization methods for API responses
    toPublicObject() {
        return {
            ...this._data,
            formattedPrice: this.price.getFormattedPrice(),
            ratingDisplay: this.rating.getDisplayRating(),
            popularityScore: this.getPopularityScore(),
            isEligibleForFeaturing: this.isEligibleForFeaturing(),
        };
    }
    toSearchResult() {
        return {
            id: this._data.id,
            name: this._data.name,
            description: this._data.description,
            price: this._data.price,
            formattedPrice: this.price.getFormattedPrice(),
            rating: this.rating.getRating(),
            totalReviews: this.rating.getTotalReviews(),
            totalOrders: this._data.totalOrders,
            primaryImage: this.metadata.getPrimaryImage(),
            tags: this.metadata.getTags(),
            location: this._data.location || undefined,
            isFeatured: this._data.isFeatured,
        };
    }
}
exports.ServiceModel = ServiceModel;
//# sourceMappingURL=Service.model.js.map