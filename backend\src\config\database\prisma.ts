// Prisma Database Client - Following Singleton pattern and DIP
import { PrismaClient } from '@prisma/client';

// Singleton pattern for database connection
class DatabaseClient {
  private static instance: PrismaClient;
  private static isConnected = false;

  public static getInstance(): PrismaClient {
    if (!DatabaseClient.instance) {
      DatabaseClient.instance = new PrismaClient({
        log: process.env.NODE_ENV === 'development' 
          ? ['query', 'info', 'warn', 'error']
          : ['error'],
        errorFormat: 'pretty',
      });

      // Add middleware for soft deletes, logging, etc.
      DatabaseClient.instance.$use(async (params, next) => {
        // Log all database operations in development
        if (process.env.NODE_ENV === 'development') {
          const before = Date.now();
          const result = await next(params);
          const after = Date.now();
          console.log(`Query ${params.model}.${params.action} took ${after - before}ms`);
          return result;
        }
        return next(params);
      });
    }

    return DatabaseClient.instance;
  }

  public static async connect(): Promise<void> {
    if (!DatabaseClient.isConnected) {
      try {
        const client = DatabaseClient.getInstance();
        await client.$connect();
        DatabaseClient.isConnected = true;
        console.log('✅ Database connected successfully');
      } catch (error) {
        console.error('❌ Database connection failed:', error);
        throw error;
      }
    }
  }

  public static async disconnect(): Promise<void> {
    if (DatabaseClient.isConnected && DatabaseClient.instance) {
      try {
        await DatabaseClient.instance.$disconnect();
        DatabaseClient.isConnected = false;
        console.log('✅ Database disconnected successfully');
      } catch (error) {
        console.error('❌ Database disconnection failed:', error);
        throw error;
      }
    }
  }

  public static async healthCheck(): Promise<boolean> {
    try {
      const client = DatabaseClient.getInstance();
      await client.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      console.error('❌ Database health check failed:', error);
      return false;
    }
  }

  public static getConnectionStatus(): boolean {
    return DatabaseClient.isConnected;
  }
}

// Export singleton instance
export const prisma = DatabaseClient.getInstance();
export const {
  connect: connectDatabase,
  disconnect: disconnectDatabase,
  healthCheck: checkDatabaseHealth,
  getConnectionStatus: getDatabaseConnectionStatus
} = DatabaseClient;