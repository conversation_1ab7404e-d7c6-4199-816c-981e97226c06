import {
	Order,
	OrderItem,
	OrderFile,
	OrderStatusHistory,
	OrderWithDetails,
	OrderSummary,
	OrderStats,
	OrderSearchRequest,
} from '@/types/order.types';
import {PaginationParams, PaginatedResponse} from '@/types/common.types';

export interface IOrderRepository {
	// Order operations
	createOrder(orderData: Partial<Order>): Promise<Order>;
	findOrderById(id: string): Promise<Order | null>;
	findOrderWithDetails(id: string): Promise<OrderWithDetails | null>;
	updateOrder(id: string, orderData: Partial<Order>): Promise<Order>;
	deleteOrder(id: string): Promise<void>;
	listOrders(params: PaginationParams): Promise<PaginatedResponse<Order>>;

	// Order items operations
	createOrderItem(
		orderId: string,
		itemData: Partial<OrderItem>
	): Promise<OrderItem>;
	findOrderItemById(id: string): Promise<OrderItem | null>;
	findOrderItemsByOrderId(orderId: string): Promise<OrderItem[]>;
	updateOrderItem(id: string, itemData: Partial<OrderItem>): Promise<OrderItem>;
	deleteOrderItem(id: string): Promise<void>;

	// Order files operations
	createOrderFile(
		orderId: string,
		fileData: Partial<OrderFile>
	): Promise<OrderFile>;
	findOrderFileById(id: string): Promise<OrderFile | null>;
	findOrderFilesByOrderId(orderId: string): Promise<OrderFile[]>;
	updateOrderFile(id: string, fileData: Partial<OrderFile>): Promise<OrderFile>;
	deleteOrderFile(id: string): Promise<void>;

	// Order status history operations
	createStatusHistory(
		orderId: string,
		statusData: Partial<OrderStatusHistory>
	): Promise<OrderStatusHistory>;
	findStatusHistoryByOrderId(orderId: string): Promise<OrderStatusHistory[]>;

	// Customer order operations
	findOrdersByCustomerId(
		customerId: string,
		params: PaginationParams
	): Promise<PaginatedResponse<OrderSummary>>;
	findOrderSummariesByCustomerId(
		customerId: string,
		params: PaginationParams
	): Promise<PaginatedResponse<OrderSummary>>;

	// Provider order operations
	findOrdersByProviderId(
		providerId: string,
		params: PaginationParams
	): Promise<PaginatedResponse<OrderSummary>>;
	findOrderSummariesByProviderId(
		providerId: string,
		params: PaginationParams
	): Promise<PaginatedResponse<OrderSummary>>;

	// Search and filtering operations
	searchOrders(
		searchParams: OrderSearchRequest,
		params: PaginationParams
	): Promise<PaginatedResponse<OrderSummary>>;
	findOrdersByStatus(
		status: string,
		params: PaginationParams
	): Promise<PaginatedResponse<OrderSummary>>;
	findOrdersByService(
		serviceId: string,
		params: PaginationParams
	): Promise<PaginatedResponse<OrderSummary>>;
	findOrdersByDateRange(
		startDate: Date,
		endDate: Date,
		params: PaginationParams
	): Promise<PaginatedResponse<OrderSummary>>;

	// Statistics operations
	getOrderStats(): Promise<OrderStats>;
	getOrderStatsByDateRange(startDate: Date, endDate: Date): Promise<OrderStats>;
	getCustomerOrderStats(customerId: string): Promise<OrderStats>;
	getProviderOrderStats(providerId: string): Promise<OrderStats>;

	// Status update operations
	updateOrderStatus(
		id: string,
		status: string,
		comment?: string
	): Promise<Order>;
	completeOrder(id: string): Promise<Order>;
	cancelOrder(id: string, reason?: string): Promise<Order>;
}
