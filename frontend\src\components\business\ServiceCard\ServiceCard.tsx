import React from 'react';
import { Link } from 'react-router-dom';
import { Service } from '../../../types';
import { Heart } from 'lucide-react';

interface ServiceCardProps {
  service: Service;
  onAddToCart?: (service: Service) => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ service, onAddToCart }) => {
  return (
    <Link to={`/service/${service.id}`} className="block">
      <div className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
        <div className="relative">
          <img
            src={service.image}
            alt={service.name}
            className="w-full aspect-square object-cover"
          />
          <button className="absolute top-3 right-3 p-2 bg-white rounded-full shadow-md hover:bg-gray-50 transition-colors">
            <Heart className="h-5 w-5 text-gray-600" />
          </button>
        </div>

        <div className="p-4">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            {service.name}
          </h3>
          <p className="text-gray-600 text-sm mb-3 line-clamp-2">
            {service.description}
          </p>

          <div className="text-gray-700">
            <span className="text-sm">25 starting at </span>
            <span className="font-semibold">${service.price}</span>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ServiceCard;
