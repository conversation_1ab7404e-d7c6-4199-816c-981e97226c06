"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderProfileModel = exports.ProviderRating = exports.VerificationInfo = exports.BusinessAddress = exports.BusinessInfo = void 0;
const BaseModel_1 = require("../base/BaseModel");
const validation_schemas_1 = require("../schemas/validation.schemas");
// Value Objects for ProviderProfile domain
class BusinessInfo extends BaseModel_1.ValueObject {
    constructor(businessName, description, website) {
        super();
        this.businessName = businessName;
        this.description = description;
        this.website = website;
        this.validate();
    }
    validate() {
        if (!this.businessName || this.businessName.trim().length < 2) {
            throw new Error('Business name must be at least 2 characters');
        }
        if (this.businessName.length > 200) {
            throw new Error('Business name cannot exceed 200 characters');
        }
        if (this.description && this.description.length > 1000) {
            throw new Error('Description cannot exceed 1000 characters');
        }
        if (this.website && !/^https?:\/\/.+/.test(this.website)) {
            throw new Error('Website must be a valid URL');
        }
    }
    getBusinessName() {
        return this.businessName;
    }
    getDescription() {
        return this.description;
    }
    getWebsite() {
        return this.website;
    }
    getDisplayName() {
        return this.businessName;
    }
    getEqualityComponents() {
        return [this.businessName, this.description, this.website];
    }
}
exports.BusinessInfo = BusinessInfo;
class BusinessAddress extends BaseModel_1.ValueObject {
    constructor(businessPhone, businessAddress, businessCity, businessState, businessZip) {
        super();
        this.businessPhone = businessPhone;
        this.businessAddress = businessAddress;
        this.businessCity = businessCity;
        this.businessState = businessState;
        this.businessZip = businessZip;
        this.validate();
    }
    validate() {
        if (this.businessPhone && !/^\+?[\d\s-()]+$/.test(this.businessPhone)) {
            throw new Error('Invalid business phone format');
        }
    }
    getBusinessPhone() {
        return this.businessPhone;
    }
    getBusinessAddress() {
        return this.businessAddress;
    }
    getBusinessCity() {
        return this.businessCity;
    }
    getBusinessState() {
        return this.businessState;
    }
    getBusinessZip() {
        return this.businessZip;
    }
    getFullAddress() {
        const parts = [
            this.businessAddress,
            this.businessCity,
            this.businessState,
            this.businessZip
        ].filter(Boolean);
        return parts.join(', ');
    }
    isComplete() {
        return !!(this.businessAddress && this.businessCity && this.businessState);
    }
    getEqualityComponents() {
        return [
            this.businessPhone,
            this.businessAddress,
            this.businessCity,
            this.businessState,
            this.businessZip
        ];
    }
}
exports.BusinessAddress = BusinessAddress;
class VerificationInfo extends BaseModel_1.ValueObject {
    constructor(licenseNumber, insuranceNumber, isVerified = false, verifiedAt) {
        super();
        this.licenseNumber = licenseNumber;
        this.insuranceNumber = insuranceNumber;
        this.isVerified = isVerified;
        this.verifiedAt = verifiedAt;
        this.validate();
    }
    validate() {
        if (this.isVerified && !this.verifiedAt) {
            throw new Error('Verified providers must have a verification date');
        }
        if (!this.isVerified && this.verifiedAt) {
            throw new Error('Unverified providers cannot have a verification date');
        }
    }
    getLicenseNumber() {
        return this.licenseNumber;
    }
    getInsuranceNumber() {
        return this.insuranceNumber;
    }
    isVerified() {
        return this.isVerified;
    }
    getVerifiedAt() {
        return this.verifiedAt;
    }
    getVerificationStatus() {
        if (this.isVerified)
            return 'verified';
        if (this.licenseNumber || this.insuranceNumber)
            return 'pending';
        return 'unverified';
    }
    hasDocumentation() {
        return !!(this.licenseNumber || this.insuranceNumber);
    }
    getEqualityComponents() {
        return [this.licenseNumber, this.insuranceNumber, this.isVerified, this.verifiedAt];
    }
}
exports.VerificationInfo = VerificationInfo;
class ProviderRating extends BaseModel_1.ValueObject {
    constructor(rating, totalReviews, totalOrders) {
        super();
        this.rating = rating;
        this.totalReviews = totalReviews;
        this.totalOrders = totalOrders;
        this.validate();
    }
    validate() {
        if (this.rating < 0 || this.rating > 5) {
            throw new Error('Rating must be between 0 and 5');
        }
        if (this.totalReviews < 0) {
            throw new Error('Total reviews cannot be negative');
        }
        if (this.totalOrders < 0) {
            throw new Error('Total orders cannot be negative');
        }
    }
    getRating() {
        return this.rating;
    }
    getTotalReviews() {
        return this.totalReviews;
    }
    getTotalOrders() {
        return this.totalOrders;
    }
    getDisplayRating() {
        if (this.totalReviews === 0) {
            return 'No reviews yet';
        }
        return `${this.rating.toFixed(1)} (${this.totalReviews} review${this.totalReviews !== 1 ? 's' : ''})`;
    }
    getReputationLevel() {
        if (this.totalOrders < 5)
            return 'new';
        if (this.totalOrders < 25)
            return 'established';
        if (this.totalOrders < 100)
            return 'experienced';
        return 'expert';
    }
    isHighlyRated() {
        return this.rating >= 4.5 && this.totalReviews >= 10;
    }
    getCompletionRate() {
        // Simplified calculation - in reality would need cancelled orders data
        if (this.totalOrders === 0)
            return 0;
        return Math.min(95 + (this.rating * 1), 100);
    }
    getEqualityComponents() {
        return [this.rating, this.totalReviews, this.totalOrders];
    }
}
exports.ProviderRating = ProviderRating;
// ProviderProfile Domain Model
class ProviderProfileModel extends BaseModel_1.BaseModel {
    constructor(data) {
        super(data);
        this._validationSchema = validation_schemas_1.providerProfileSchema;
        this.businessInfo = new BusinessInfo(data.businessName, data.description || undefined, data.website || undefined);
        this.businessAddress = new BusinessAddress(data.businessPhone || undefined, data.businessAddress || undefined, data.businessCity || undefined, data.businessState || undefined, data.businessZip || undefined);
        this.verificationInfo = new VerificationInfo(data.licenseNumber || undefined, data.insuranceNumber || undefined, data.isVerified, data.verifiedAt || undefined);
        this.rating = new ProviderRating(data.rating || 0, data.totalReviews, data.totalOrders);
    }
    // Business Logic Methods
    getUserId() {
        return this._data.userId;
    }
    getBusinessInfo() {
        return this.businessInfo;
    }
    getBusinessAddress() {
        return this.businessAddress;
    }
    getVerificationInfo() {
        return this.verificationInfo;
    }
    getRating() {
        return this.rating;
    }
    isActive() {
        return this._data.isActive;
    }
    isVerified() {
        return this.verificationInfo.isVerified();
    }
    getProfileCompleteness() {
        const requiredFields = [
            { field: 'businessName', value: this.businessInfo.getBusinessName() },
            { field: 'description', value: this.businessInfo.getDescription() },
            { field: 'businessPhone', value: this.businessAddress.getBusinessPhone() },
            { field: 'businessAddress', value: this.businessAddress.getBusinessAddress() },
            { field: 'businessCity', value: this.businessAddress.getBusinessCity() },
            { field: 'businessState', value: this.businessAddress.getBusinessState() },
        ];
        const completedFields = requiredFields.filter(field => field.value).length;
        const percentage = Math.round((completedFields / requiredFields.length) * 100);
        const missingFields = requiredFields
            .filter(field => !field.value)
            .map(field => field.field);
        return { percentage, missingFields };
    }
    canAcceptOrders() {
        return this._data.isActive &&
            this.getProfileCompleteness().percentage >= 80 &&
            this.businessInfo.getBusinessName().trim().length > 0;
    }
    getBusinessHours() {
        // Placeholder - would typically store business hours in the database
        return 'Mon-Fri 9AM-5PM';
    }
    // Business Actions
    updateBusinessInfo(updates) {
        try {
            const newBusinessInfo = new BusinessInfo(updates.businessName ?? this.businessInfo.getBusinessName(), updates.description ?? this.businessInfo.getDescription(), updates.website ?? this.businessInfo.getWebsite());
            this.businessInfo = newBusinessInfo;
            this.updateData({
                ...this._data,
                businessName: newBusinessInfo.getBusinessName(),
                description: newBusinessInfo.getDescription() || null,
                website: newBusinessInfo.getWebsite() || null,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ProviderBusinessInfoUpdated',
                aggregateId: this.id,
                data: {
                    providerId: this.id,
                    userId: this._data.userId,
                    changes: updates
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update business info');
        }
    }
    updateBusinessAddress(updates) {
        try {
            const newBusinessAddress = new BusinessAddress(updates.businessPhone ?? this.businessAddress.getBusinessPhone(), updates.businessAddress ?? this.businessAddress.getBusinessAddress(), updates.businessCity ?? this.businessAddress.getBusinessCity(), updates.businessState ?? this.businessAddress.getBusinessState(), updates.businessZip ?? this.businessAddress.getBusinessZip());
            this.businessAddress = newBusinessAddress;
            this.updateData({
                ...this._data,
                businessPhone: newBusinessAddress.getBusinessPhone() || null,
                businessAddress: newBusinessAddress.getBusinessAddress() || null,
                businessCity: newBusinessAddress.getBusinessCity() || null,
                businessState: newBusinessAddress.getBusinessState() || null,
                businessZip: newBusinessAddress.getBusinessZip() || null,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ProviderAddressUpdated',
                aggregateId: this.id,
                data: {
                    providerId: this.id,
                    userId: this._data.userId,
                    changes: updates
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update business address');
        }
    }
    verify(verifiedBy, licenseNumber, insuranceNumber) {
        if (this.verificationInfo.isVerified()) {
            return BaseModel_1.Result.failure('Provider is already verified');
        }
        try {
            const newVerificationInfo = new VerificationInfo(licenseNumber ?? this.verificationInfo.getLicenseNumber(), insuranceNumber ?? this.verificationInfo.getInsuranceNumber(), true, new Date());
            this.verificationInfo = newVerificationInfo;
            this.updateData({
                ...this._data,
                licenseNumber: newVerificationInfo.getLicenseNumber() || null,
                insuranceNumber: newVerificationInfo.getInsuranceNumber() || null,
                isVerified: true,
                verifiedAt: new Date(),
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ProviderVerified',
                aggregateId: this.id,
                data: {
                    providerId: this.id,
                    userId: this._data.userId,
                    verifiedBy,
                    verifiedAt: new Date()
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to verify provider');
        }
    }
    revokeVerification(reason, revokedBy) {
        if (!this.verificationInfo.isVerified()) {
            return BaseModel_1.Result.failure('Provider is not verified');
        }
        try {
            const newVerificationInfo = new VerificationInfo(this.verificationInfo.getLicenseNumber(), this.verificationInfo.getInsuranceNumber(), false, undefined);
            this.verificationInfo = newVerificationInfo;
            this.updateData({
                ...this._data,
                isVerified: false,
                verifiedAt: null,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ProviderVerificationRevoked',
                aggregateId: this.id,
                data: {
                    providerId: this.id,
                    userId: this._data.userId,
                    reason,
                    revokedBy,
                    revokedAt: new Date()
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to revoke verification');
        }
    }
    activate() {
        if (this._data.isActive) {
            return BaseModel_1.Result.failure('Provider profile is already active');
        }
        const completeness = this.getProfileCompleteness();
        if (completeness.percentage < 60) {
            return BaseModel_1.Result.failure(`Profile must be at least 60% complete to activate. Missing: ${completeness.missingFields.join(', ')}`);
        }
        this.updateData({
            ...this._data,
            isActive: true,
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'ProviderActivated',
            aggregateId: this.id,
            data: {
                providerId: this.id,
                userId: this._data.userId,
                activatedAt: new Date()
            },
            timestamp: new Date(),
            version: 1,
        });
        return BaseModel_1.Result.success(undefined);
    }
    deactivate(reason) {
        if (!this._data.isActive) {
            return BaseModel_1.Result.failure('Provider profile is already deactivated');
        }
        this.updateData({
            ...this._data,
            isActive: false,
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'ProviderDeactivated',
            aggregateId: this.id,
            data: {
                providerId: this.id,
                userId: this._data.userId,
                reason,
                deactivatedAt: new Date()
            },
            timestamp: new Date(),
            version: 1,
        });
        return BaseModel_1.Result.success(undefined);
    }
    updateRating(newRating, totalReviews, totalOrders) {
        try {
            const rating = new ProviderRating(newRating, totalReviews, totalOrders);
            this.rating = rating;
            this.updateData({
                ...this._data,
                rating: newRating,
                totalReviews: totalReviews,
                totalOrders: totalOrders,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'ProviderRatingUpdated',
                aggregateId: this.id,
                data: {
                    providerId: this.id,
                    userId: this._data.userId,
                    rating: newRating,
                    totalReviews,
                    totalOrders
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update rating');
        }
    }
    // Business Rule Validation
    validateBusinessRules() {
        const errors = [];
        // Basic validation
        if (!this.businessInfo.getBusinessName()) {
            errors.push('Business name is required');
        }
        // Verification rules
        if (this.verificationInfo.isVerified() && !this.verificationInfo.hasDocumentation()) {
            errors.push('Verified providers must have license or insurance documentation');
        }
        // Active profile requirements
        if (this._data.isActive) {
            const completeness = this.getProfileCompleteness();
            if (completeness.percentage < 60) {
                errors.push('Active profiles must be at least 60% complete');
            }
        }
        return errors;
    }
    // Static Factory Methods
    static createProviderProfile(providerData) {
        try {
            // Validate business info
            const businessInfo = new BusinessInfo(providerData.businessName, providerData.description, providerData.website);
            // Validate business address
            const businessAddress = new BusinessAddress(providerData.businessPhone, providerData.businessAddress, providerData.businessCity, providerData.businessState, providerData.businessZip);
            // Validate verification info
            const verificationInfo = new VerificationInfo(providerData.licenseNumber, providerData.insuranceNumber, false, undefined);
            const now = new Date();
            const providerProfile = new ProviderProfileModel({
                id: '', // Will be set by the database
                userId: providerData.userId,
                businessName: providerData.businessName,
                description: providerData.description || null,
                website: providerData.website || null,
                businessPhone: providerData.businessPhone || null,
                businessAddress: providerData.businessAddress || null,
                businessCity: providerData.businessCity || null,
                businessState: providerData.businessState || null,
                businessZip: providerData.businessZip || null,
                licenseNumber: providerData.licenseNumber || null,
                insuranceNumber: providerData.insuranceNumber || null,
                isVerified: false,
                verifiedAt: null,
                rating: null,
                totalReviews: 0,
                totalOrders: 0,
                isActive: true,
                createdAt: now,
                updatedAt: now,
            });
            const validation = providerProfile.validate();
            if (!validation.isValid) {
                return BaseModel_1.Result.failure(`Validation failed: ${validation.errors.join(', ')}`);
            }
            return BaseModel_1.Result.success(providerProfile);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to create provider profile');
        }
    }
    static fromPrismaProviderProfile(prismaProviderProfile) {
        return new ProviderProfileModel(prismaProviderProfile);
    }
    // Serialization methods for API responses
    toPublicObject() {
        return {
            ...this._data,
            profileCompleteness: this.getProfileCompleteness(),
            canAcceptOrders: this.canAcceptOrders(),
            reputationLevel: this.rating.getReputationLevel(),
            verificationStatus: this.verificationInfo.getVerificationStatus(),
        };
    }
    toSearchResult() {
        return {
            id: this._data.id,
            businessName: this.businessInfo.getBusinessName(),
            description: this.businessInfo.getDescription(),
            rating: this.rating.getRating(),
            totalReviews: this.rating.getTotalReviews(),
            totalOrders: this.rating.getTotalOrders(),
            isVerified: this.verificationInfo.isVerified(),
            location: this.businessAddress.getFullAddress() || 'Location not specified',
            reputationLevel: this.rating.getReputationLevel(),
        };
    }
}
exports.ProviderProfileModel = ProviderProfileModel;
//# sourceMappingURL=ProviderProfile.model.js.map