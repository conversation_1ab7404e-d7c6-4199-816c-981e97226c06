// Service Category Repository - Following Repository Pattern and SOLID principles
import { PrismaClient, ServiceCategory } from '@prisma/client';
import { BaseRepository } from '@/repositories/base/base.repository';
import { CreateCategoryData, UpdateCategoryData } from '@/types/services';

// Category-specific repository interface (ISP)
export interface ICategoryRepository {
  findById(id: string): Promise<ServiceCategory | null>;
  findByName(name: string): Promise<ServiceCategory | null>;
  findAll(options?: {
    skip?: number;
    take?: number;
    orderBy?: { [key: string]: 'asc' | 'desc' };
    includeInactive?: boolean;
  }): Promise<ServiceCategory[]>;
  findActive(): Promise<ServiceCategory[]>;
  create(categoryData: CreateCategoryData): Promise<ServiceCategory>;
  update(id: string, categoryData: UpdateCategoryData): Promise<ServiceCategory>;
  activate(id: string): Promise<ServiceCategory>;
  deactivate(id: string): Promise<ServiceCategory>;
  reorderCategories(categoryOrders: { id: string; sortOrder: number }[]): Promise<void>;
  getCategoryWithServiceCount(): Promise<CategoryWithServiceCount[]>;
  getMostPopularCategories(limit?: number): Promise<CategoryWithServiceCount[]>;
}

export interface CategoryWithServiceCount extends ServiceCategory {
  _count: {
    services: number;
  };
}

// Category repository implementation (SRP)
export class CategoryRepository extends BaseRepository<ServiceCategory> implements ICategoryRepository {
  constructor(prisma: PrismaClient) {
    super(prisma, 'serviceCategory');
  }

  async findByName(name: string): Promise<ServiceCategory | null> {
    try {
      return await this.prisma.serviceCategory.findUnique({
        where: { name },
      });
    } catch (error) {
      throw new Error(`Failed to find category by name: ${error}`);
    }
  }

  async findActive(): Promise<ServiceCategory[]> {
    try {
      return await this.prisma.serviceCategory.findMany({
        where: { isActive: true },
        orderBy: { sortOrder: 'asc' },
      });
    } catch (error) {
      throw new Error(`Failed to find active categories: ${error}`);
    }
  }

  async create(categoryData: CreateCategoryData): Promise<ServiceCategory> {
    try {
      // Get the highest sort order and increment by 1
      const lastCategory = await this.prisma.serviceCategory.findFirst({
        orderBy: { sortOrder: 'desc' },
        select: { sortOrder: true },
      });

      const sortOrder = (lastCategory?.sortOrder || 0) + 1;

      return await this.prisma.serviceCategory.create({
        data: {
          ...categoryData,
          sortOrder,
        },
      });
    } catch (error) {
      throw new Error(`Failed to create category: ${error}`);
    }
  }

  async update(id: string, categoryData: UpdateCategoryData): Promise<ServiceCategory> {
    try {
      return await this.prisma.serviceCategory.update({
        where: { id },
        data: categoryData,
      });
    } catch (error) {
      throw new Error(`Failed to update category: ${error}`);
    }
  }

  async activate(id: string): Promise<ServiceCategory> {
    try {
      return await this.prisma.serviceCategory.update({
        where: { id },
        data: { isActive: true },
      });
    } catch (error) {
      throw new Error(`Failed to activate category: ${error}`);
    }
  }

  async deactivate(id: string): Promise<ServiceCategory> {
    try {
      return await this.prisma.serviceCategory.update({
        where: { id },
        data: { isActive: false },
      });
    } catch (error) {
      throw new Error(`Failed to deactivate category: ${error}`);
    }
  }

  async reorderCategories(categoryOrders: { id: string; sortOrder: number }[]): Promise<void> {
    try {
      await this.prisma.$transaction(
        categoryOrders.map(({ id, sortOrder }) =>
          this.prisma.serviceCategory.update({
            where: { id },
            data: { sortOrder },
          })
        )
      );
    } catch (error) {
      throw new Error(`Failed to reorder categories: ${error}`);
    }
  }

  async getCategoryWithServiceCount(): Promise<CategoryWithServiceCount[]> {
    try {
      return await this.prisma.serviceCategory.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: {
              services: {
                where: { isActive: true },
              },
            },
          },
        },
        orderBy: { sortOrder: 'asc' },
      }) as CategoryWithServiceCount[];
    } catch (error) {
      throw new Error(`Failed to get categories with service count: ${error}`);
    }
  }

  async getMostPopularCategories(limit: number = 10): Promise<CategoryWithServiceCount[]> {
    try {
      return await this.prisma.serviceCategory.findMany({
        where: { isActive: true },
        include: {
          _count: {
            select: {
              services: {
                where: { isActive: true },
              },
            },
          },
        },
        orderBy: {
          services: {
            _count: 'desc',
          },
        },
        take: limit,
      }) as CategoryWithServiceCount[];
    } catch (error) {
      throw new Error(`Failed to get most popular categories: ${error}`);
    }
  }
}