// Category Management Service - Following SOLID principles
import { ServiceCategory } from '@prisma/client';
import { ICategoryRepository, CategoryWithServiceCount } from '@/repositories/services';
import { BusinessLogicResult } from '@/types/common';
import { CreateCategoryData, UpdateCategoryData } from '@/types/services';

// Business validation errors
export class CategoryValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'CategoryValidationError';
  }
}

// Category Management Service (SRP - handles category business logic)
export class CategoryManagementService {
  constructor(private categoryRepository: ICategoryRepository) {}

  // Get all categories with service counts (SRP)
  async getAllCategories(includeInactive = false): Promise<BusinessLogicResult<CategoryWithServiceCount[]>> {
    try {
      let categories: CategoryWithServiceCount[];
      
      if (includeInactive) {
        // Get all categories for admin view
        categories = await this.categoryRepository.getCategoryWithServiceCount();
      } else {
        // Get only active categories for public view
        categories = await this.categoryRepository.getCategoryWithServiceCount();
        categories = categories.filter(cat => cat.isActive);
      }

      return {
        success: true,
        data: categories,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get categories',
      };
    }
  }

  // Get category by ID (SRP)
  async getCategoryById(id: string): Promise<BusinessLogicResult<ServiceCategory>> {
    try {
      const category = await this.categoryRepository.findById(id);
      
      if (!category) {
        return {
          success: false,
          error: 'Category not found',
        };
      }

      return {
        success: true,
        data: category,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get category',
      };
    }
  }

  // Create new category (SRP)
  async createCategory(
    categoryData: CreateCategoryData,
    userRole: string
  ): Promise<BusinessLogicResult<ServiceCategory>> {
    try {
      // Only admins can create categories
      if (userRole !== 'ADMIN') {
        return {
          success: false,
          error: 'Only administrators can create categories',
        };
      }

      // Validate category data
      await this.validateCategoryData(categoryData);

      // Check if category name already exists
      const existingCategory = await this.categoryRepository.findByName(categoryData.name);
      if (existingCategory) {
        return {
          success: false,
          error: 'Category name already exists',
          validationErrors: { name: 'A category with this name already exists' }
        };
      }

      // Create category
      const category = await this.categoryRepository.create(categoryData);

      return {
        success: true,
        data: category,
      };
    } catch (error) {
      if (error instanceof CategoryValidationError) {
        return {
          success: false,
          error: error.message,
          validationErrors: { [error.field || 'general']: error.message }
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create category',
      };
    }
  }

  // Update category (SRP)
  async updateCategory(
    id: string,
    updateData: UpdateCategoryData,
    userRole: string
  ): Promise<BusinessLogicResult<ServiceCategory>> {
    try {
      // Only admins can update categories
      if (userRole !== 'ADMIN') {
        return {
          success: false,
          error: 'Only administrators can update categories',
        };
      }

      // Get existing category
      const existingCategory = await this.categoryRepository.findById(id);
      if (!existingCategory) {
        return {
          success: false,
          error: 'Category not found',
        };
      }

      // Validate update data
      if (updateData.name) {
        await this.validateCategoryName(updateData.name);
        
        // Check if new name conflicts with existing category
        const categoryWithSameName = await this.categoryRepository.findByName(updateData.name);
        if (categoryWithSameName && categoryWithSameName.id !== id) {
          return {
            success: false,
            error: 'Category name already exists',
            validationErrors: { name: 'A category with this name already exists' }
          };
        }
      }

      // Update category
      const updatedCategory = await this.categoryRepository.update(id, updateData);

      return {
        success: true,
        data: updatedCategory,
      };
    } catch (error) {
      if (error instanceof CategoryValidationError) {
        return {
          success: false,
          error: error.message,
          validationErrors: { [error.field || 'general']: error.message }
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update category',
      };
    }
  }

  // Delete category (SRP)
  async deleteCategory(
    id: string,
    userRole: string
  ): Promise<BusinessLogicResult<void>> {
    try {
      // Only admins can delete categories
      if (userRole !== 'ADMIN') {
        return {
          success: false,
          error: 'Only administrators can delete categories',
        };
      }

      // Get existing category
      const existingCategory = await this.categoryRepository.findById(id);
      if (!existingCategory) {
        return {
          success: false,
          error: 'Category not found',
        };
      }

      // Check if category has active services
      const categoryWithCount = await this.categoryRepository.getCategoryWithServiceCount();
      const categoryData = categoryWithCount.find(cat => cat.id === id);
      
      if (categoryData && categoryData._count.services > 0) {
        return {
          success: false,
          error: 'Cannot delete category with active services',
        };
      }

      // Delete category
      await this.categoryRepository.delete(id);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete category',
      };
    }
  }

  // Activate category (SRP)
  async activateCategory(
    id: string,
    userRole: string
  ): Promise<BusinessLogicResult<ServiceCategory>> {
    try {
      // Only admins can activate categories
      if (userRole !== 'ADMIN') {
        return {
          success: false,
          error: 'Only administrators can activate categories',
        };
      }

      const category = await this.categoryRepository.findById(id);
      if (!category) {
        return {
          success: false,
          error: 'Category not found',
        };
      }

      const activatedCategory = await this.categoryRepository.activate(id);

      return {
        success: true,
        data: activatedCategory,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to activate category',
      };
    }
  }

  // Deactivate category (SRP)
  async deactivateCategory(
    id: string,
    userRole: string
  ): Promise<BusinessLogicResult<ServiceCategory>> {
    try {
      // Only admins can deactivate categories
      if (userRole !== 'ADMIN') {
        return {
          success: false,
          error: 'Only administrators can deactivate categories',
        };
      }

      const category = await this.categoryRepository.findById(id);
      if (!category) {
        return {
          success: false,
          error: 'Category not found',
        };
      }

      const deactivatedCategory = await this.categoryRepository.deactivate(id);

      return {
        success: true,
        data: deactivatedCategory,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to deactivate category',
      };
    }
  }

  // Reorder categories (SRP)
  async reorderCategories(
    categoryOrders: { id: string; sortOrder: number }[],
    userRole: string
  ): Promise<BusinessLogicResult<void>> {
    try {
      // Only admins can reorder categories
      if (userRole !== 'ADMIN') {
        return {
          success: false,
          error: 'Only administrators can reorder categories',
        };
      }

      // Validate all category IDs exist
      for (const order of categoryOrders) {
        const category = await this.categoryRepository.findById(order.id);
        if (!category) {
          return {
            success: false,
            error: `Category with ID ${order.id} not found`,
          };
        }
      }

      // Reorder categories
      await this.categoryRepository.reorderCategories(categoryOrders);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to reorder categories',
      };
    }
  }

  // Get most popular categories (SRP)
  async getMostPopularCategories(limit = 10): Promise<BusinessLogicResult<CategoryWithServiceCount[]>> {
    try {
      const categories = await this.categoryRepository.getMostPopularCategories(limit);

      return {
        success: true,
        data: categories,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get popular categories',
      };
    }
  }

  // Private helper methods

  private async validateCategoryData(categoryData: CreateCategoryData): Promise<void> {
    await this.validateCategoryName(categoryData.name);
    
    if (categoryData.description && categoryData.description.length > 1000) {
      throw new CategoryValidationError('Description cannot exceed 1000 characters', 'description');
    }
  }

  private async validateCategoryName(name: string): Promise<void> {
    if (!name || name.trim().length < 2) {
      throw new CategoryValidationError('Category name must be at least 2 characters', 'name');
    }

    if (name.length > 100) {
      throw new CategoryValidationError('Category name cannot exceed 100 characters', 'name');
    }

    // Check for valid characters (letters, numbers, spaces, hyphens)
    if (!/^[a-zA-Z0-9\s\-]+$/.test(name)) {
      throw new CategoryValidationError('Category name can only contain letters, numbers, spaces, and hyphens', 'name');
    }
  }
}