// Service Management Service - Following SOLID principles and business logic separation
import { Service, ServiceCategory } from '@prisma/client';
import { 
  IServiceRepository, 
  ICategoryRepository, 
  ServiceWithDetails,
  ServiceQueryOptions,
  ServiceStats 
} from '@/repositories/services';
import { BusinessLogicResult, PaginatedResult } from '@/types/common';
import { 
  CreateServiceData, 
  UpdateServiceData, 
  ServiceSearchRequest,
  ServiceFilters 
} from '@/types/services';

// Abstract interfaces for dependencies (DIP)
export interface IFileUploadService {
  uploadImage(file: Express.Multer.File, folder: string): Promise<string>;
  uploadMultipleImages(files: Express.Multer.File[], folder: string): Promise<string[]>;
  deleteImage(url: string): Promise<void>;
  deleteMultipleImages(urls: string[]): Promise<void>;
}

export interface INotificationService {
  notifyServiceCreated(serviceId: string, providerId: string): Promise<void>;
  notifyServiceUpdated(serviceId: string, providerId: string): Promise<void>;
  notifyServiceDeleted(serviceId: string, providerId: string): Promise<void>;
}

// Business validation errors
export class ServiceValidationError extends Error {
  constructor(message: string, public field?: string) {
    super(message);
    this.name = 'ServiceValidationError';
  }
}

export class ServiceAuthorizationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'ServiceAuthorizationError';
  }
}

// Main Service Management Service (SRP - handles service business logic)
export class ServiceManagementService {
  constructor(
    private serviceRepository: IServiceRepository,
    private categoryRepository: ICategoryRepository,
    private fileUploadService: IFileUploadService,
    private notificationService: INotificationService
  ) {}

  // Create a new service with business validation (SRP)
  async createService(
    serviceData: CreateServiceData,
    userId: string,
    providerId: string,
    imageFiles?: Express.Multer.File[]
  ): Promise<BusinessLogicResult<Service>> {
    try {
      // Business validation
      await this.validateServiceData(serviceData);
      
      // Validate category exists
      const category = await this.categoryRepository.findById(serviceData.categoryId);
      if (!category) {
        return {
          success: false,
          error: 'Invalid category selected',
          validationErrors: { categoryId: 'Category does not exist' }
        };
      }

      if (!category.isActive) {
        return {
          success: false,
          error: 'Selected category is not active',
          validationErrors: { categoryId: 'Category is not available' }
        };
      }

      // Upload images if provided
      let imageUrls: string[] = [];
      if (imageFiles && imageFiles.length > 0) {
        try {
          imageUrls = await this.fileUploadService.uploadMultipleImages(
            imageFiles,
            `services/${providerId}`
          );
        } catch (uploadError) {
          return {
            success: false,
            error: 'Failed to upload service images',
          };
        }
      }

      // Prepare service data with business rules
      const completeServiceData: CreateServiceData = {
        ...serviceData,
        userId,
        providerId,
        images: imageUrls,
        isActive: true,
        isFeatured: false, // Only admins can set featured
        rating: 0,
        totalReviews: 0,
        totalOrders: 0,
      };

      // Create service
      const service = await this.serviceRepository.create(completeServiceData);

      // Send notifications
      await this.notificationService.notifyServiceCreated(service.id, providerId);

      return {
        success: true,
        data: service,
      };
    } catch (error) {
      if (error instanceof ServiceValidationError) {
        return {
          success: false,
          error: error.message,
          validationErrors: { [error.field || 'general']: error.message }
        };
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create service',
      };
    }
  }

  // Get service with full details (SRP)
  async getServiceById(id: string, includeInactive = false): Promise<BusinessLogicResult<ServiceWithDetails>> {
    try {
      const service = await this.serviceRepository.findByIdWithDetails(id);
      
      if (!service) {
        return {
          success: false,
          error: 'Service not found',
        };
      }

      // Business rule: Don't return inactive services unless explicitly requested
      if (!service.isActive && !includeInactive) {
        return {
          success: false,
          error: 'Service not available',
        };
      }

      return {
        success: true,
        data: service,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get service',
      };
    }
  }

  // Update service with authorization checks (SRP)
  async updateService(
    id: string,
    updateData: UpdateServiceData,
    userId: string,
    userRole: string,
    imageFiles?: Express.Multer.File[]
  ): Promise<BusinessLogicResult<Service>> {
    try {
      // Get existing service
      const existingService = await this.serviceRepository.findById(id);
      if (!existingService) {
        return {
          success: false,
          error: 'Service not found',
        };
      }

      // Authorization check
      if (!this.canUserModifyService(existingService, userId, userRole)) {
        return {
          success: false,
          error: 'You are not authorized to modify this service',
        };
      }

      // Validate update data
      if (updateData.categoryId) {
        const category = await this.categoryRepository.findById(updateData.categoryId);
        if (!category || !category.isActive) {
          return {
            success: false,
            error: 'Invalid category selected',
            validationErrors: { categoryId: 'Category does not exist or is not active' }
          };
        }
      }

      // Handle image uploads
      let imageUrls = existingService.images;
      if (imageFiles && imageFiles.length > 0) {
        try {
          const newImageUrls = await this.fileUploadService.uploadMultipleImages(
            imageFiles,
            `services/${existingService.providerId}`
          );
          imageUrls = [...imageUrls, ...newImageUrls];
        } catch (uploadError) {
          return {
            success: false,
            error: 'Failed to upload new images',
          };
        }
      }

      // Prepare final update data
      const finalUpdateData: UpdateServiceData = {
        ...updateData,
        images: imageUrls,
      };

      // Only admins can modify featured status
      if (updateData.isFeatured !== undefined && userRole !== 'ADMIN') {
        delete finalUpdateData.isFeatured;
      }

      // Update service
      const updatedService = await this.serviceRepository.update(id, finalUpdateData);

      // Send notifications
      await this.notificationService.notifyServiceUpdated(updatedService.id, existingService.providerId);

      return {
        success: true,
        data: updatedService,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update service',
      };
    }
  }

  // Delete service with cleanup (SRP)
  async deleteService(
    id: string,
    userId: string,
    userRole: string
  ): Promise<BusinessLogicResult<void>> {
    try {
      // Get existing service
      const existingService = await this.serviceRepository.findById(id);
      if (!existingService) {
        return {
          success: false,
          error: 'Service not found',
        };
      }

      // Authorization check
      if (!this.canUserModifyService(existingService, userId, userRole)) {
        return {
          success: false,
          error: 'You are not authorized to delete this service',
        };
      }

      // Business rule: Check if service has active orders
      // This would require order repository integration
      // For now, we'll proceed with deletion

      // Clean up images
      if (existingService.images.length > 0) {
        try {
          await this.fileUploadService.deleteMultipleImages(existingService.images);
        } catch (cleanupError) {
          // Log error but don't fail the deletion
          console.warn('Failed to clean up service images:', cleanupError);
        }
      }

      // Delete service
      await this.serviceRepository.delete(id);

      // Send notifications
      await this.notificationService.notifyServiceDeleted(id, existingService.providerId);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete service',
      };
    }
  }

  // Search services with filters (SRP)
  async searchServices(
    query?: string,
    filters?: ServiceFilters,
    pagination?: { page: number; limit: number }
  ): Promise<BusinessLogicResult<PaginatedResult<Service>>> {
    try {
      const { page = 1, limit = 20 } = pagination || {};
      const skip = (page - 1) * limit;

      const options: ServiceQueryOptions = {
        skip,
        take: limit,
        where: {
          isActive: true,
          ...(query && {
            OR: [
              { name: { contains: query, mode: 'insensitive' } },
              { description: { contains: query, mode: 'insensitive' } },
              { tags: { has: query } },
            ],
          }),
          ...(filters?.categoryId && { categoryId: filters.categoryId }),
          ...(filters?.providerId && { providerId: filters.providerId }),
          ...(filters?.priceType && { priceType: filters.priceType }),
          ...(filters?.minPrice && { price: { gte: filters.minPrice } }),
          ...(filters?.maxPrice && { price: { lte: filters.maxPrice } }),
          ...(filters?.tags && { tags: { hasEvery: filters.tags } }),
        },
        orderBy: this.buildOrderBy(filters?.sortBy, filters?.sortOrder),
        include: {
          category: true,
          provider: {
            select: {
              businessName: true,
              rating: true,
              isVerified: true,
            },
          },
        },
      };

      const [services, totalCount] = await Promise.all([
        this.serviceRepository.findAll(options),
        this.serviceRepository.count(options.where),
      ]);

      const totalPages = Math.ceil(totalCount / limit);

      return {
        success: true,
        data: {
          data: services,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search services',
      };
    }
  }

  // Get services by category (SRP)
  async getServicesByCategory(
    categoryId: string,
    pagination?: { page: number; limit: number }
  ): Promise<BusinessLogicResult<PaginatedResult<Service>>> {
    try {
      const { page = 1, limit = 20 } = pagination || {};
      
      const services = await this.serviceRepository.findByCategory(categoryId, {
        skip: (page - 1) * limit,
        take: limit,
        include: {
          provider: {
            select: {
              businessName: true,
              rating: true,
              isVerified: true,
            },
          },
        },
      });

      const totalCount = await this.serviceRepository.count({
        categoryId,
        isActive: true,
      });

      const totalPages = Math.ceil(totalCount / limit);

      return {
        success: true,
        data: {
          data: services,
          pagination: {
            page,
            limit,
            total: totalCount,
            totalPages,
            hasNext: page < totalPages,
            hasPrev: page > 1,
          },
        },
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get services by category',
      };
    }
  }

  // Get featured services (SRP)
  async getFeaturedServices(limit = 10): Promise<BusinessLogicResult<Service[]>> {
    try {
      const services = await this.serviceRepository.findFeatured({
        take: limit,
        include: {
          category: true,
          provider: {
            select: {
              businessName: true,
              rating: true,
              isVerified: true,
            },
          },
        },
      });

      return {
        success: true,
        data: services,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get featured services',
      };
    }
  }

  // Get service statistics (SRP)
  async getServiceStats(): Promise<BusinessLogicResult<ServiceStats>> {
    try {
      const stats = await this.serviceRepository.getServiceStats();

      return {
        success: true,
        data: stats,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get service statistics',
      };
    }
  }

  // Set service featured status (Admin only) (SRP)
  async setServiceFeatured(
    id: string,
    featured: boolean,
    userId: string,
    userRole: string
  ): Promise<BusinessLogicResult<Service>> {
    try {
      // Only admins can set featured status
      if (userRole !== 'ADMIN') {
        return {
          success: false,
          error: 'Only administrators can set featured status',
        };
      }

      const service = await this.serviceRepository.findById(id);
      if (!service) {
        return {
          success: false,
          error: 'Service not found',
        };
      }

      const updatedService = await this.serviceRepository.setFeatured(id, featured);

      return {
        success: true,
        data: updatedService,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update featured status',
      };
    }
  }

  // Private helper methods

  private async validateServiceData(serviceData: CreateServiceData): Promise<void> {
    if (!serviceData.name || serviceData.name.trim().length < 3) {
      throw new ServiceValidationError('Service name must be at least 3 characters', 'name');
    }

    if (!serviceData.description || serviceData.description.trim().length < 10) {
      throw new ServiceValidationError('Service description must be at least 10 characters', 'description');
    }

    if (serviceData.price < 0) {
      throw new ServiceValidationError('Price cannot be negative', 'price');
    }

    if (serviceData.duration && serviceData.duration < 1) {
      throw new ServiceValidationError('Duration must be at least 1 minute', 'duration');
    }
  }

  private canUserModifyService(service: Service, userId: string, userRole: string): boolean {
    return userRole === 'ADMIN' || service.userId === userId;
  }

  private buildOrderBy(sortBy?: string, sortOrder: 'asc' | 'desc' = 'desc'): any {
    switch (sortBy) {
      case 'name':
        return { name: sortOrder };
      case 'price':
        return { price: sortOrder };
      case 'rating':
        return { rating: sortOrder };
      case 'orders':
        return { totalOrders: sortOrder };
      case 'date':
      default:
        return { createdAt: sortOrder };
    }
  }
}