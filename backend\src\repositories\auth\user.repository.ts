// User Repository - Following SRP and ISP principles
import { PrismaClient, User, UserRole } from '@prisma/client';
import { BaseRepository } from '@/repositories/base/base.repository';
import { CreateUserData, UpdateUserData } from '@/types/auth';

// User-specific repository interface (ISP)
export interface IUserRepository {
  findByEmail(email: string): Promise<User | null>;
  findByEmailToken(token: string): Promise<User | null>;
  findByResetToken(token: string): Promise<User | null>;
  createUser(userData: CreateUserData): Promise<User>;
  updateUser(id: string, userData: UpdateUserData): Promise<User>;
  verifyEmail(id: string): Promise<User>;
  setResetToken(id: string, token: string, expiry: Date): Promise<User>;
  clearResetToken(id: string): Promise<User>;
  updateLastLogin(id: string): Promise<User>;
  findActiveUsers(options?: {
    skip?: number;
    take?: number;
    role?: UserRole;
  }): Promise<User[]>;
  searchUsers(query: string, options?: {
    skip?: number;
    take?: number;
    role?: UserRole;
  }): Promise<User[]>;
  getUserStats(): Promise<{
    total: number;
    verified: number;
    active: number;
    byRole: Record<UserRole, number>;
  }>;
}

// User repository implementation (SRP)
export class UserRepository extends BaseRepository<User> implements IUserRepository {
  constructor(prisma: PrismaClient) {
    super(prisma, 'user');
  }

  async findByEmail(email: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { email: email.toLowerCase() },
      });
    } catch (error) {
      throw new Error(`Failed to find user by email: ${error}`);
    }
  }

  async findByEmailToken(token: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { emailToken: token },
      });
    } catch (error) {
      throw new Error(`Failed to find user by email token: ${error}`);
    }
  }

  async findByResetToken(token: string): Promise<User | null> {
    try {
      return await this.prisma.user.findUnique({
        where: { 
          resetToken: token,
          resetTokenExpiry: {
            gt: new Date(), // Token must not be expired
          },
        },
      });
    } catch (error) {
      throw new Error(`Failed to find user by reset token: ${error}`);
    }
  }

  async createUser(userData: CreateUserData): Promise<User> {
    try {
      return await this.prisma.user.create({
        data: {
          ...userData,
          email: userData.email.toLowerCase(),
        },
      });
    } catch (error) {
      throw new Error(`Failed to create user: ${error}`);
    }
  }

  async updateUser(id: string, userData: UpdateUserData): Promise<User> {
    try {
      return await this.prisma.user.update({
        where: { id },
        data: {
          ...userData,
          ...(userData.email && { email: userData.email.toLowerCase() }),
        },
      });
    } catch (error) {
      throw new Error(`Failed to update user: ${error}`);
    }
  }

  async verifyEmail(id: string): Promise<User> {
    try {
      return await this.prisma.user.update({
        where: { id },
        data: {
          isEmailVerified: true,
          emailToken: null,
        },
      });
    } catch (error) {
      throw new Error(`Failed to verify email: ${error}`);
    }
  }

  async setResetToken(id: string, token: string, expiry: Date): Promise<User> {
    try {
      return await this.prisma.user.update({
        where: { id },
        data: {
          resetToken: token,
          resetTokenExpiry: expiry,
        },
      });
    } catch (error) {
      throw new Error(`Failed to set reset token: ${error}`);
    }
  }

  async clearResetToken(id: string): Promise<User> {
    try {
      return await this.prisma.user.update({
        where: { id },
        data: {
          resetToken: null,
          resetTokenExpiry: null,
        },
      });
    } catch (error) {
      throw new Error(`Failed to clear reset token: ${error}`);
    }
  }

  async updateLastLogin(id: string): Promise<User> {
    try {
      return await this.prisma.user.update({
        where: { id },
        data: {
          lastLogin: new Date(),
        },
      });
    } catch (error) {
      throw new Error(`Failed to update last login: ${error}`);
    }
  }

  async findActiveUsers(options: {
    skip?: number;
    take?: number;
    role?: UserRole;
  } = {}): Promise<User[]> {
    try {
      const { skip, take, role } = options;
      return await this.prisma.user.findMany({
        where: {
          isActive: true,
          ...(role && { role }),
        },
        skip,
        take,
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find active users: ${error}`);
    }
  }

  async searchUsers(query: string, options: {
    skip?: number;
    take?: number;
    role?: UserRole;
  } = {}): Promise<User[]> {
    try {
      const { skip, take, role } = options;
      return await this.prisma.user.findMany({
        where: {
          isActive: true,
          ...(role && { role }),
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
          ],
        },
        skip,
        take,
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to search users: ${error}`);
    }
  }

  async getUserStats(): Promise<{
    total: number;
    verified: number;
    active: number;
    byRole: Record<UserRole, number>;
  }> {
    try {
      const [total, verified, active, byRoleData] = await Promise.all([
        this.prisma.user.count(),
        this.prisma.user.count({ where: { isEmailVerified: true } }),
        this.prisma.user.count({ where: { isActive: true } }),
        this.prisma.user.groupBy({
          by: ['role'],
          _count: { _all: true },
        }),
      ]);

      const byRole = byRoleData.reduce((acc, item) => {
        acc[item.role] = item._count._all;
        return acc;
      }, {} as Record<UserRole, number>);

      // Ensure all roles are present
      Object.values(UserRole).forEach(role => {
        if (!(role in byRole)) {
          byRole[role] = 0;
        }
      });

      return {
        total,
        verified,
        active,
        byRole,
      };
    } catch (error) {
      throw new Error(`Failed to get user stats: ${error}`);
    }
  }
}