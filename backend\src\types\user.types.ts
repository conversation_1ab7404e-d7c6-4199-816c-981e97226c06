import {z} from 'zod';
import {UserRole, AddressType} from './common.types';

// User schemas
export const CreateUserSchema = z.object({
	name: z.string().min(2, 'Name must be at least 2 characters'),
	email: z.string().email('Invalid email address'),
	password: z.string().min(6, 'Password must be at least 6 characters'),
	role: z.nativeEnum(UserRole).default(UserRole.CUSTOMER),
	phone: z.string().optional(),
	avatar: z.string().url().optional(),
});

export const UpdateUserSchema = z.object({
	name: z.string().min(2, 'Name must be at least 2 characters').optional(),
	phone: z.string().optional(),
	avatar: z.string().url().optional(),
});

export const CreateAddressSchema = z.object({
	type: z.nativeEnum(AddressType),
	street: z.string().min(1, 'Street address is required'),
	city: z.string().min(1, 'City is required'),
	state: z.string().min(1, 'State is required'),
	zipCode: z.string().min(1, 'ZIP code is required'),
	country: z.string().default('US'),
	isDefault: z.boolean().default(false),
});

export const UpdateAddressSchema = CreateAddressSchema.partial();

// User types
export type CreateUserRequest = z.infer<typeof CreateUserSchema>;
export type UpdateUserRequest = z.infer<typeof UpdateUserSchema>;
export type CreateAddressRequest = z.infer<typeof CreateAddressSchema>;
export type UpdateAddressRequest = z.infer<typeof UpdateAddressSchema>;

export interface User {
	id: string;
	email: string;
	name: string;
	role: UserRole;
	isActive: boolean;
	isVerified: boolean;
	emailVerified?: Date;
	avatar?: string;
	phone?: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface UserAddress {
	id: string;
	userId: string;
	type: AddressType;
	street: string;
	city: string;
	state: string;
	zipCode: string;
	country: string;
	isDefault: boolean;
	createdAt: Date;
	updatedAt: Date;
}

export interface UserProfile extends User {
	addresses: UserAddress[];
	defaultAddress?: UserAddress;
}

export interface UserStats {
	totalOrders: number;
	completedOrders: number;
	pendingOrders: number;
	totalSpent: number;
	memberSince: Date;
}
