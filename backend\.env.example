# Environment Configuration
NODE_ENV=development
PORT=3001

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/printwedit?schema=public"

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
REFRESH_SECRET=your-super-secret-refresh-key-change-this-in-production

# Encryption Configuration
ENCRYPTION_KEY=your-encryption-key-for-sensitive-data

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=PrintWeditt

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=printwedit-uploads

# Redis Configuration (for sessions/caching)
REDIS_URL=redis://localhost:6379

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
AUTH_RATE_LIMIT_MAX=5

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log

# External APIs
GOOGLE_CLIENT_ID=your-google-oauth-client-id
GOOGLE_CLIENT_SECRET=your-google-oauth-client-secret

# Payment Configuration
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Application URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:3001
API_BASE_URL=http://localhost:3001/api/v1