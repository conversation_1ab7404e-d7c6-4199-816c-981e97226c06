import React from 'react';
import { Link } from 'react-router-dom';
import { useServices } from '../../contexts';
import { ServiceCard } from '../../components/business';
import {
  CheckCircle,
  Clock,
  Shield,
  Award,
  ChevronDown,
  ChevronUp,
  Star,
  Palette,
  Lightbulb,
  Users,
  Zap,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

const DesignServices: React.FC = () => {
  const { services } = useServices();
  const [openFaq, setOpenFaq] = React.useState<number | null>(null);
  const [currentHeroImageIndex, setCurrentHeroImageIndex] = React.useState(0);
  const [portfolioScrollPosition, setPortfolioScrollPosition] = React.useState(0);
  const portfolioScrollRef = React.useRef<HTMLDivElement>(null);
  const designServicesSectionRef = React.useRef<HTMLDivElement>(null);

  const designServices = services.filter(
    (service) => service.category === 'Design Services' && service.isActive
  );

  // Hero images array for design services
  const heroImages = [
    {
      url: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75",
      alt: "Professional Design Services"
    },
    {
      url: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75",
      alt: "Creative Design Solutions"
    },
    {
      url: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75",
      alt: "Brand Identity Design"
    },
    {
      url: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75",
      alt: "Marketing Design Services"
    }
  ];

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const scrollToDesignServices = () => {
    if (designServicesSectionRef.current) {
      designServicesSectionRef.current.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };
  // Auto-advance hero images every 5 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentHeroImageIndex((prev) => (prev + 1) % heroImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [heroImages.length]);

  const scrollPortfolio = (direction: 'left' | 'right') => {
    if (portfolioScrollRef.current) {
      const scrollAmount = 280; // Width of card + gap
      const currentScroll = portfolioScrollRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      portfolioScrollRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
      setPortfolioScrollPosition(newScroll);
    }
  };

  const handlePortfolioScroll = () => {
    if (portfolioScrollRef.current) {
      setPortfolioScrollPosition(portfolioScrollRef.current.scrollLeft);
    }
  };
  const faqs = [
    {
      question: 'What design services do you offer?',
      answer:
        'We offer comprehensive design services including logo design, business cards, brochures, flyers, banners, and posters. Our professional designers create custom artwork tailored to your brand and needs.',
    },
    {
      question: 'How does the design process work?',
      answer:
        'Our design process includes consultation, concept development, design creation, revisions based on your feedback, and final file delivery. We work closely with you to ensure the design meets your vision.',
    },
    {
      question: 'How many revisions are included?',
      answer:
        "Most design packages include 3 rounds of revisions. Additional revisions can be requested for a small fee. We want to ensure you're completely satisfied with the final design.",
    },
    {
      question: 'What file formats will I receive?',
      answer:
        "You'll receive high-resolution files in multiple formats including PNG, JPG, PDF, and vector files (AI/EPS) when applicable. All files are print-ready and web-optimized.",
    },
    {
      question: 'How long does the design process take?',
      answer:
        'Design turnaround varies by project complexity. Logo designs typically take 5-7 business days, while simpler projects like flyers can be completed in 2-3 business days.',
    },
    {
      question: 'Can you match my existing brand guidelines?',
      answer:
        'Absolutely! We can work with your existing brand guidelines, colors, fonts, and style preferences to ensure consistency across all your marketing materials.',
    },
  ];

  const clientSamples = [
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      title: 'Tech Logo',
      description: 'Modern logo design with clean typography and icon',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      title: 'Flyer design',
      description: 'Complete brand identity with logo and business cards',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      title: 'flyer design Branding',
      description: 'Complete brand identity with logo and business cards',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      title: 'Restaurant Branding',
      description: 'Complete brand identity with logo and business cards',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      title: 'Event Poster Design',
      description: 'Eye-catching poster with custom illustrations',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      title: 'Marketing Campaign',
      description: 'Cohesive design across multiple marketing materials',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Section 1: Hero with Headline and Image */}
      <section className="bg-white py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-600">
                Professional Design Services
              </h1>
              <p className="text-xl mb-8 text-gray-700">
                Bring your vision to life with custom design services. From
                logos to marketing materials, our talented designers create
                stunning visuals that represent your brand perfectly.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  onClick={scrollToDesignServices}
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  View Services
                </button>
                <Link
                  to="/our-work"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  Our Works
                </Link>
              </div>
            </div>
            <div className="relative">
              {/* Image Slider Container */}
              <div className="relative overflow-hidden rounded-lg shadow-2xl">
                <div className="relative h-96 md:h-[500px]">
                  {heroImages.map((image, index) => (
                    <div
                      key={index}
                      className={`absolute inset-0 transition-opacity duration-500 ease-in-out ${
                        index === currentHeroImageIndex ? 'opacity-100' : 'opacity-0'
                      }`}
                    >
                      <img
                        src={image.url}
                        alt={image.alt}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
                
                {/* Navigation Dots */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {heroImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentHeroImageIndex(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentHeroImageIndex
                          ? 'bg-white scale-110'
                          : 'bg-white/50 hover:bg-white/75'
                      }`}
                      aria-label={`View image ${index + 1}`}
                    />
                  ))}
                </div>
              </div>
              
              <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <Palette className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Custom Design</p>
                    <p className="text-sm text-gray-600">
                      Tailored to your brand
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
      {/* What We Do Section */}
      <section className="py-10 bg-white">
        <div className="max-w-full mx-auto px-9 sm:px-10 lg:px-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
            {/* Logo Design */}
            <Link
              to="/design-services/logo-design"
              className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 block"
            >
              <div className="mb-4">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75"
                  alt="Logo Design"
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <h3 className="text-lg font-bold text-orange-500 mb-2">
                Logo Design
              </h3>
              <p className="text-sm text-gray-600">
                Your business deserves a standout logo, let us design it for you
              </p>
            </Link>

            {/* Printing Design */}
            <Link
              to="/design-services/printing-design"
              className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 block"
            >
              <div className="mb-4">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75"
                  alt="Printing Design"
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <h3 className="text-lg font-bold text-orange-500 mb-2">
                Printing Design
              </h3>
              <p className="text-sm text-gray-600">
                From brochures to business cards we create stunning print
                designs that captivate your audience and elevate your brand.
              </p>
            </Link>

            {/* Package Design */}
            <Link
              to="/design-services/package-design"
              className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 block"
            >
              <div className="mb-4">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725923064812.jpg&w=1080&q=75"
                  alt="Package Design"
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <h3 className="text-lg font-bold text-orange-500 mb-2">
                Package Design
              </h3>
              <p className="text-sm text-gray-600">
                Make your products unforgettable with custom packaging that
                stands out on the shelf and strengthens your brand identity.
              </p>
            </Link>

            {/* Website Design */}
            <Link
              to="/design-services/website-design"
              className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 relative block"
            >
              <div className="mb-4">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75"
                  alt="Website Design"
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <h3 className="text-lg font-bold text-orange-500 mb-2">
                Website Design
              </h3>
              <p className="text-sm text-gray-600">
                Bring your business online with a responsive and visually
                appealing website that reflects your brand and engages your
                customers.
              </p>
            </Link>

            {/* Illustrator Art */}
            <Link
              to="/design-services/illustrator-art"
              className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 block"
            >
              <div className="mb-4">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75"
                  alt="Illustrator Art"
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <h3 className="text-lg font-bold text-orange-500 mb-2">
                Illustrator Art
              </h3>
              <p className="text-sm text-gray-600">
                Transform your ideas into stunning digital illustrations that
                add a unique, artistic touch to your projects and brand.
              </p>
            </Link>

            {/* Vehicle Wraps */}
            <Link
              to="/design-services/vehicle-wraps"
              className="bg-white rounded-lg p-6 shadow-lg hover:shadow-xl transition-shadow duration-300 block"
            >
              <div className="mb-4">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789523124.jpg&w=1080&q=75"
                  alt="Vehicle Wraps"
                  className="w-full h-64 object-cover rounded-lg"
                />
              </div>
              <h3 className="text-lg font-bold text-orange-500 mb-2">
                Vehicle Wraps
              </h3>
              <p className="text-sm text-gray-600">
                Turn your vehicle into a moving billboard with eye-catching
                wraps that increase visibility and promote your brand wherever
                you go.
              </p>
            </Link>
          </div>
        </div>
      </section>

      {/* Section 2: Services on this Page */}
      <section ref={designServicesSectionRef} id="services" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Design Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Professional design services to elevate your brand and marketing
              materials with custom, high-quality artwork.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {designServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>

          {designServices.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">
                No design services available at the moment.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Section 3: Information About This Category */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-12">
            <h2 className="text-l md:text-5xl font-bold text-orange-500 mb-8">
              Why Choose us ...
            </h2>
          </div>

          <div className="bg-orange-50 rounded-3xl p-8 lg:p-12">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
              {/* Left side - Colorful illustration */}
              <div className="flex justify-center">
                <div className="relative">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75"
                    alt="Creative Design Illustration"
                    className="w-80 h-80 object-cover rounded-2xl shadow-lg"
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-pink-500/20 via-purple-500/20 to-blue-500/20 rounded-2xl"></div>
                </div>
              </div>

              {/* Middle column - Features 01 and 02 */}
              <div className="space-y-8">
                <div>
                  <div className="flex items-center mb-3">
                    <div className="bg-blue-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                      01
                    </div>
                    <h3 className="text-xl font-bold text-blue-600">
                      Experience
                    </h3>
                  </div>
                  <p className="text-gray-700 ml-11">
                    Our team of designers has years of experience creating
                    professional and effective designs for a variety of
                    businesses and industries.
                  </p>
                </div>

                <div>
                  <div className="flex items-center mb-3">
                    <div className="bg-orange-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                      02
                    </div>
                    <h3 className="text-xl font-bold text-orange-600">
                      Attention to Detail
                    </h3>
                  </div>
                  <p className="text-gray-700 ml-11">
                    We believe that the little things can make a big difference,
                    which is why we pay attention to every detail to ensure that
                    your design is flawless.
                  </p>
                </div>
              </div>

              {/* Right column - Features 03 and 04 */}
              <div className="space-y-8">
                <div>
                  <div className="flex items-center mb-3">
                    <div className="bg-green-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                      03
                    </div>
                    <h3 className="text-xl font-bold text-green-600">
                      Personalized Service
                    </h3>
                  </div>
                  <p className="text-gray-700 ml-11">
                    We work closely with each of our clients to understand their
                    unique needs and goals, and we tailor our services to meet
                    those needs.
                  </p>
                </div>

                <div>
                  <div className="flex items-center mb-3">
                    <div className="bg-yellow-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                      04
                    </div>
                    <h3 className="text-xl font-bold text-yellow-600">
                      Affordable Pricing
                    </h3>
                  </div>
                  <p className="text-gray-700 ml-11">
                    We believe that professional design services should be
                    accessible to businesses of all sizes, which is why we offer
                    competitive pricing.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Additional Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Elevate Your Brand with Professional Design
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Great design is more than just aesthetics—it's about
                communicating your brand's message effectively and creating a
                lasting impression. Our experienced designers work with you to
                create visuals that resonate with your audience.
              </p>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Brand Consistency
                    </h3>
                    <p className="text-gray-600">
                      Cohesive design across all your marketing materials builds
                      brand recognition.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Professional Impact
                    </h3>
                    <p className="text-gray-600">
                      Quality design establishes credibility and trust with your
                      audience.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Custom Solutions
                    </h3>
                    <p className="text-gray-600">
                      Tailored designs that reflect your unique brand
                      personality and values.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-purple-50 p-6 rounded-lg text-center">
                <Palette className="h-8 w-8 text-purple-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Creative Design
                </h3>
                <p className="text-sm text-gray-600">
                  Innovative visual solutions
                </p>
              </div>
              <div className="bg-blue-50 p-6 rounded-lg text-center">
                <Lightbulb className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Strategic Thinking
                </h3>
                <p className="text-sm text-gray-600">Design with purpose</p>
              </div>
              <div className="bg-green-50 p-6 rounded-lg text-center">
                <Users className="h-8 w-8 text-green-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Collaborative Process
                </h3>
                <p className="text-sm text-gray-600">
                  Work closely with designers
                </p>
              </div>
              <div className="bg-orange-50 p-6 rounded-lg text-center">
                <Zap className="h-8 w-8 text-orange-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Fast Delivery
                </h3>
                <p className="text-sm text-gray-600">Quick turnaround times</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 4: Sample Images from Previous Clients */}
      <section id="portfolio" className="w-full py-20 bg-[#f5fbff]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Client Love Content */}
            <div className="lg:col-span-1">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                What our clients say ...
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We're a global team of skilled designers united by a passion for design and a commitment to excellence.
              </p>
              
              {/* Statistics */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Star className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Our Services</div>
                    <div className="text-gray-600">Flyers, banners, websites and more—completed for startups & enterprises alike	</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Lightbulb className="h-8 w-8 text-purple-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Unlimited Tweaks</div>
                    <div className="text-gray-600">We iterate until you say “perfect.”	</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Award className="h-8 w-8 text-orange-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Print‑Ready Files</div>
                    <div className="text-gray-600">Get PNG, PDF, and editable AI/SVG—no extra fees</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Scrollable Portfolio */}
            <div className="lg:col-span-2">
              <div className="relative">
                {/* Left Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('left')}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                    portfolioScrollPosition <= 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-50 hover:shadow-xl'
                  }`}
                  disabled={portfolioScrollPosition <= 0}
                >
                  <ChevronLeft className="h-5 w-5 text-gray-600" />
                </button>

                {/* Right Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('right')}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:bg-gray-50 hover:shadow-xl"
                >
                  <ChevronRight className="h-5 w-5 text-gray-600" />
                </button>

                {/* Scrollable Container */}
                <div 
                  ref={portfolioScrollRef}
                  onScroll={handlePortfolioScroll}
                  className="overflow-x-auto whitespace-nowrap py-4 px-12 scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                <div className="flex space-x-6">
                  {clientSamples.map((sample, index) => (
                    <div
                      key={index}
                      className="inline-block w-64 bg-white rounded-lg shadow-lg overflow-hidden flex-shrink-0 hover:shadow-xl transition-shadow duration-300"
                    >
                      <img
                        src={sample.image}
                        alt={sample.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <h3 className="font-semibold text-gray-900 mb-2 whitespace-normal">
                          {sample.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4 whitespace-normal">{sample.description}</p>
                        
                        {/* Star Rating */}
                        <div className="flex items-center mb-4">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className="h-4 w-4 text-yellow-400 fill-current"
                            />
                          ))}
                          <span className="text-sm text-gray-500 ml-2">5.0</span>
                        </div>
                        
                        {/* Reviewer Info */}
                        <div className="flex items-center space-x-3">
                          <img
                            src={sample.image}
                            alt="Client"
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <p className="text-sm font-medium text-gray-900 whitespace-normal">
                            Happy Client
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              </div>
            </div>
          </div>

        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-[#f5fbff]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-[#a7bed3] p-8 rounded-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Ready to Start Your Design Project?
              </h3>
              <p className="text-gray-600 mb-6">
                Let our talented designers bring your vision to life with
                professional, custom artwork.
              </p>
              <Link
                to="#services"
                className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
              >
                Get Started Now
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Section 5: FAQ Section */}
      <section className="py-20 bg-[#f5fbff]">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to know about our professional design
              services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                >
                  <span className="font-semibold text-gray-900">
                    {faq.question}
                  </span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">Still have questions?</p>
            <Link
              to="/contact"
              className="text-orange-500 hover:text-orange-700 font-semibold"
            >
              Contact our design team →
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default DesignServices;
