import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Link } from 'react-router-dom';
import { useServices, FormField, FormFieldOption, GalleryImage } from '../contexts/ServiceContext';
import { useProviders, Provider } from '../contexts/ProviderContext';
import ServiceCard from '../components/ServiceCard';
import { ArrowLeft, ShoppingCart, Plus, Minus, Check, Upload, X, MapPin, Star, Phone, Mail, ExternalLink } from 'lucide-react';

const ServiceDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { services, getGalleryImagesByCategory } = useServices();
  const { getProvidersByService, getProvidersByLocation } = useProviders();
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [totalPrice, setTotalPrice] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [designOption, setDesignOption] = useState<'upload' | 'service' | null>(null);
  const [customerLocation, setCustomerLocation] = useState('');
  const [nearbyProviders, setNearbyProviders] = useState<Provider[]>([]);
  const [selectedProvider, setSelectedProvider] = useState<Provider | null>(null);
  const [showProviders, setShowProviders] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<{
    front?: File;
    back?: File;
  }>({});
  const [selectedImage, setSelectedImage] = useState<GalleryImage | null>(null);
  const [activeTab, setActiveTab] = useState<'product' | 'design'>('product');

  const service = services.find(s => s.id === id);
  
  // Get related services (same category, excluding current service)
  const relatedServices = services.filter(s => 
    s.category === service?.category && 
    s.id !== service?.id && 
    s.isActive
  ).slice(0, 3);
  
  // Get gallery images for this service's category
  const galleryImages = service ? getGalleryImagesByCategory(service.category) : [];

  useEffect(() => {
    if (service) {
      // Initialize form with default values
      const initialData: Record<string, any> = {};
      service.formFields.forEach(field => {
        if (field.defaultValue) {
          initialData[field.name] = field.defaultValue;
        } else if (field.type === 'checkbox') {
          initialData[field.name] = [];
        }
      });
      setFormData(initialData);
    }
  }, [service]);

  useEffect(() => {
    if (service) {
      calculatePrice();
    }
  }, [formData, quantity, service, designOption]);

  const calculatePrice = () => {
    if (!service) return;

    let price = service.price;
    
    // Add design service cost
    if (designOption === 'service') {
      price += 39;
    }
    
    service.formFields.forEach(field => {
      const value = formData[field.name];
      
      if (field.type === 'checkbox' && Array.isArray(value)) {
        value.forEach((selectedValue: string) => {
          const option = field.options?.find(opt => opt.value === selectedValue);
          if (option) {
            price += option.priceModifier;
          }
        });
      } else if (value && field.options) {
        const option = field.options.find(opt => opt.value === value);
        if (option) {
          price += option.priceModifier;
        }
      }
    });

    setTotalPrice(price * quantity);
  };

  const handleFileUpload = (side: 'front' | 'back', file: File | null) => {
    if (file) {
      setUploadedFiles(prev => ({
        ...prev,
        [side]: file
      }));
    } else {
      setUploadedFiles(prev => {
        const newFiles = { ...prev };
        delete newFiles[side];
        return newFiles;
      });
    }
  };

  const handleInputChange = (fieldName: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));
  };

  const handleCheckboxChange = (fieldName: string, optionValue: string, checked: boolean) => {
    setFormData(prev => {
      const currentValues = prev[fieldName] || [];
      if (checked) {
        return {
          ...prev,
          [fieldName]: [...currentValues, optionValue]
        };
      } else {
        return {
          ...prev,
          [fieldName]: currentValues.filter((val: string) => val !== optionValue)
        };
      }
    });
  };

  const handleLocationSearch = () => {
    if (!customerLocation.trim()) {
      alert('Please enter a ZIP code or address');
      return;
    }

    // Get providers that offer this service
    const serviceProviders = getProvidersByService(service!.id);
    
    // Filter by location (simplified - in real app would use geolocation)
    const locationProviders = getProvidersByLocation(customerLocation);
    
    // Find intersection of service providers and location providers
    const availableProviders = serviceProviders.filter(sp => 
      locationProviders.some(lp => lp.id === sp.id)
    );

    // Sort by distance (mock sorting - in real app would calculate actual distance)
    const sortedProviders = availableProviders.sort((a, b) => {
      // Mock distance calculation based on business name for demo
      return a.businessName.localeCompare(b.businessName);
    });

    // Show top 5 providers
    setNearbyProviders(sortedProviders.slice(0, 5));
    setShowProviders(true);

    if (availableProviders.length === 0) {
      alert('No providers found in your area for this service. Please try a different location or contact us for assistance.');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    const missingFields = service?.formFields.filter(field => 
      field.required && !formData[field.name]
    );

    if (missingFields && missingFields.length > 0) {
      alert(`Please fill in all required fields: ${missingFields.map(f => f.label).join(', ')}`);
      return;
    }

    // Validate design option
    if (!designOption) {
      alert('Please select a design option (upload your design or use our design service)');
      return;
    }

    if (designOption === 'upload' && !uploadedFiles.front) {
      alert('Please upload at least a front side design');
      return;
    }

    // Validate location and provider selection
    if (!customerLocation.trim()) {
      alert('Please enter your location to find nearby providers');
      return;
    }

    if (!selectedProvider) {
      alert('Please select a printing provider to fulfill your order');
      return;
    }

    // Here you would typically add to cart or process the order
    console.log('Order data:', {
      service: service?.name,
      configuration: formData,
      quantity,
      designOption,
      uploadedFiles: Object.keys(uploadedFiles),
      totalPrice,
      customerLocation,
      selectedProvider: selectedProvider?.businessName
    });

    alert('Service added to cart successfully!');
  };

  const renderFormField = (field: FormField) => {
    switch (field.type) {
      case 'select':
        return (
          <div key={field.id} className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </label>
            <select
              value={formData[field.name] || ''}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required={field.required}
            >
              <option value="">Select {field.label}</option>
              {field.options?.map((option) => (
                <option key={option.id} value={option.value}>
                  {option.label}
                  {option.priceModifier !== 0 && (
                    <span className="text-gray-600">
                      {option.priceModifier > 0 ? ' (+' : ' ('}${Math.abs(option.priceModifier)})
                    </span>
                  )}
                </option>
              ))}
            </select>
          </div>
        );

      case 'radio':
        return (
          <div key={field.id} className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </label>
            <div className="space-y-2">
              {field.options?.map((option) => (
                <label key={option.id} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="radio"
                    name={field.name}
                    value={option.value}
                    checked={formData[field.name] === option.value}
                    onChange={(e) => handleInputChange(field.name, e.target.value)}
                    className="mr-3 text-blue-600"
                    required={field.required}
                  />
                  <div className="flex-1">
                    <span className="text-gray-900">{option.label}</span>
                    {option.priceModifier !== 0 && (
                      <span className="text-sm text-gray-600 ml-2">
                        {option.priceModifier > 0 ? '+' : ''}${option.priceModifier}
                      </span>
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        );

      case 'checkbox':
        return (
          <div key={field.id} className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </label>
            <div className="space-y-2">
              {field.options?.map((option) => (
                <label key={option.id} className="flex items-center p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                  <input
                    type="checkbox"
                    checked={(formData[field.name] || []).includes(option.value)}
                    onChange={(e) => handleCheckboxChange(field.name, option.value, e.target.checked)}
                    className="mr-3 text-blue-600"
                  />
                  <div className="flex-1">
                    <span className="text-gray-900">{option.label}</span>
                    {option.priceModifier !== 0 && (
                      <span className="text-sm text-gray-600 ml-2">
                        {option.priceModifier > 0 ? '+' : ''}${option.priceModifier}
                      </span>
                    )}
                  </div>
                </label>
              ))}
            </div>
          </div>
        );

      case 'number':
        return (
          <div key={field.id} className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </label>
            <input
              type="number"
              value={formData[field.name] || ''}
              onChange={(e) => handleInputChange(field.name, parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required={field.required}
              min="1"
            />
          </div>
        );

      case 'text':
        return (
          <div key={field.id} className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {field.label} {field.required && <span className="text-red-500">*</span>}
            </label>
            <input
              type="text"
              value={formData[field.name] || ''}
              onChange={(e) => handleInputChange(field.name, e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required={field.required}
            />
          </div>
        );

      default:
        return null;
    }
  };

  if (!service) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Service Not Found</h1>
          <button
            onClick={() => navigate('/services')}
            className="text-blue-600 hover:text-blue-700"
          >
            Back to Services
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Back Button */}
        <button
          onClick={() => navigate('/services')}
          className="flex items-center text-blue-600 hover:text-blue-700 mb-6"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Services
        </button>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Service Info */}
          <div>
            <img
              src={service.image}
              alt={service.name}
              className="w-full h-64 object-cover rounded-lg shadow-lg mb-6"
            />
            
            <h1 className="text-3xl font-bold text-gray-900 mb-4">{service.name}</h1>
            <p className="text-gray-600 mb-6">{service.description}</p>
            
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Features:</h3>
              <ul className="space-y-2">
                {service.features.map((feature, index) => (
                  <li key={index} className="flex items-center text-gray-600">
                    <Check className="h-5 w-5 text-green-500 mr-2" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <h3 className="font-semibold text-blue-900 mb-2">Need Help?</h3>
              <p className="text-blue-700 text-sm">
                Our design team is available to help you create the perfect design for your {service.name.toLowerCase()}.
              </p>
            </div>
          </div>

          {/* Configuration Form */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Configure Your Order</h2>
            
            <form onSubmit={handleSubmit}>
              {service.formFields.map(renderFormField)}

              {/* Design Options */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-3">
                  Design Options <span className="text-red-500">*</span>
                </label>
                
                <div className="space-y-4">
                  {/* Upload Design Option */}
                  <div className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                    designOption === 'upload' 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <label className="flex items-start cursor-pointer">
                      <input
                        type="radio"
                        name="designOption"
                        value="upload"
                        checked={designOption === 'upload'}
                        onChange={(e) => setDesignOption(e.target.value as 'upload')}
                        className="mt-1 mr-3 text-blue-600"
                      />
                      <div className="flex-1">
                        <div className="font-medium text-gray-900 mb-2">Upload Your Design</div>
                        <p className="text-sm text-gray-600 mb-3">
                          Upload your own design files (PDF, PNG, JPG, AI, PSD)
                        </p>
                        
                        {designOption === 'upload' && (
                          <div className="space-y-3">
                            {/* Front Side Upload */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Front Side Design <span className="text-red-500">*</span>
                              </label>
                              <div className="flex items-center space-x-3">
                                <label className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                  <Upload className="h-4 w-4 mr-2 text-gray-500" />
                                  <span className="text-sm text-gray-700">Choose File</span>
                                  <input
                                    type="file"
                                    accept=".pdf,.png,.jpg,.jpeg,.ai,.psd"
                                    onChange={(e) => handleFileUpload('front', e.target.files?.[0] || null)}
                                    className="hidden"
                                  />
                                </label>
                                {uploadedFiles.front && (
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm text-green-600">{uploadedFiles.front.name}</span>
                                    <button
                                      type="button"
                                      onClick={() => handleFileUpload('front', null)}
                                      className="text-red-500 hover:text-red-700"
                                    >
                                      <X className="h-4 w-4" />
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            {/* Back Side Upload (Optional) */}
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-1">
                                Back Side Design (Optional)
                              </label>
                              <div className="flex items-center space-x-3">
                                <label className="flex items-center px-4 py-2 bg-white border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                                  <Upload className="h-4 w-4 mr-2 text-gray-500" />
                                  <span className="text-sm text-gray-700">Choose File</span>
                                  <input
                                    type="file"
                                    accept=".pdf,.png,.jpg,.jpeg,.ai,.psd"
                                    onChange={(e) => handleFileUpload('back', e.target.files?.[0] || null)}
                                    className="hidden"
                                  />
                                </label>
                                {uploadedFiles.back && (
                                  <div className="flex items-center space-x-2">
                                    <span className="text-sm text-green-600">{uploadedFiles.back.name}</span>
                                    <button
                                      type="button"
                                      onClick={() => handleFileUpload('back', null)}
                                      className="text-red-500 hover:text-red-700"
                                    >
                                      <X className="h-4 w-4" />
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <div className="bg-blue-50 p-3 rounded-lg">
                              <p className="text-sm text-blue-700">
                                <strong>File Requirements:</strong> Maximum 50MB per file. 
                                Supported formats: PDF, PNG, JPG, AI, PSD. 
                                Minimum resolution: 300 DPI for best quality.
                              </p>
                            </div>
                          </div>
                        )}
                      </div>
                    </label>
                  </div>

                  {/* Design Service Option */}
                  <div className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${
                    designOption === 'service' 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}>
                    <label className="flex items-start cursor-pointer">
                      <input
                        type="radio"
                        name="designOption"
                        value="service"
                        checked={designOption === 'service'}
                        onChange={(e) => setDesignOption(e.target.value as 'service')}
                        className="mt-1 mr-3 text-blue-600"
                      />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <div className="font-medium text-gray-900">Professional Design Service</div>
                          <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm font-semibold">
                            +$39.00
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">
                          Let our professional designers create a custom design for you
                        </p>
                        <ul className="text-sm text-gray-600 space-y-1">
                          <li className="flex items-center">
                            <Check className="h-4 w-4 text-green-500 mr-2" />
                            Professional graphic designer assigned
                          </li>
                          <li className="flex items-center">
                            <Check className="h-4 w-4 text-green-500 mr-2" />
                            Up to 3 design revisions included
                          </li>
                          <li className="flex items-center">
                            <Check className="h-4 w-4 text-green-500 mr-2" />
                            High-resolution files provided
                          </li>
                          <li className="flex items-center">
                            <Check className="h-4 w-4 text-green-500 mr-2" />
                            24-48 hour turnaround
                          </li>
                        </ul>
                      </div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Quantity */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Quantity
                </label>
                <div className="flex items-center space-x-3">
                  <button
                    type="button"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-4 py-2 border border-gray-300 rounded-lg min-w-[60px] text-center">
                    {quantity}
                  </span>
                  <button
                    type="button"
                    onClick={() => setQuantity(quantity + 1)}
                    className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Location Input */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Location <span className="text-red-500">*</span>
                </label>
                <div className="flex space-x-3">
                  <input
                    type="text"
                    value={customerLocation}
                    onChange={(e) => setCustomerLocation(e.target.value)}
                    placeholder="Enter ZIP code or address"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                  <button
                    type="button"
                    onClick={handleLocationSearch}
                    className="bg-brand-orange text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors flex items-center space-x-2"
                  >
                    <MapPin className="h-4 w-4" />
                    <span>Find Providers</span>
                  </button>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  We'll find the 5 nearest printing providers who can fulfill this order.
                </p>
              </div>

              {/* Provider Selection */}
              {showProviders && (
                <div className="mb-6">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Select Printing Provider <span className="text-red-500">*</span>
                  </label>
                  
                  {nearbyProviders.length > 0 ? (
                    <div className="space-y-3">
                      {nearbyProviders.map((provider) => (
                        <div
                          key={provider.id}
                          className={`border-2 rounded-lg p-4 cursor-pointer transition-all hover:shadow-md ${
                            selectedProvider?.id === provider.id
                              ? 'border-blue-500 bg-blue-50'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => setSelectedProvider(provider)}
                        >
                          <div className="flex items-center space-x-4">
                            <input
                              type="radio"
                              name="provider"
                              checked={selectedProvider?.id === provider.id}
                              onChange={() => setSelectedProvider(provider)}
                              className="text-blue-600"
                            />
                            
                            {/* Desktop Layout */}
                            <div className="hidden md:flex items-center space-x-4 flex-1">
                              <img
                                src={provider.logo}
                                alt={provider.businessName}
                                className="w-12 h-12 rounded-lg object-cover"
                              />
                              <div className="flex-1">
                                <div className="flex items-center justify-between">
                                  <h3 className="font-semibold text-gray-900">{provider.businessName}</h3>
                                  {provider.isVerified && (
                                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                                      Verified
                                    </span>
                                  )}
                                </div>
                                <div className="flex items-center space-x-4 mt-1">
                                  <div className="flex items-center">
                                    {[...Array(5)].map((_, i) => (
                                      <Star 
                                        key={i} 
                                        className={`h-4 w-4 ${
                                          i < Math.floor(provider.averageRating) 
                                            ? 'text-yellow-400 fill-current' 
                                            : 'text-gray-300'
                                        }`} 
                                      />
                                    ))}
                                    <span className="ml-1 text-sm text-gray-600">
                                      {provider.averageRating} ({provider.reviewCount})
                                    </span>
                                  </div>
                                  <div className="flex items-center text-sm text-gray-600">
                                    <MapPin className="h-4 w-4 mr-1" />
                                    {provider.city}, {provider.state}
                                  </div>
                                </div>
                              </div>
                              <div className="flex items-center space-x-3">
                                <a 
                                  href={`tel:${provider.phone}`}
                                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <Phone className="h-4 w-4 mr-1" />
                                  Call
                                </a>
                                <a 
                                  href={`mailto:${provider.email}`}
                                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <Mail className="h-4 w-4 mr-1" />
                                  Email
                                </a>
                                <Link
                                  to={`/provider/${provider.id}`}
                                  className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <ExternalLink className="h-4 w-4 mr-1" />
                                  Profile
                                </Link>
                              </div>
                            </div>

                            {/* Mobile Layout */}
                            <div className="md:hidden flex-1">
                              <div className="flex items-start space-x-3">
                                <img
                                  src={provider.logo}
                                  alt={provider.businessName}
                                  className="w-10 h-10 rounded-lg object-cover"
                                />
                                <div className="flex-1">
                                  <h3 className="font-semibold text-gray-900 text-sm">{provider.businessName}</h3>
                                  <div className="flex items-center mt-1">
                                    {[...Array(5)].map((_, i) => (
                                      <Star 
                                        key={i} 
                                        className={`h-3 w-3 ${
                                          i < Math.floor(provider.averageRating) 
                                            ? 'text-yellow-400 fill-current' 
                                            : 'text-gray-300'
                                        }`} 
                                      />
                                    ))}
                                    <span className="ml-1 text-xs text-gray-600">
                                      {provider.averageRating}
                                    </span>
                                  </div>
                                  <div className="flex items-center text-xs text-gray-600 mt-1">
                                    <MapPin className="h-3 w-3 mr-1" />
                                    {provider.address}, {provider.city}
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                      
                      <div className="text-center pt-3">
                        <Link
                          to="/find-providers"
                          className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                        >
                          View All Providers in Your Area →
                        </Link>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 bg-gray-50 rounded-lg">
                      <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Providers Found</h3>
                      <p className="text-gray-600 mb-4">
                        No printing providers found in your area for this service.
                      </p>
                      <p className="text-sm text-gray-500">
                        Try a different location or{' '}
                        <Link to="/find-providers" className="text-blue-600 hover:text-blue-700">
                          browse all providers
                        </Link>
                      </p>
                    </div>
                  )}
                </div>
              )}

              {/* Price Summary */}
              <div className="bg-gray-50 p-4 rounded-lg mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">Base Price:</span>
                  <span className="text-gray-900">${service.price.toFixed(2)}</span>
                </div>
                {designOption === 'service' && (
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Design Service:</span>
                    <span className="text-gray-900">$39.00</span>
                  </div>
                )}
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-600">Quantity:</span>
                  <span className="text-gray-900">{quantity}</span>
                </div>
                {selectedProvider && (
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-gray-600">Provider:</span>
                    <span className="text-gray-900">{selectedProvider.businessName}</span>
                  </div>
                )}
                <hr className="my-2" />
                <div className="flex justify-between items-center text-lg font-bold">
                  <span className="text-gray-900">Total:</span>
                  <span className="text-blue-600">${totalPrice.toFixed(2)}</span>
                </div>
              </div>

              {/* Submit Button */}
              <button
                type="submit"
                className="w-full bg-brand-orange text-white py-3 px-6 rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center space-x-2"
              >
                <ShoppingCart className="h-5 w-5" />
                <span>Place Order</span>
              </button>
            </form>
          </div>

          {/* Product Information Tabs */}
        </div>

        {/* Product Information Tabs - Full Width */}
        <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mt-8">
          <div className="max-w-4xl mx-auto">
            <div className="border-b border-gray-200 mb-6">
              <nav className="-mb-px flex space-x-8">
                <button
                  onClick={() => setActiveTab('product')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'product'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Product Information
                </button>
                <button
                  onClick={() => setActiveTab('design')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'design'
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  Design Notes
                </button>
              </nav>
            </div>

            {/* Tab Content */}
            {activeTab === 'product' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">About This Service</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                
                <h4 className="text-lg font-semibold text-gray-900 mb-3">Features:</h4>
                <ul className="space-y-2">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-center text-gray-600">
                      <Check className="h-5 w-5 text-green-500 mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {activeTab === 'design' && (
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Design Guidelines</h3>
                <div className="space-y-4 text-gray-600">
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">File Requirements:</h4>
                    <ul className="space-y-1 ml-4">
                      <li>• High-resolution files (300 DPI minimum)</li>
                      <li>• Accepted formats: PDF, PNG, JPG, AI, PSD</li>
                      <li>• Maximum file size: 50MB per file</li>
                      <li>• Include bleed area (0.125" on all sides)</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Design Tips:</h4>
                    <ul className="space-y-1 ml-4">
                      <li>• Use CMYK color mode for accurate print colors</li>
                      <li>• Avoid placing important text near edges</li>
                      <li>• Consider readability at final print size</li>
                      <li>• Use vector graphics when possible for crisp results</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-2">Need Design Help?</h4>
                    <p>Our professional design team can create custom artwork that meets all technical requirements and captures your vision perfectly.</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Related Services Section */}
        <div className="mt-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Related Services</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Complete your order with these complementary printing services
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {relatedServices.map((relatedService) => (
              <ServiceCard key={relatedService.id} service={relatedService} />
            ))}
          </div>
          
          {relatedServices.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No related services available at the moment.</p>
            </div>
          )}
        </div>

        {/* Gallery Section */}
        {galleryImages.length > 0 && (
          <div className="mt-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-gray-900 mb-4">Gallery</h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                See examples of {service.name.toLowerCase()} we've created for our clients
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {galleryImages.map((image, index) => (
                <div 
                  key={index} 
                  className="relative group cursor-pointer overflow-hidden rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                  onClick={() => setSelectedImage(image)}
                >
                  <img
                    src={image.url}
                    alt={image.title}
                    className="w-full h-64 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-40 transition-all duration-300 flex items-center justify-center">
                    <div className="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
                      <h3 className="text-lg font-semibold mb-2">{image.title}</h3>
                      <p className="text-sm">{image.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="text-center mt-8">
              <p className="text-gray-600 mb-4">Need inspiration for your design?</p>
              <Link
                to="/contact"
                className="text-blue-600 hover:text-blue-700 font-semibold"
              >
                Contact our design team for custom solutions →
              </Link>
            </div>
          </div>
        )}

        {/* Image Modal */}
        {selectedImage && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="relative max-w-4xl max-h-full">
              <button
                onClick={() => setSelectedImage(null)}
                className="absolute top-4 right-4 text-white hover:text-gray-300 z-10"
              >
                <X className="h-8 w-8" />
              </button>
              <img
                src={selectedImage.url}
                alt={selectedImage.title}
                className="max-w-full max-h-full object-contain rounded-lg"
              />
              <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white p-4 rounded-b-lg">
                <h3 className="text-xl font-semibold mb-2">{selectedImage.title}</h3>
                <p className="text-gray-300">{selectedImage.description}</p>
                <p className="text-sm text-gray-400 mt-2">Category: {selectedImage.category}</p>
              </div>
            </div>
          </div>
        )}

        {/* Design Help Section */}
        <div className="mt-16">
          <div className="bg-warm-cream rounded-lg p-8 text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              Do You Need Design Help?
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-6">
              Not sure how to prepare your print design? No worries — our expert designers are here to help! Whether it's business cards, flyers, or banners, we'll create a professional design that's print-ready and perfectly aligned with your goals.
            </p>
            <Link
              to="/design-services"
              className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
            >
              Let's Design It Together
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ServiceDetail;