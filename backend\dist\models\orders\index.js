"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderFiles = exports.OrderTimeline = exports.OrderAmount = exports.OrderNumber = exports.OrderModel = void 0;
// Order Models Index
var Order_model_1 = require("./Order.model");
Object.defineProperty(exports, "OrderModel", { enumerable: true, get: function () { return Order_model_1.OrderModel; } });
Object.defineProperty(exports, "OrderNumber", { enumerable: true, get: function () { return Order_model_1.OrderNumber; } });
Object.defineProperty(exports, "OrderAmount", { enumerable: true, get: function () { return Order_model_1.OrderAmount; } });
Object.defineProperty(exports, "OrderTimeline", { enumerable: true, get: function () { return Order_model_1.OrderTimeline; } });
Object.defineProperty(exports, "OrderFiles", { enumerable: true, get: function () { return Order_model_1.OrderFiles; } });
//# sourceMappingURL=index.js.map