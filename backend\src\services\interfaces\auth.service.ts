import {
	LoginRequest,
	RegisterRequest,
	GoogleAuthRequest,
	RefreshTokenRequest,
	ChangePasswordRequest,
	LoginResponse,
	AuthTokens,
	AuthUser,
	JwtPayload,
} from '@/types/auth.types';

export interface IAuthService {
	// Authentication operations
	registerUser(userData: RegisterRequest): Promise<AuthUser>;
	loginUser(
		credentials: LoginRequest,
		ipAddress?: string,
		userAgent?: string
	): Promise<LoginResponse>;
	loginWithGoogle(
		token: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<LoginResponse>;
	logoutUser(refreshToken: string): Promise<void>;
	refreshToken(refreshToken: string): Promise<AuthTokens>;
	changePassword(
		userId: string,
		passwordData: ChangePasswordRequest
	): Promise<void>;

	// Token operations
	generateTokens(
		userId: string,
		email: string,
		role: string
	): Promise<AuthTokens>;
	verifyAccessToken(token: string): Promise<JwtPayload>;
	verifyRefreshToken(token: string): Promise<JwtPayload>;
	invalidateRefreshToken(token: string): Promise<void>;

	// Session management
	createSession(
		userId: string,
		refreshToken: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void>;
	validateSession(refreshToken: string): Promise<boolean>;
	revokeAllUserSessions(userId: string): Promise<void>;

	// Security operations
	hashPassword(password: string): Promise<string>;
	comparePasswords(password: string, hashedPassword: string): Promise<boolean>;
	validatePasswordStrength(password: string): Promise<boolean>;
	checkRateLimit(identifier: string, action: string): Promise<boolean>;
}
