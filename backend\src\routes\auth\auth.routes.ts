// Authentication Routes - Following SRP and REST principles
import { Router } from 'express';
import { AuthController } from '@/controllers/auth/auth.controller';
import { Validation<PERSON>hains } from '@/middleware/validation';
import { handleValidation } from '@/middleware/validation/validator';
import { authRateLimiter } from '@/middleware';
import { authenticate, optionalAuth } from '@/middleware/auth';

const router = Router();

// Apply rate limiting to all auth routes
router.use(authRateLimiter);

// Initialize controller (dependency injection would be handled here)
const authController = new AuthController();

// POST /api/v1/auth/register - User registration
router.post(
  '/register',
  ValidationChains.register(),
  handleValidation,
  authController.register.bind(authController)
);

// POST /api/v1/auth/login - User login
router.post(
  '/login',
  ValidationChains.login(),
  handleValidation,
  authController.login.bind(authController)
);

// POST /api/v1/auth/refresh - Refresh access token
router.post(
  '/refresh',
  ValidationChains.refreshToken(),
  handleValidation,
  authController.refreshToken.bind(authController)
);

// POST /api/v1/auth/logout - User logout
router.post(
  '/logout',
  authenticate,
  authController.logout.bind(authController)
);

// POST /api/v1/auth/verify-email - Email verification
router.post(
  '/verify-email',
  ValidationChains.verifyEmail(),
  handleValidation,
  authController.verifyEmail.bind(authController)
);

// POST /api/v1/auth/forgot-password - Request password reset
router.post(
  '/forgot-password',
  ValidationChains.forgotPassword(),
  handleValidation,
  authController.forgotPassword.bind(authController)
);

// POST /api/v1/auth/reset-password - Reset password
router.post(
  '/reset-password',
  ValidationChains.resetPassword(),
  handleValidation,
  authController.resetPassword.bind(authController)
);

// GET /api/v1/auth/me - Get current user profile
router.get(
  '/me',
  authenticate,
  authController.getCurrentUser.bind(authController)
);

// PUT /api/v1/auth/me - Update current user profile
router.put(
  '/me',
  authenticate,
  ValidationChains.updateProfile(),
  handleValidation,
  authController.updateProfile.bind(authController)
);

// POST /api/v1/auth/change-password - Change password
router.post(
  '/change-password',
  authenticate,
  ValidationChains.changePassword(),
  handleValidation,
  authController.changePassword.bind(authController)
);

// POST /api/v1/auth/resend-verification - Resend verification email
router.post(
  '/resend-verification',
  authenticate,
  authController.resendVerification.bind(authController)
);

// DELETE /api/v1/auth/sessions - Logout from all devices
router.delete(
  '/sessions',
  authenticate,
  authController.logoutAllDevices.bind(authController)
);

// GET /api/v1/auth/sessions - Get active sessions
router.get(
  '/sessions',
  authenticate,
  authController.getActiveSessions.bind(authController)
);

// DELETE /api/v1/auth/sessions/:sessionId - Logout from specific device
router.delete(
  '/sessions/:sessionId',
  authenticate,
  authController.logoutFromDevice.bind(authController)
);

export default router;