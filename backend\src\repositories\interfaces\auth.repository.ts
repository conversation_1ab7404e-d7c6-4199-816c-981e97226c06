import {AuthSession, JwtPayload} from '@/types/auth.types';
import {User} from '@/types/user.types';

export interface IAuthRepository {
	// Session management
	createSession(sessionData: Partial<AuthSession>): Promise<AuthSession>;
	findSessionByRefreshToken(refreshToken: string): Promise<AuthSession | null>;
	findSessionById(id: string): Promise<AuthSession | null>;
	findActiveSessionsByUserId(userId: string): Promise<AuthSession[]>;
	updateSession(
		id: string,
		sessionData: Partial<AuthSession>
	): Promise<AuthSession>;
	deleteSession(id: string): Promise<void>;
	deleteAllUserSessions(userId: string): Promise<void>;
	deleteExpiredSessions(): Promise<void>;

	// Token management
	invalidateRefreshToken(refreshToken: string): Promise<void>;
	isRefreshTokenValid(refreshToken: string): Promise<boolean>;

	// User authentication
	findUserWithPassword(
		email: string
	): Promise<(User & {password: string}) | null>;
	updateUserPassword(userId: string, hashedPassword: string): Promise<void>;

	// Security operations
	recordLoginAttempt(
		userId: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void>;
	getFailedLoginAttempts(userId: string, timeWindow: number): Promise<number>;
	clearFailedLoginAttempts(userId: string): Promise<void>;
}
