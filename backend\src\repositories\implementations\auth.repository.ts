import {PrismaClient} from '@prisma/client';
import {IAuthRepository} from '../interfaces/auth.repository';
import {AuthSession, JwtPayload} from '@/types/auth.types';
import {User} from '@/types/user.types';

export class AuthRepository implements IAuthRepository {
	constructor(private prisma: PrismaClient) {}

	async createSession(sessionData: Partial<AuthSession>): Promise<AuthSession> {
		const session = await this.prisma.userSession.create({
			data: {
				userId: sessionData.userId!,
				refreshToken: sessionData.refreshToken!,
				expiresAt: sessionData.expiresAt!,
				ipAddress: sessionData.ipAddress,
				userAgent: sessionData.userAgent,
				isActive: sessionData.isActive ?? true,
			},
		});

		return this.mapToAuthSession(session);
	}

	async findSessionByRefreshToken(
		refreshToken: string
	): Promise<AuthSession | null> {
		const session = await this.prisma.userSession.findUnique({
			where: {refreshToken},
		});

		return session ? this.mapToAuthSession(session) : null;
	}

	async findSessionById(id: string): Promise<AuthSession | null> {
		const session = await this.prisma.userSession.findUnique({
			where: {id},
		});

		return session ? this.mapToAuthSession(session) : null;
	}

	async findActiveSessionsByUserId(userId: string): Promise<AuthSession[]> {
		const sessions = await this.prisma.userSession.findMany({
			where: {
				userId,
				isActive: true,
				expiresAt: {
					gt: new Date(),
				},
			},
		});

		return sessions.map((session) => this.mapToAuthSession(session));
	}

	async updateSession(
		id: string,
		sessionData: Partial<AuthSession>
	): Promise<AuthSession> {
		const session = await this.prisma.userSession.update({
			where: {id},
			data: {
				refreshToken: sessionData.refreshToken,
				expiresAt: sessionData.expiresAt,
				ipAddress: sessionData.ipAddress,
				userAgent: sessionData.userAgent,
				isActive: sessionData.isActive,
			},
		});

		return this.mapToAuthSession(session);
	}

	async deleteSession(id: string): Promise<void> {
		await this.prisma.userSession.delete({
			where: {id},
		});
	}

	async deleteAllUserSessions(userId: string): Promise<void> {
		await this.prisma.userSession.deleteMany({
			where: {userId},
		});
	}

	async deleteExpiredSessions(): Promise<void> {
		await this.prisma.userSession.deleteMany({
			where: {
				expiresAt: {
					lt: new Date(),
				},
			},
		});
	}

	async invalidateRefreshToken(refreshToken: string): Promise<void> {
		await this.prisma.userSession.updateMany({
			where: {refreshToken},
			data: {isActive: false},
		});
	}

	async isRefreshTokenValid(refreshToken: string): Promise<boolean> {
		const session = await this.prisma.userSession.findFirst({
			where: {
				refreshToken,
				isActive: true,
				expiresAt: {
					gt: new Date(),
				},
			},
		});

		return !!session;
	}

	async findUserWithPassword(
		email: string
	): Promise<(User & {password: string}) | null> {
		const user = await this.prisma.user.findUnique({
			where: {email},
			select: {
				id: true,
				email: true,
				name: true,
				password: true,
				role: true,
				isActive: true,
				isVerified: true,
				emailVerified: true,
				avatar: true,
				phone: true,
				createdAt: true,
				updatedAt: true,
			},
		});

		if (!user) return null;

		return {
			id: user.id,
			email: user.email,
			name: user.name,
			password: user.password!,
			role: user.role,
			isActive: user.isActive,
			isVerified: user.isVerified,
			emailVerified: user.emailVerified,
			avatar: user.avatar,
			phone: user.phone,
			createdAt: user.createdAt,
			updatedAt: user.updatedAt,
		};
	}

	async updateUserPassword(
		userId: string,
		hashedPassword: string
	): Promise<void> {
		await this.prisma.user.update({
			where: {id: userId},
			data: {password: hashedPassword},
		});
	}

	async recordLoginAttempt(
		userId: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void> {
		// For now, we'll just log the attempt
		// In a real implementation, you might want to store this in a separate table
		console.log(
			`Failed login attempt for user ${userId} from ${ipAddress} with ${userAgent}`
		);
	}

	async getFailedLoginAttempts(
		userId: string,
		timeWindow: number
	): Promise<number> {
		// For now, return 0 (no rate limiting implemented)
		// In a real implementation, you would query a login attempts table
		return 0;
	}

	async clearFailedLoginAttempts(userId: string): Promise<void> {
		// For now, do nothing
		// In a real implementation, you would clear failed attempts from a table
	}

	// Helper methods for mapping Prisma models to domain types
	private mapToAuthSession(prismaSession: any): AuthSession {
		return {
			id: prismaSession.id,
			userId: prismaSession.userId,
			refreshToken: prismaSession.refreshToken,
			expiresAt: prismaSession.expiresAt,
			ipAddress: prismaSession.ipAddress,
			userAgent: prismaSession.userAgent,
			isActive: prismaSession.isActive,
		};
	}
}
