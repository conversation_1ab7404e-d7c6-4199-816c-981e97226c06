import {Request, Response} from 'express';
import {IAuthService} from '@/services/interfaces/auth.service';
import {
	LoginSchema,
	RegisterSchema,
	GoogleAuthSchema,
	RefreshTokenSchema,
	ChangePasswordSchema,
} from '@/types/auth.types';
import {ApiResponse} from '@/types/common.types';

export class AuthController {
	constructor(private authService: IAuthService) {}

	async register(req: Request, res: Response): Promise<void> {
		try {
			// Validate request body
			const validatedData = RegisterSchema.parse(req.body);

			// Register user
			const user = await this.authService.registerUser(validatedData);

			const response: ApiResponse = {
				success: true,
				data: user,
				message: 'User registered successfully',
			};

			res.status(201).json(response);
		} catch (error) {
			this.handleError(error, res);
		}
	}

	async login(req: Request, res: Response): Promise<void> {
		try {
			// Validate request body
			const validatedData = LoginSchema.parse(req.body);

			// Get client information
			const ipAddress = req.ip || req.connection.remoteAddress;
			const userAgent = req.get('User-Agent');

			// Login user
			const result = await this.authService.loginUser(
				validatedData,
				ipAddress,
				userAgent
			);

			const response: ApiResponse = {
				success: true,
				data: result,
				message: 'Login successful',
			};

			res.status(200).json(response);
		} catch (error) {
			this.handleError(error, res);
		}
	}

	async loginWithGoogle(req: Request, res: Response): Promise<void> {
		try {
			// Validate request body
			const validatedData = GoogleAuthSchema.parse(req.body);

			// Get client information
			const ipAddress = req.ip || req.connection.remoteAddress;
			const userAgent = req.get('User-Agent');

			// Login with Google
			const result = await this.authService.loginWithGoogle(
				validatedData.token,
				ipAddress,
				userAgent
			);

			const response: ApiResponse = {
				success: true,
				data: result,
				message: 'Google login successful',
			};

			res.status(200).json(response);
		} catch (error) {
			this.handleError(error, res);
		}
	}

	async logout(req: Request, res: Response): Promise<void> {
		try {
			// Get refresh token from request body or headers
			const refreshToken =
				req.body.refreshToken || (req.headers['x-refresh-token'] as string);

			if (!refreshToken) {
				res.status(400).json({
					success: false,
					message: 'Refresh token is required',
				});
				return;
			}

			// Logout user
			await this.authService.logoutUser(refreshToken);

			const response: ApiResponse = {
				success: true,
				message: 'Logout successful',
			};

			res.status(200).json(response);
		} catch (error) {
			this.handleError(error, res);
		}
	}

	async refreshToken(req: Request, res: Response): Promise<void> {
		try {
			// Validate request body
			const validatedData = RefreshTokenSchema.parse(req.body);

			// Refresh token
			const tokens = await this.authService.refreshToken(
				validatedData.refreshToken
			);

			const response: ApiResponse = {
				success: true,
				data: tokens,
				message: 'Token refreshed successfully',
			};

			res.status(200).json(response);
		} catch (error) {
			this.handleError(error, res);
		}
	}

	async changePassword(req: Request, res: Response): Promise<void> {
		try {
			// Validate request body
			const validatedData = ChangePasswordSchema.parse(req.body);

			// Get user ID from authenticated request
			const userId = (req as any).user?.userId;
			if (!userId) {
				res.status(401).json({
					success: false,
					message: 'User not authenticated',
				});
				return;
			}

			// Change password
			await this.authService.changePassword(userId, validatedData);

			const response: ApiResponse = {
				success: true,
				message: 'Password changed successfully',
			};

			res.status(200).json(response);
		} catch (error) {
			this.handleError(error, res);
		}
	}

	async getCurrentUser(req: Request, res: Response): Promise<void> {
		try {
			// Get user from authenticated request
			const user = (req as any).user;
			if (!user) {
				res.status(401).json({
					success: false,
					message: 'User not authenticated',
				});
				return;
			}

			const response: ApiResponse = {
				success: true,
				data: {
					id: user.userId,
					email: user.email,
					role: user.role,
				},
				message: 'Current user retrieved successfully',
			};

			res.status(200).json(response);
		} catch (error) {
			this.handleError(error, res);
		}
	}

	private handleError(error: any, res: Response): void {
		console.error('Auth controller error:', error);

		if (error.name === 'ZodError') {
			res.status(400).json({
				success: false,
				message: 'Validation error',
				errors: error.errors.map(
					(err: any) => `${err.path.join('.')}: ${err.message}`
				),
			});
			return;
		}

		if (error.statusCode) {
			res.status(error.statusCode).json({
				success: false,
				message: error.message,
			});
			return;
		}

		res.status(500).json({
			success: false,
			message: 'Internal server error',
		});
	}
}
