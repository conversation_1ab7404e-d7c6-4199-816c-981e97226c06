// Validation Utilities - Following DRY and SRP principles
import { body, param, query, Validation<PERSON>hain } from 'express-validator';
import { APP_CONFIG, ERROR_MESSAGES } from '@/constants';

// Base validation rules (SRP - Single responsibility for each validator)
export const ValidationRules = {
  // Common validations
  email: () => body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage(ERROR_MESSAGES.INVALID_EMAIL),
  
  password: () => body('password')
    .isLength({ min: APP_CONFIG.MIN_PASSWORD_LENGTH })
    .withMessage(ERROR_MESSAGES.INVALID_PASSWORD),
  
  name: () => body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters'),
  
  id: (field: string = 'id') => param(field)
    .isUUID()
    .withMessage('Invalid ID format'),
  
  // Pagination validations
  page: () => query('page')
    .optional()
    .isInt({ min: 1 })
    .toInt()
    .withMessage('Page must be a positive integer'),
  
  limit: () => query('limit')
    .optional()
    .isInt({ min: 1, max: APP_CONFIG.MAX_PAGE_SIZE })
    .toInt()
    .withMessage(`Limit must be between 1 and ${APP_CONFIG.MAX_PAGE_SIZE}`),
  
  // Service validations
  serviceName: () => body('name')
    .trim()
    .isLength({ min: 3, max: 200 })
    .withMessage('Service name must be between 3 and 200 characters'),
  
  serviceDescription: () => body('description')
    .trim()
    .isLength({ min: 10, max: APP_CONFIG.MAX_DESCRIPTION_LENGTH })
    .withMessage(`Description must be between 10 and ${APP_CONFIG.MAX_DESCRIPTION_LENGTH} characters`),
  
  serviceCategory: () => body('category')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Category is required'),
  
  price: () => body('price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Price must be a positive number'),
  
  // Rating validations
  rating: () => body('rating')
    .isInt({ min: APP_CONFIG.MIN_RATING, max: APP_CONFIG.MAX_RATING })
    .withMessage(`Rating must be between ${APP_CONFIG.MIN_RATING} and ${APP_CONFIG.MAX_RATING}`),
  
  reviewComment: () => body('comment')
    .optional()
    .trim()
    .isLength({ max: APP_CONFIG.MAX_REVIEW_LENGTH })
    .withMessage(`Comment must not exceed ${APP_CONFIG.MAX_REVIEW_LENGTH} characters`),
  
  // Provider validations
  businessName: () => body('businessName')
    .trim()
    .isLength({ min: 2, max: 200 })
    .withMessage('Business name must be between 2 and 200 characters'),
  
  phone: () => body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Invalid phone number format'),
  
  website: () => body('website')
    .optional()
    .isURL()
    .withMessage('Invalid website URL'),
  
  // Address validations
  address: () => [
    body('address.street').trim().isLength({ min: 1 }).withMessage('Street address is required'),
    body('address.city').trim().isLength({ min: 1 }).withMessage('City is required'),
    body('address.state').trim().isLength({ min: 1 }).withMessage('State is required'),
    body('address.zipCode').trim().isLength({ min: 1 }).withMessage('ZIP code is required'),
    body('address.country').trim().isLength({ min: 1 }).withMessage('Country is required'),
  ],
};

// Composite validation chains (Open/Closed - open for extension)
export const ValidationChains = {
  // Authentication
  register: (): ValidationChain[] => [
    ValidationRules.name(),
    ValidationRules.email(),
    ValidationRules.password(),
    body('role')
      .optional()
      .isIn(['user', 'provider'])
      .withMessage('Role must be either user or provider'),
  ],
  
  login: (): ValidationChain[] => [
    ValidationRules.email(),
    ValidationRules.password(),
  ],
  
  // Service management
  createService: (): ValidationChain[] => [
    ValidationRules.serviceName(),
    ValidationRules.serviceDescription(),
    ValidationRules.serviceCategory(),
    ValidationRules.price(),
    body('priceType')
      .isIn(['fixed', 'starting_at', 'quote_required'])
      .withMessage('Invalid price type'),
  ],
  
  updateService: (): ValidationChain[] => [
    ValidationRules.id(),
    ValidationRules.serviceName().optional(),
    ValidationRules.serviceDescription().optional(),
    ValidationRules.serviceCategory().optional(),
    ValidationRules.price(),
  ],
  
  // Provider management
  providerRegistration: (): ValidationChain[] => [
    ValidationRules.businessName(),
    ValidationRules.email(),
    ValidationRules.phone(),
    ValidationRules.website(),
    ...ValidationRules.address(),
  ],
  
  // Review management
  createReview: (): ValidationChain[] => [
    ValidationRules.id('orderId'),
    ValidationRules.rating(),
    ValidationRules.reviewComment(),
    body('ratings.quality').isInt({ min: 1, max: 5 }).withMessage('Quality rating required'),
    body('ratings.delivery').isInt({ min: 1, max: 5 }).withMessage('Delivery rating required'),
    body('ratings.communication').isInt({ min: 1, max: 5 }).withMessage('Communication rating required'),
    body('ratings.value').isInt({ min: 1, max: 5 }).withMessage('Value rating required'),
  ],
  
  // Search and filtering
  serviceSearch: (): ValidationChain[] => [
    query('query').optional().trim().isLength({ max: 200 }),
    query('category').optional().trim(),
    query('priceMin').optional().isFloat({ min: 0 }).toFloat(),
    query('priceMax').optional().isFloat({ min: 0 }).toFloat(),
    ValidationRules.page(),
    ValidationRules.limit(),
    query('sortBy')
      .optional()
      .isIn(['relevance', 'price', 'rating', 'popular', 'newest'])
      .withMessage('Invalid sort option'),
    query('sortOrder')
      .optional()
      .isIn(['asc', 'desc'])
      .withMessage('Sort order must be asc or desc'),
  ],
  
  // Pagination
  pagination: (): ValidationChain[] => [
    ValidationRules.page(),
    ValidationRules.limit(),
  ],
};

// Password strength validation
export const validatePasswordStrength = (password: string): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  if (password.length < APP_CONFIG.MIN_PASSWORD_LENGTH) {
    errors.push(`Password must be at least ${APP_CONFIG.MIN_PASSWORD_LENGTH} characters long`);
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }
  
  if (!/(?=.*\d)/.test(password)) {
    errors.push('Password must contain at least one number');
  }
  
  if (!/(?=.*[@$!%*?&])/.test(password)) {
    errors.push('Password must contain at least one special character');
  }
  
  return {
    isValid: errors.length === 0,
    errors,
  };
};

// File validation
export const validateFileUpload = (file: Express.Multer.File, allowedTypes: string[]): { isValid: boolean; error?: string } => {
  if (!file) {
    return { isValid: false, error: 'No file provided' };
  }
  
  if (file.size > APP_CONFIG.MAX_FILE_SIZE) {
    return { isValid: false, error: ERROR_MESSAGES.FILE_TOO_LARGE };
  }
  
  if (!allowedTypes.includes(file.mimetype)) {
    return { isValid: false, error: ERROR_MESSAGES.INVALID_FILE_TYPE };
  }
  
  return { isValid: true };
};