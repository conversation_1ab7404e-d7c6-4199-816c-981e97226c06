// Cryptographic utilities - SRP: Single responsibility for each crypto function
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import { TokenPayload } from '@/types/auth';
import { APP_CONFIG } from '@/constants';

// Password hashing (SRP)
export class PasswordService {
  private static readonly SALT_ROUNDS = 12;
  
  static async hash(password: string): Promise<string> {
    return bcrypt.hash(password, this.SALT_ROUNDS);
  }
  
  static async compare(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }
}

// JWT token management (SRP)
export class TokenService {
  private static readonly JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
  private static readonly REFRESH_SECRET = process.env.REFRESH_SECRET || 'your-super-secret-refresh-key';
  
  static generateAccessToken(payload: Omit<TokenPayload, 'iat' | 'exp'>): string {
    return jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: APP_CONFIG.JWT_EXPIRES_IN,
      issuer: APP_CONFIG.NAME,
      audience: APP_CONFIG.NAME,
    });
  }
  
  static generateRefreshToken(userId: string): string {
    return jwt.sign({ userId }, this.REFRESH_SECRET, {
      expiresIn: APP_CONFIG.REFRESH_TOKEN_EXPIRES_IN,
      issuer: APP_CONFIG.NAME,
      audience: APP_CONFIG.NAME,
    });
  }
  
  static verifyAccessToken(token: string): TokenPayload {
    return jwt.verify(token, this.JWT_SECRET, {
      issuer: APP_CONFIG.NAME,
      audience: APP_CONFIG.NAME,
    }) as TokenPayload;
  }
  
  static verifyRefreshToken(token: string): { userId: string } {
    return jwt.verify(token, this.REFRESH_SECRET, {
      issuer: APP_CONFIG.NAME,
      audience: APP_CONFIG.NAME,
    }) as { userId: string };
  }
  
  static extractTokenFromHeader(authHeader?: string): string | null {
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return null;
    }
    return authHeader.substring(7);
  }
}

// Random token generation (SRP)
export class RandomTokenService {
  static generateSecureToken(length: number = 32): string {
    return crypto.randomBytes(length).toString('hex');
  }
  
  static generateNumericCode(digits: number = 6): string {
    const min = Math.pow(10, digits - 1);
    const max = Math.pow(10, digits) - 1;
    return Math.floor(Math.random() * (max - min + 1) + min).toString();
  }
  
  static generateUUID(): string {
    return crypto.randomUUID();
  }
}

// Data encryption/decryption (SRP)
export class EncryptionService {
  private static readonly ALGORITHM = 'aes-256-gcm';
  private static readonly KEY = crypto.scryptSync(
    process.env.ENCRYPTION_KEY || 'your-encryption-key',
    'salt',
    32
  );
  
  static encrypt(text: string): { encrypted: string; iv: string; tag: string } {
    const iv = crypto.randomBytes(16);
    const cipher = crypto.createCipher(this.ALGORITHM, this.KEY);
    cipher.setAAD(Buffer.from('additional-data'));
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    return {
      encrypted,
      iv: iv.toString('hex'),
      tag: tag.toString('hex'),
    };
  }
  
  static decrypt(encrypted: string, iv: string, tag: string): string {
    const decipher = crypto.createDecipher(this.ALGORITHM, this.KEY);
    decipher.setAAD(Buffer.from('additional-data'));
    decipher.setAuthTag(Buffer.from(tag, 'hex'));
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  }
}

// Hash generation for non-sensitive data (SRP)
export class HashService {
  static generateMD5(data: string): string {
    return crypto.createHash('md5').update(data).digest('hex');
  }
  
  static generateSHA256(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex');
  }
  
  static generateHMAC(data: string, secret: string): string {
    return crypto.createHmac('sha256', secret).update(data).digest('hex');
  }
}

// Email verification token service (SRP)
export class EmailVerificationService {
  static generateVerificationToken(): { token: string; expiresAt: Date } {
    const token = RandomTokenService.generateSecureToken(32);
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 24); // 24 hours from now
    
    return { token, expiresAt };
  }
  
  static isTokenExpired(expiresAt: Date): boolean {
    return new Date() > expiresAt;
  }
}

// Password reset token service (SRP)
export class PasswordResetService {
  static generateResetToken(): { token: string; expiresAt: Date } {
    const token = RandomTokenService.generateSecureToken(32);
    const expiresAt = new Date();
    expiresAt.setHours(expiresAt.getHours() + 1); // 1 hour from now
    
    return { token, expiresAt };
  }
  
  static isTokenExpired(expiresAt: Date): boolean {
    return new Date() > expiresAt;
  }
}