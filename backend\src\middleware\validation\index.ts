// Validation middleware - Following SRP principle
import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { ResponseFormatter } from '@/utils/formatting';
import { HTTP_STATUS } from '@/constants';

// Export validation chains and handlers
export { ValidationChains, ValidationRules } from './validation.chains';
export { handleValidation } from './validator';

// Main validation middleware (SRP)
export const validateRequest = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.param || 'unknown',
      message: error.msg,
      value: error.value,
    }));
    
    res.status(HTTP_STATUS.VALIDATION_ERROR).json(
      ResponseFormatter.error(
        'Validation failed',
        'One or more fields contain invalid data',
        HTTP_STATUS.VALIDATION_ERROR,
        req.path
      )
    );
    return;
  }
  
  next();
};

// Sanitization middleware factory (Open/Closed principle)
export const sanitizeFields = (fields: string[]) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    fields.forEach(field => {
      if (req.body[field] && typeof req.body[field] === 'string') {
        req.body[field] = req.body[field].trim();
      }
    });
    next();
  };
};

// File upload validation middleware
export const validateFileUpload = (
  fieldName: string,
  allowedTypes: string[] = [],
  maxSize: number = 5 * 1024 * 1024 // 5MB default
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const file = req.file;
    
    if (!file && req.body.required !== false) {
      res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseFormatter.error(
          'File required',
          `${fieldName} is required`,
          HTTP_STATUS.BAD_REQUEST,
          req.path
        )
      );
      return;
    }
    
    if (file) {
      // Check file size
      if (file.size > maxSize) {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'File too large',
            `File size must not exceed ${maxSize / (1024 * 1024)}MB`,
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
        return;
      }
      
      // Check file type
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Invalid file type',
            `Allowed types: ${allowedTypes.join(', ')}`,
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
        return;
      }
    }
    
    next();
  };
};

// Multiple file upload validation
export const validateMultipleFileUpload = (
  fieldName: string,
  maxFiles: number = 5,
  allowedTypes: string[] = [],
  maxSizePerFile: number = 5 * 1024 * 1024
) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const files = req.files as Express.Multer.File[];
    
    if (!files || files.length === 0) {
      res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseFormatter.error(
          'Files required',
          `At least one ${fieldName} is required`,
          HTTP_STATUS.BAD_REQUEST,
          req.path
        )
      );
      return;
    }
    
    if (files.length > maxFiles) {
      res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseFormatter.error(
          'Too many files',
          `Maximum ${maxFiles} files allowed`,
          HTTP_STATUS.BAD_REQUEST,
          req.path
        )
      );
      return;
    }
    
    // Validate each file
    for (const file of files) {
      if (file.size > maxSizePerFile) {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'File too large',
            `Each file must not exceed ${maxSizePerFile / (1024 * 1024)}MB`,
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
        return;
      }
      
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.mimetype)) {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Invalid file type',
            `Allowed types: ${allowedTypes.join(', ')}`,
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
        return;
      }
    }
    
    next();
  };
};

// JSON schema validation middleware
export const validateJSONSchema = (schema: any) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    // This would typically use a library like Joi or ajv
    // For now, we'll implement basic type checking
    
    try {
      // Basic validation - in a real implementation, use a proper schema validator
      if (typeof req.body !== 'object') {
        throw new Error('Request body must be an object');
      }
      
      next();
    } catch (error) {
      res.status(HTTP_STATUS.VALIDATION_ERROR).json(
        ResponseFormatter.error(
          'Schema validation failed',
          error instanceof Error ? error.message : 'Invalid data format',
          HTTP_STATUS.VALIDATION_ERROR,
          req.path
        )
      );
    }
  };
};

// Pagination validation middleware
export const validatePagination = (req: Request, res: Response, next: NextFunction): void => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  
  if (page < 1) {
    res.status(HTTP_STATUS.BAD_REQUEST).json(
      ResponseFormatter.error(
        'Invalid pagination',
        'Page must be greater than 0',
        HTTP_STATUS.BAD_REQUEST,
        req.path
      )
    );
    return;
  }
  
  if (limit < 1 || limit > 100) {
    res.status(HTTP_STATUS.BAD_REQUEST).json(
      ResponseFormatter.error(
        'Invalid pagination',
        'Limit must be between 1 and 100',
        HTTP_STATUS.BAD_REQUEST,
        req.path
      )
    );
    return;
  }
  
  // Add validated pagination to request
  req.query.page = page.toString();
  req.query.limit = limit.toString();
  
  next();
};