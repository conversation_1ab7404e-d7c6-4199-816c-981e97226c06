"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceMetadata = exports.ServiceRating = exports.ServicePrice = exports.ServiceModel = void 0;
// Service Models Index
var Service_model_1 = require("./Service.model");
Object.defineProperty(exports, "ServiceModel", { enumerable: true, get: function () { return Service_model_1.ServiceModel; } });
Object.defineProperty(exports, "ServicePrice", { enumerable: true, get: function () { return Service_model_1.ServicePrice; } });
Object.defineProperty(exports, "ServiceRating", { enumerable: true, get: function () { return Service_model_1.ServiceRating; } });
Object.defineProperty(exports, "ServiceMetadata", { enumerable: true, get: function () { return Service_model_1.ServiceMetadata; } });
//# sourceMappingURL=index.js.map