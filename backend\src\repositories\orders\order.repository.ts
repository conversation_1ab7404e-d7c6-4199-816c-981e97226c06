// Order Repository - Following established patterns and SOLID principles
import {PrismaClient, Order, OrderStatus, PaymentStatus} from '@prisma/client';
import {BaseRepository} from '@/repositories/base/base.repository';
import {
	CreateOrderRequest,
	UpdateOrderRequest,
	OrderSearchRequest,
	OrderFilters,
	OrderSearchResult,
	OrderWithMetrics,
	OrderMetrics,
	OrderStatistics,
	OrderWithRelations,
} from '@/types/orders';
import {PaginatedResult} from '@/types/common';

// Order Repository Interface (DIP - Dependency Inversion Principle)
export interface IOrderRepository {
	create(
		orderData: CreateOrderRequest,
		userId: string,
		serviceId: string,
		providerId: string
	): Promise<Order>;
	findById(id: string): Promise<OrderWithRelations | null>;
	findByOrderNumber(orderNumber: string): Promise<OrderWithRelations | null>;
	findByUserId(userId: string): Promise<OrderWithRelations[]>;
	findByProviderId(providerId: string): Promise<OrderWithRelations[]>;
	findByServiceId(serviceId: string): Promise<OrderWithRelations[]>;
	update(
		id: string,
		orderData: UpdateOrderRequest
	): Promise<OrderWithRelations>;
	delete(id: string): Promise<void>;
	search(params: OrderSearchRequest): Promise<OrderSearchResult>;
	updateStatus(
		id: string,
		status: OrderStatus,
		notes?: string
	): Promise<OrderWithRelations>;
	updatePaymentStatus(
		id: string,
		paymentStatus: PaymentStatus,
		paymentMethod?: string
	): Promise<OrderWithRelations>;
	getOrderMetrics(userId?: string, providerId?: string): Promise<OrderMetrics>;
	getOrderStatistics(): Promise<OrderStatistics>;
	getRecentOrders(limit?: number): Promise<OrderWithRelations[]>;
	getOverdueOrders(): Promise<OrderWithRelations[]>;
	generateOrderNumber(): Promise<string>;
	incrementServiceOrderCount(serviceId: string): Promise<void>;
	incrementProviderOrderCount(providerId: string): Promise<void>;
}

// Order Repository Implementation (SRP - Single Responsibility Principle)
export class OrderRepository
	extends BaseRepository
	implements IOrderRepository
{
	constructor(private prisma: PrismaClient) {
		super();
	}

	// Create a new order (SRP)
	async create(
		orderData: CreateOrderRequest,
		userId: string,
		serviceId: string,
		providerId: string
	): Promise<Order> {
		// Get service to calculate total amount
		const service = await this.prisma.service.findUnique({
			where: {id: serviceId},
			select: {price: true, priceType: true},
		});

		if (!service) {
			throw new Error('Service not found');
		}

		const orderNumber = await this.generateOrderNumber();
		const totalAmount = service.price * (orderData.quantity || 1);

		return this.prisma.order.create({
			data: {
				orderNumber,
				serviceId,
				userId,
				providerId,
				status: 'PENDING',
				totalAmount,
				paymentStatus: 'PENDING',
				customerNotes: orderData.customerNotes,
				scheduledDate: orderData.scheduledDate,
				files: orderData.files
					? orderData.files.map((file) => file.filename)
					: [],
			},
		});
	}

	// Find order by ID (SRP)
	async findById(id: string): Promise<OrderWithRelations | null> {
		return this.prisma.order.findUnique({
			where: {id},
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
		});
	}

	// Find order by order number (SRP)
	async findByOrderNumber(
		orderNumber: string
	): Promise<OrderWithRelations | null> {
		return this.prisma.order.findUnique({
			where: {orderNumber},
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
		});
	}

	// Find orders by user ID (SRP)
	async findByUserId(userId: string): Promise<OrderWithRelations[]> {
		return this.prisma.order.findMany({
			where: {userId},
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
			orderBy: {createdAt: 'desc'},
		});
	}

	// Find orders by provider ID (SRP)
	async findByProviderId(providerId: string): Promise<OrderWithRelations[]> {
		return this.prisma.order.findMany({
			where: {providerId},
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
			orderBy: {createdAt: 'desc'},
		});
	}

	// Find orders by service ID (SRP)
	async findByServiceId(serviceId: string): Promise<OrderWithRelations[]> {
		return this.prisma.order.findMany({
			where: {serviceId},
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
			orderBy: {createdAt: 'desc'},
		});
	}

	// Update order (SRP)
	async update(
		id: string,
		orderData: UpdateOrderRequest
	): Promise<OrderWithRelations> {
		const updateData: any = {};

		if (orderData.customerNotes !== undefined)
			updateData.customerNotes = orderData.customerNotes;
		if (orderData.providerNotes !== undefined)
			updateData.providerNotes = orderData.providerNotes;
		if (orderData.scheduledDate !== undefined)
			updateData.scheduledDate = orderData.scheduledDate;
		if (orderData.status !== undefined) updateData.status = orderData.status;
		if (orderData.paymentStatus !== undefined)
			updateData.paymentStatus = orderData.paymentStatus;
		if (orderData.cancellationReason !== undefined)
			updateData.cancellationReason = orderData.cancellationReason;

		// Handle status-specific date updates
		if (orderData.status === 'COMPLETED') {
			updateData.completedDate = new Date();
		} else if (orderData.status === 'CANCELLED') {
			updateData.cancelledDate = new Date();
		}

		// Handle file uploads
		if (orderData.files && orderData.files.length > 0) {
			const existingOrder = await this.prisma.order.findUnique({
				where: {id},
				select: {files: true},
			});
			const newFiles = orderData.files.map((file) => file.filename);
			updateData.files = [...(existingOrder?.files || []), ...newFiles];
		}

		return this.prisma.order.update({
			where: {id},
			data: updateData,
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
		});
	}

	// Delete order (SRP)
	async delete(id: string): Promise<void> {
		await this.prisma.order.delete({
			where: {id},
		});
	}

	// Search orders with filters and pagination (SRP)
	async search(params: OrderSearchRequest): Promise<OrderSearchResult> {
		const {query, filters, pagination} = params;
		const page = pagination?.page || 1;
		const limit = pagination?.limit || 10;
		const skip = (page - 1) * limit;

		// Build where clause based on filters
		const where: any = {};

		if (query) {
			where.OR = [
				{orderNumber: {contains: query, mode: 'insensitive'}},
				{customerNotes: {contains: query, mode: 'insensitive'}},
				{providerNotes: {contains: query, mode: 'insensitive'}},
				{service: {name: {contains: query, mode: 'insensitive'}}},
				{user: {name: {contains: query, mode: 'insensitive'}}},
				{provider: {businessName: {contains: query, mode: 'insensitive'}}},
			];
		}

		if (filters) {
			if (filters.status && filters.status.length > 0) {
				where.status = {in: filters.status};
			}
			if (filters.paymentStatus && filters.paymentStatus.length > 0) {
				where.paymentStatus = {in: filters.paymentStatus};
			}
			if (filters.serviceId) {
				where.serviceId = filters.serviceId;
			}
			if (filters.providerId) {
				where.providerId = filters.providerId;
			}
			if (filters.userId) {
				where.userId = filters.userId;
			}
			if (filters.minAmount) {
				where.totalAmount = {gte: filters.minAmount};
			}
			if (filters.maxAmount) {
				where.totalAmount = {...where.totalAmount, lte: filters.maxAmount};
			}
			if (filters.scheduledDateFrom) {
				where.scheduledDate = {gte: filters.scheduledDateFrom};
			}
			if (filters.scheduledDateTo) {
				where.scheduledDate = {
					...where.scheduledDate,
					lte: filters.scheduledDateTo,
				};
			}
			if (filters.createdAtFrom) {
				where.createdAt = {gte: filters.createdAtFrom};
			}
			if (filters.createdAtTo) {
				where.createdAt = {...where.createdAt, lte: filters.createdAtTo};
			}
		}

		// Get total count
		const total = await this.prisma.order.count({where});

		// Get orders with pagination
		const orders = await this.prisma.order.findMany({
			where,
			skip,
			take: limit,
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
			orderBy: this.buildOrderBy(filters),
		});

		// Calculate metrics for each order
		const ordersWithMetrics = await Promise.all(
			orders.map(async (order) => {
				const metrics = await this.calculateOrderMetrics(order);
				return {
					...order,
					metrics,
					timeline: [], // Would be populated from actual timeline data
				};
			})
		);

		return {
			orders: ordersWithMetrics,
			total,
			page,
			limit,
			totalPages: Math.ceil(total / limit),
			hasNext: page < Math.ceil(total / limit),
			hasPrev: page > 1,
		};
	}

	// Update order status (SRP)
	async updateStatus(
		id: string,
		status: OrderStatus,
		notes?: string
	): Promise<OrderWithRelations> {
		const updateData: any = {status};

		// Handle status-specific date updates
		if (status === 'COMPLETED') {
			updateData.completedDate = new Date();
		} else if (status === 'CANCELLED') {
			updateData.cancelledDate = new Date();
		}

		if (notes) {
			updateData.providerNotes = notes;
		}

		return this.prisma.order.update({
			where: {id},
			data: updateData,
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
		});
	}

	// Update payment status (SRP)
	async updatePaymentStatus(
		id: string,
		paymentStatus: PaymentStatus,
		paymentMethod?: string
	): Promise<OrderWithRelations> {
		const updateData: any = {paymentStatus};

		if (paymentMethod) {
			updateData.paymentMethod = paymentMethod;
		}

		return this.prisma.order.update({
			where: {id},
			data: updateData,
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
		});
	}

	// Get order metrics (SRP)
	async getOrderMetrics(
		userId?: string,
		providerId?: string
	): Promise<OrderMetrics> {
		const where: any = {};
		if (userId) where.userId = userId;
		if (providerId) where.providerId = providerId;

		const [
			totalOrders,
			ordersByStatus,
			ordersByPaymentStatus,
			totalRevenue,
			averageOrderValue,
			topServices,
		] = await Promise.all([
			this.prisma.order.count({where}),
			this.prisma.order.groupBy({
				by: ['status'],
				where,
				_count: {id: true},
			}),
			this.prisma.order.groupBy({
				by: ['paymentStatus'],
				where,
				_count: {id: true},
			}),
			this.prisma.order.aggregate({
				where,
				_sum: {totalAmount: true},
			}),
			this.prisma.order.aggregate({
				where,
				_avg: {totalAmount: true},
			}),
			this.prisma.order.groupBy({
				by: ['serviceId'],
				where,
				_count: {id: true},
				_sum: {totalAmount: true},
			}),
		]);

		const pendingOrders =
			ordersByStatus.find((o) => o.status === 'PENDING')?._count.id || 0;
		const confirmedOrders =
			ordersByStatus.find((o) => o.status === 'CONFIRMED')?._count.id || 0;
		const inProgressOrders =
			ordersByStatus.find((o) => o.status === 'IN_PROGRESS')?._count.id || 0;
		const completedOrders =
			ordersByStatus.find((o) => o.status === 'COMPLETED')?._count.id || 0;
		const cancelledOrders =
			ordersByStatus.find((o) => o.status === 'CANCELLED')?._count.id || 0;
		const refundedOrders =
			ordersByStatus.find((o) => o.status === 'REFUNDED')?._count.id || 0;

		return {
			totalOrders,
			pendingOrders,
			confirmedOrders,
			inProgressOrders,
			completedOrders,
			cancelledOrders,
			refundedOrders,
			totalRevenue: totalRevenue._sum.totalAmount || 0,
			averageOrderValue: averageOrderValue._avg.totalAmount || 0,
			completionRate:
				totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0,
			cancellationRate:
				totalOrders > 0 ? (cancelledOrders / totalOrders) * 100 : 0,
			averageProcessingTime: 24, // Placeholder - would need to calculate from actual data
			topServices: topServices.slice(0, 5).map((service) => ({
				serviceId: service.serviceId,
				serviceName: 'Service Name', // Would need to fetch from service table
				orderCount: service._count.id,
				revenue: service._sum.totalAmount || 0,
			})),
			monthlyStats: [], // Would need to implement monthly aggregation
		};
	}

	// Get order statistics (SRP)
	async getOrderStatistics(): Promise<OrderStatistics> {
		const [
			totalOrders,
			totalRevenue,
			averageOrderValue,
			ordersByStatus,
			ordersByPaymentStatus,
			topProviders,
			topServices,
		] = await Promise.all([
			this.prisma.order.count(),
			this.prisma.order.aggregate({
				_sum: {totalAmount: true},
			}),
			this.prisma.order.aggregate({
				_avg: {totalAmount: true},
			}),
			this.prisma.order.groupBy({
				by: ['status'],
				_count: {id: true},
			}),
			this.prisma.order.groupBy({
				by: ['paymentStatus'],
				_count: {id: true},
			}),
			this.prisma.order.groupBy({
				by: ['providerId'],
				_count: {id: true},
				_sum: {totalAmount: true},
			}),
			this.prisma.order.groupBy({
				by: ['serviceId'],
				_count: {id: true},
				_sum: {totalAmount: true},
			}),
		]);

		const completedOrders =
			ordersByStatus.find((o) => o.status === 'COMPLETED')?._count.id || 0;
		const cancelledOrders =
			ordersByStatus.find((o) => o.status === 'CANCELLED')?._count.id || 0;

		return {
			totalOrders,
			totalRevenue: totalRevenue._sum.totalAmount || 0,
			averageOrderValue: averageOrderValue._avg.totalAmount || 0,
			ordersByStatus: ordersByStatus.reduce((acc, order) => {
				acc[order.status] = order._count.id;
				return acc;
			}, {} as Record<OrderStatus, number>),
			ordersByPaymentStatus: ordersByPaymentStatus.reduce((acc, order) => {
				acc[order.paymentStatus] = order._count.id;
				return acc;
			}, {} as Record<PaymentStatus, number>),
			averageProcessingTime: 24, // Placeholder
			completionRate:
				totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0,
			cancellationRate:
				totalOrders > 0 ? (cancelledOrders / totalOrders) * 100 : 0,
			topProviders: topProviders.slice(0, 5).map((provider) => ({
				providerId: provider.providerId,
				providerName: 'Provider Name', // Would need to fetch from provider table
				orderCount: provider._count.id,
				revenue: provider._sum.totalAmount || 0,
			})),
			topServices: topServices.slice(0, 5).map((service) => ({
				serviceId: service.serviceId,
				serviceName: 'Service Name', // Would need to fetch from service table
				orderCount: service._count.id,
				revenue: service._sum.totalAmount || 0,
			})),
		};
	}

	// Get recent orders (SRP)
	async getRecentOrders(limit: number = 10): Promise<OrderWithRelations[]> {
		return this.prisma.order.findMany({
			take: limit,
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
			orderBy: {createdAt: 'desc'},
		});
	}

	// Get overdue orders (SRP)
	async getOverdueOrders(): Promise<OrderWithRelations[]> {
		const now = new Date();
		return this.prisma.order.findMany({
			where: {
				scheduledDate: {lt: now},
				status: {in: ['CONFIRMED', 'IN_PROGRESS']},
			},
			include: {
				service: {
					include: {
						provider: {
							select: {
								id: true,
								businessName: true,
								rating: true,
							},
						},
					},
				},
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				provider: {
					select: {
						id: true,
						businessName: true,
						rating: true,
						totalReviews: true,
					},
				},
				review: {
					select: {
						id: true,
						rating: true,
						comment: true,
						createdAt: true,
					},
				},
			},
			orderBy: {scheduledDate: 'asc'},
		});
	}

	// Generate unique order number (SRP)
	async generateOrderNumber(): Promise<string> {
		const timestamp = Date.now().toString();
		const random = Math.floor(Math.random() * 1000)
			.toString()
			.padStart(3, '0');
		return `ORD-${timestamp}-${random}`;
	}

	// Increment service order count (SRP)
	async incrementServiceOrderCount(serviceId: string): Promise<void> {
		await this.prisma.service.update({
			where: {id: serviceId},
			data: {
				totalOrders: {
					increment: 1,
				},
			},
		});
	}

	// Increment provider order count (SRP)
	async incrementProviderOrderCount(providerId: string): Promise<void> {
		await this.prisma.providerProfile.update({
			where: {id: providerId},
			data: {
				totalOrders: {
					increment: 1,
				},
			},
		});
	}

	// Helper method to calculate order metrics (SRP)
	private async calculateOrderMetrics(
		order: OrderWithRelations
	): Promise<OrderWithMetrics['metrics']> {
		const now = new Date();
		const processingTime = order.completedDate
			? (order.completedDate.getTime() - order.createdAt.getTime()) /
			  (1000 * 60 * 60) // hours
			: (now.getTime() - order.createdAt.getTime()) / (1000 * 60 * 60);

		const isOverdue = order.scheduledDate ? now > order.scheduledDate : false;
		const daysUntilScheduled = order.scheduledDate
			? Math.ceil(
					(order.scheduledDate.getTime() - now.getTime()) /
						(1000 * 60 * 60 * 24)
			  )
			: 0;

		const canBeCancelled = ['PENDING', 'CONFIRMED'].includes(order.status);
		const canBeRefunded = ['COMPLETED', 'CANCELLED'].includes(order.status);
		const refundAmount = canBeRefunded ? order.totalAmount : 0;

		return {
			processingTime,
			isOverdue,
			daysUntilScheduled,
			canBeCancelled,
			canBeRefunded,
			refundAmount,
		};
	}

	// Helper method to build order by clause (SRP)
	private buildOrderBy(filters?: OrderFilters): any {
		if (!filters?.sortBy) {
			return {createdAt: 'desc'};
		}

		const order = filters.sortOrder || 'desc';

		switch (filters.sortBy) {
			case 'orderNumber':
				return {orderNumber: order};
			case 'totalAmount':
				return {totalAmount: order};
			case 'status':
				return {status: order};
			case 'createdAt':
				return {createdAt: order};
			case 'scheduledDate':
				return {scheduledDate: order};
			case 'completedDate':
				return {completedDate: order};
			default:
				return {createdAt: 'desc'};
		}
	}
}
