import {z} from 'zod';

// Provider schemas
export const CreateProviderSchema = z.object({
	businessName: z.string().min(1, 'Business name is required'),
	description: z.string().optional(),
	logo: z.string().url().optional(),
	website: z.string().url().optional(),
	phone: z.string().optional(),
	isActive: z.boolean().default(true),
});

export const UpdateProviderSchema = CreateProviderSchema.partial();

export const CreateServiceAreaSchema = z.object({
	zipCode: z.string().min(1, 'ZIP code is required'),
	city: z.string().min(1, 'City is required'),
	state: z.string().min(1, 'State is required'),
	radius: z.number().min(1).max(100).default(25),
	isActive: z.boolean().default(true),
});

export const UpdateServiceAreaSchema = CreateServiceAreaSchema.partial();

export const CreateOperatingHourSchema = z.object({
	dayOfWeek: z.number().min(0).max(6),
	openTime: z
		.string()
		.regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
	closeTime: z
		.string()
		.regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
	isOpen: z.boolean().default(true),
});

export const UpdateOperatingHourSchema = CreateOperatingHourSchema.partial();

export const CreateProviderServiceSchema = z.object({
	serviceId: z.string().min(1, 'Service is required'),
	price: z.number().positive('Price must be positive'),
	description: z.string().optional(),
	isActive: z.boolean().default(true),
});

export const UpdateProviderServiceSchema =
	CreateProviderServiceSchema.partial();

export const ProviderSearchSchema = z.object({
	serviceId: z.string().optional(),
	location: z.string().optional(),
	radius: z.number().min(1).max(100).default(25),
	rating: z.number().min(1).max(5).optional(),
	isVerified: z.boolean().optional(),
	isActive: z.boolean().optional(),
});

// Provider types
export type CreateProviderRequest = z.infer<typeof CreateProviderSchema>;
export type UpdateProviderRequest = z.infer<typeof UpdateProviderSchema>;
export type CreateServiceAreaRequest = z.infer<typeof CreateServiceAreaSchema>;
export type UpdateServiceAreaRequest = z.infer<typeof UpdateServiceAreaSchema>;
export type CreateOperatingHourRequest = z.infer<
	typeof CreateOperatingHourSchema
>;
export type UpdateOperatingHourRequest = z.infer<
	typeof UpdateOperatingHourSchema
>;
export type CreateProviderServiceRequest = z.infer<
	typeof CreateProviderServiceSchema
>;
export type UpdateProviderServiceRequest = z.infer<
	typeof UpdateProviderServiceSchema
>;
export type ProviderSearchRequest = z.infer<typeof ProviderSearchSchema>;

export interface Provider {
	id: string;
	userId: string;
	businessName: string;
	description?: string;
	logo?: string;
	website?: string;
	phone?: string;
	isVerified: boolean;
	isActive: boolean;
	rating: number;
	reviewCount: number;
	createdAt: Date;
	updatedAt: Date;
	user: {
		id: string;
		name: string;
		email: string;
	};
}

export interface ProviderServiceArea {
	id: string;
	providerId: string;
	zipCode: string;
	city: string;
	state: string;
	radius: number;
	isActive: boolean;
	createdAt: Date;
}

export interface ProviderOperatingHour {
	id: string;
	providerId: string;
	dayOfWeek: number;
	openTime: string;
	closeTime: string;
	isOpen: boolean;
	createdAt: Date;
}

export interface ProviderService {
	id: string;
	providerId: string;
	serviceId: string;
	price: number;
	description?: string;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
	service: {
		id: string;
		name: string;
		description: string;
	};
}

export interface ProviderWithDetails extends Provider {
	serviceAreas: ProviderServiceArea[];
	operatingHours: ProviderOperatingHour[];
	services: ProviderService[];
}

export interface ProviderSearchResult {
	provider: Provider;
	distance?: number;
	rating: number;
	reviewCount: number;
	services: ProviderService[];
}

export interface ProviderStats {
	totalOrders: number;
	completedOrders: number;
	pendingOrders: number;
	totalRevenue: number;
	averageRating: number;
	reviewCount: number;
	activeServices: number;
}
