import React from 'react';
import { Link } from 'react-router-dom';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  Clock, 
  Shield, 
  Award,
  ChevronDown,
  ChevronUp,
  Star,
  Palette,
  Lightbulb,
  Users,
  Zap,
  ArrowLeft,
  Brush,
  Target,
  Eye,
  Layers,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

const IllustratorArt: React.FC = () => {
  const [openFaq, setOpenFaq] = React.useState<number | null>(null);
  const [portfolioScrollPosition, setPortfolioScrollPosition] = React.useState(0);
  const portfolioScrollRef = React.useRef<HTMLDivElement>(null);

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const scrollPortfolio = (direction: 'left' | 'right') => {
    if (portfolioScrollRef.current) {
      const scrollAmount = 280; // Width of card + gap
      const currentScroll = portfolioScrollRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      portfolioScrollRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
      setPortfolioScrollPosition(newScroll);
    }
  };

  const handlePortfolioScroll = () => {
    if (portfolioScrollRef.current) {
      setPortfolioScrollPosition(portfolioScrollRef.current.scrollLeft);
    }
  };

  const faqs = [
    {
      question: "What types of illustrations do you create?",
      answer: "We create custom digital illustrations including character designs, mascots, icons, infographics, editorial illustrations, book illustrations, and artistic graphics for marketing materials."
    },
    {
      question: "What illustration styles do you offer?",
      answer: "We work in various styles including minimalist, cartoon, realistic, abstract, vintage, modern, hand-drawn, and vector illustrations. We can match your brand aesthetic or create something completely unique."
    },
    {
      question: "What file formats will I receive?",
      answer: "You'll receive high-resolution files in multiple formats including PNG, JPG, SVG (for vector illustrations), and original source files (AI, PSD) for future editing and scaling."
    },
    {
      question: "Can you create illustrations for both print and digital use?",
      answer: "Absolutely! We create illustrations optimized for both print (high-resolution, CMYK) and digital use (web-optimized, RGB). You'll receive files suitable for all applications."
    },
    {
      question: "How do you ensure the illustration matches my vision?",
      answer: "We start with a detailed consultation to understand your vision, then provide initial sketches and concepts. We work through revisions until the illustration perfectly captures what you're looking for."
    },
    {
      question: "Can you create illustrations that match my existing brand style?",
      answer: "Yes! We can work within your existing brand guidelines and style preferences, or help develop a new illustration style that complements your brand identity."
    }
  ];

  const portfolioSamples = [
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75",
      title: "Character Mascot Design",
      description: "Custom mascot character for brand identity and marketing"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75",
      title: "Editorial Illustration",
      description: "Magazine article illustration with modern artistic style"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75",
      title: "Infographic Design",
      description: "Data visualization with custom illustrations and icons"
    },
    {
      image: "https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75",
      title: "Book Illustration",
      description: "Children's book illustrations with whimsical characters"
    }
  ];

  const designProcess = [
    {
      step: "01",
      title: "Concept & Brief",
      description: "We discuss your vision, style preferences, and project requirements to create a detailed creative brief."
    },
    {
      step: "02",
      title: "Sketching & Ideation",
      description: "Our artists create initial sketches and concepts, exploring different approaches and compositions."
    },
    {
      step: "03",
      title: "Digital Creation",
      description: "We develop the chosen concept into a polished digital illustration using professional software and techniques."
    },
    {
      step: "04",
      title: "Refinement & Delivery",
      description: "Final adjustments are made based on your feedback, and you receive all files in multiple formats."
    }
  ];

  const features = [
    {
      icon: Brush,
      title: "Custom Artwork",
      description: "Unique illustrations created specifically for your brand and project needs"
    },
    {
      icon: Palette,
      title: "Versatile Styles",
      description: "From minimalist to detailed, we adapt our style to match your vision"
    },
    {
      icon: Target,
      title: "Purpose-Driven",
      description: "Illustrations designed to communicate your message effectively"
    },
    {
      icon: Layers,
      title: "Multi-Format",
      description: "Optimized for both digital and print applications"
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Back Button */}
      <div className="bg-warm-cream py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/design-services"
            className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Design Studio
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
                Custom Illustrator Art
              </h1>
              <p className="text-xl mb-8 text-gray-600">
                Transform your ideas into stunning digital illustrations that add a unique, artistic touch to your projects and brand. From mascots to editorial art, we bring creativity to life.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  Start Your Art Project
                </Link>
                <Link
                  to="#portfolio"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  View Portfolio
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75"
                alt="Custom Illustrator Art"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <Brush className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Custom Art</p>
                    <p className="text-sm text-gray-600">Unique illustrations</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Why Custom Illustrations Matter
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Custom illustrations set your brand apart from stock imagery and create a unique visual identity that resonates with your audience.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-6 rounded-lg shadow-lg text-center">
                <div className="bg-purple-100 w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                  <feature.icon className="h-6 w-6 text-purple-600" />
                </div>
                <h3 className="font-semibold text-gray-900 mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Design Process */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Our Illustration Process
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We follow a collaborative process to ensure your custom illustrations perfectly capture your vision and serve your project goals.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {designProcess.map((process, index) => (
              <div key={index} className="text-center">
                <div className="bg-purple-600 text-white w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 text-xl font-bold">
                  {process.step}
                </div>
                <h3 className="font-semibold text-gray-900 mb-3">{process.title}</h3>
                <p className="text-gray-600 text-sm">{process.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Illustration Types */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Types of Illustrations We Create
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From character design to editorial illustrations, we create custom artwork for all your creative needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-blue-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">Character & Mascot Design</h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Brand Mascots</li>
                <li>• Character Development</li>
                <li>• Cartoon Illustrations</li>
                <li>• Avatar Creation</li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-green-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Eye className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">Editorial & Conceptual</h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Magazine Illustrations</li>
                <li>• Book Artwork</li>
                <li>• Conceptual Art</li>
                <li>• Storytelling Visuals</li>
              </ul>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-lg">
              <div className="bg-orange-100 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <Target className="h-6 w-6 text-orange-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-3">Marketing & Commercial</h3>
              <ul className="text-gray-600 text-sm space-y-2">
                <li>• Infographics</li>
                <li>• Icon Sets</li>
                <li>• Product Illustrations</li>
                <li>• Advertising Art</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Illustration Portfolio
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              See examples of our custom illustrations across various styles and applications.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {portfolioSamples.map((sample, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                <img
                  src={sample.image}
                  alt={sample.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-6">
                  <h3 className="font-semibold text-gray-900 mb-2">{sample.title}</h3>
                  <p className="text-gray-600 text-sm">{sample.description}</p>
                  <div className="flex items-center mt-3">
                    {[...Array(5)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                    ))}
                    <span className="text-sm text-gray-500 ml-2">5.0</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Illustration Packages
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose the package that best fits your illustration needs and budget.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Basic Package */}
            <div className="bg-white border-2 border-gray-200 rounded-lg p-8 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Single Illustration</h3>
              <div className="text-4xl font-bold text-purple-600 mb-6">$149</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">1 custom illustration</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">2 rounds of revisions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">High-resolution files</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">5-7 day delivery</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>

            {/* Professional Package */}
            <div className="bg-white border-2 border-purple-600 rounded-lg p-8 text-center relative">
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span className="bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold">Most Popular</span>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Illustration Set</h3>
              <div className="text-4xl font-bold text-purple-600 mb-6">$399</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">3-5 custom illustrations</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">3 rounds of revisions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Multiple file formats</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Style guide included</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">7-10 day delivery</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>

            {/* Premium Package */}
            <div className="bg-white border-2 border-gray-200 rounded-lg p-8 text-center">
              <h3 className="text-xl font-bold text-gray-900 mb-4">Complete Art Package</h3>
              <div className="text-4xl font-bold text-purple-600 mb-6">$799</div>
              <ul className="text-left space-y-3 mb-8">
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">10+ custom illustrations</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Unlimited revisions</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Character development</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">Brand illustration guide</span>
                </li>
                <li className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-gray-700">14-21 day delivery</span>
                </li>
              </ul>
              <button className="w-full bg-brand-orange text-white py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors">
                Get Started
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="w-full py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Client Love Content */}
            <div className="lg:col-span-1">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                What our clients say ...
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We're a global team of skilled designers united by a passion for design and a commitment to excellence.
              </p>
              
              {/* Statistics */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Star className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Custom Artwork</div>
                    <div className="text-gray-600">Unique illustrations created specifically for your brand and vision.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Lightbulb className="h-8 w-8 text-purple-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Creative Concepts</div>
                    <div className="text-gray-600">Original ideas and artistic approaches tailored to your project.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Award className="h-8 w-8 text-orange-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Versatile Formats</div>
                    <div className="text-gray-600">High-resolution files optimized for both digital and print use.</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Scrollable Portfolio */}
            <div className="lg:col-span-2">
              <div className="relative">
                {/* Left Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('left')}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                    portfolioScrollPosition <= 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-50 hover:shadow-xl'
                  }`}
                  disabled={portfolioScrollPosition <= 0}
                >
                  <ChevronLeft className="h-5 w-5 text-gray-600" />
                </button>

                {/* Right Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('right')}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:bg-gray-50 hover:shadow-xl"
                >
                  <ChevronRight className="h-5 w-5 text-gray-600" />
                </button>

                {/* Scrollable Container */}
                <div 
                  ref={portfolioScrollRef}
                  onScroll={handlePortfolioScroll}
                  className="overflow-x-auto whitespace-nowrap py-4 px-12 scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                <div className="flex space-x-6">
                  {portfolioSamples.map((sample, index) => (
                    <div
                      key={index}
                      className="inline-block w-64 bg-white rounded-lg shadow-lg overflow-hidden flex-shrink-0 hover:shadow-xl transition-shadow duration-300"
                    >
                      <img
                        src={sample.image}
                        alt={sample.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <h3 className="font-semibold text-gray-900 mb-2 whitespace-normal">
                          {sample.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4 whitespace-normal">{sample.description}</p>
                        
                        {/* Star Rating */}
                        <div className="flex items-center mb-4">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className="h-4 w-4 text-yellow-400 fill-current"
                            />
                          ))}
                          <span className="text-sm text-gray-500 ml-2">5.0</span>
                        </div>
                        
                        {/* Reviewer Info */}
                        <div className="flex items-center space-x-3">
                          <img
                            src={sample.image}
                            alt="Client"
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <p className="text-sm font-medium text-gray-900 whitespace-normal">
                            Happy Client
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to know about our custom illustration services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                >
                  <span className="font-semibold text-gray-900">{faq.question}</span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">Ready to create custom illustrations?</p>
            <Link
              to="/contact"
              className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
            >
              Start Your Art Project
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default IllustratorArt;