import React from 'react';
import { Link } from 'react-router-dom';
import { useServices } from '../contexts/ServiceContext';
import ServiceCard from '../components/ServiceCard';
import {
  CheckCircle,
  Clock,
  Shield,
  Award,
  ChevronDown,
  ChevronUp,
  Star,
  Shirt,
  <PERSON><PERSON>,
  <PERSON>,
  Zap,
} from 'lucide-react';

const Apparel: React.FC = () => {
  const { services } = useServices();
  const [openFaq, setOpenFaq] = React.useState<number | null>(null);
  const [galleryScrollPosition, setGalleryScrollPosition] = React.useState(0);
  const galleryScrollRef = React.useRef<HTMLDivElement>(null);

  const apparelServices = services.filter(
    (service) => service.category === 'Apparel' && service.isActive
  );

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const scrollGallery = (direction: 'left' | 'right') => {
    if (galleryScrollRef.current) {
      const scrollAmount = 336; // Width of image (320px) + gap (16px)
      const currentScroll = galleryScrollRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      galleryScrollRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
      setGalleryScrollPosition(newScroll);
    }
  };

  const handleGalleryScroll = () => {
    if (galleryScrollRef.current) {
      setGalleryScrollPosition(galleryScrollRef.current.scrollLeft);
    }
  };

  const faqs = [
    {
      question: 'What types of apparel can you customize?',
      answer:
        'We customize t-shirts, hoodies, polo shirts, tank tops, hats, and more. Available in various colors, sizes, and styles for men, women, and children.',
    },
    {
      question: 'What printing methods do you use?',
      answer:
        'We use screen printing for large orders, heat transfer vinyl for small quantities, and direct-to-garment (DTG) printing for detailed designs with multiple colors.',
    },
    {
      question: "What's the minimum order quantity?",
      answer:
        'We have no minimum order quantity! You can order as few as one custom shirt or as many as thousands. Bulk pricing is available for larger orders.',
    },
    {
      question: 'How long does custom apparel take to produce?',
      answer:
        'Standard turnaround is 7-10 business days for most orders. Rush orders are available with 3-5 day turnaround for an additional fee.',
    },
    {
      question: 'What file formats do you accept for designs?',
      answer:
        'We accept PNG, JPG, PDF, AI, and PSD files. For best results, provide high-resolution vector files (AI or PDF) or high-resolution raster images (300 DPI).',
    },
    {
      question: 'Do you offer design services for apparel?',
      answer:
        'Yes! Our design team can create custom artwork for your apparel, including logos, text layouts, and graphic designs. Design services start at $39.',
    },
  ];

  const clientSamples = [
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      title: 'Company Team Shirts',
      description: 'Custom branded t-shirts for corporate team building',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      title: 'Event Merchandise',
      description: 'Concert tour shirts with vibrant graphics',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      title: 'Sports Team Uniforms',
      description: 'Custom jerseys with player names and numbers',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      title: 'Promotional Hoodies',
      description: 'Branded hoodies for marketing campaign',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Section 1: Hero with Headline and Image */}
      <section className="bg-white py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-600">
                Custom Apparel & T-Shirts
              </h1>
              <p className="text-xl mb-8 text-gray-700">
                Create custom t-shirts, hoodies, and apparel that represent your
                brand, team, or event. High-quality printing on premium garments
                for lasting comfort and style.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="#services"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  View Options
                </Link>
                <Link
                  to="#samples"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  See Examples
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725934515573.jpg&w=1080&q=75"
                alt="Custom Apparel and T-Shirts"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Shirt className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      Premium Quality
                    </p>
                    <p className="text-sm text-gray-600">100% cotton comfort</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 2: Services on this Page */}
      <section id="services" className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Custom Apparel Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Professional custom printing on high-quality apparel for
              businesses, teams, events, and personal use.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {apparelServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>

          {apparelServices.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">
                No apparel services available at the moment.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Section 3: Information About This Category */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Wear Your Brand With Pride
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Custom apparel is more than just clothing—it's a powerful way to
                build team unity, promote your brand, and create lasting
                memories. From corporate uniforms to event merchandise, quality
                custom apparel makes a statement.
              </p>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Brand Recognition
                    </h3>
                    <p className="text-gray-600">
                      Custom apparel turns your team into walking advertisements
                      for your brand.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">Team Unity</h3>
                    <p className="text-gray-600">
                      Matching apparel builds team spirit and professional
                      appearance.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Event Memorabilia
                    </h3>
                    <p className="text-gray-600">
                      Custom shirts create lasting memories of special events
                      and occasions.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75"
                alt="Custom Apparel Services"
                className="w-full h-auto rounded-lg shadow-lg"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      {/* Portfolio Section */}
      <section id="portfolio" className="w-full py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Client Love Content */}
            <div className="lg:col-span-1">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                What our clients say ...
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We're a global team of skilled designers united by a passion for design and a commitment to excellence.
              </p>
              
              {/* Statistics */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Star className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Premium Apparel</div>
                    <div className="text-gray-600">High-quality garments with professional printing and embroidery.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Users className="h-8 w-8 text-blue-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Team Unity</div>
                    <div className="text-gray-600">Custom apparel that builds team spirit and brand recognition.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Award className="h-8 w-8 text-orange-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">No Minimums</div>
                    <div className="text-gray-600">Order as few as one item or thousands with bulk pricing.</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Scrollable Portfolio */}
            <div className="lg:col-span-2">
              <div className="relative">
                {/* Left Navigation Button */}
                <button
                  onClick={() => scrollGallery('left')}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                    galleryScrollPosition <= 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-50 hover:shadow-xl'
                  }`}
                  disabled={galleryScrollPosition <= 0}
                >
                  <svg className="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>

                {/* Right Navigation Button */}
                <button
                  onClick={() => scrollGallery('right')}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:bg-gray-50 hover:shadow-xl"
                >
                  <svg className="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                {/* Scrollable Container */}
                <div 
                  ref={galleryScrollRef}
                  onScroll={handleGalleryScroll}
                  className="overflow-x-auto whitespace-nowrap py-4 px-12 scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                  <div className="flex space-x-6">
                    {clientSamples.map((sample, index) => (
                      <div
                        key={index}
                        className="inline-block w-64 bg-white rounded-lg shadow-lg overflow-hidden flex-shrink-0 hover:shadow-xl transition-shadow duration-300"
                      >
                        <img
                          src={sample.image}
                          alt={sample.title}
                          className="w-full h-48 object-cover"
                        />
                        <div className="p-6">
                          <h3 className="font-semibold text-gray-900 mb-2 whitespace-normal">
                            {sample.title}
                          </h3>
                          <p className="text-gray-600 text-sm mb-4 whitespace-normal">{sample.description}</p>
                          
                          {/* Star Rating */}
                          <div className="flex items-center mb-4">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className="h-4 w-4 text-yellow-400 fill-current"
                              />
                            ))}
                            <span className="text-sm text-gray-500 ml-2">5.0</span>
                          </div>
                          
                          {/* Reviewer Info */}
                          <div className="flex items-center space-x-3">
                            <img
                              src={sample.image}
                              alt="Client"
                              className="w-8 h-8 rounded-full object-cover"
                            />
                            <p className="text-sm font-medium text-gray-900 whitespace-normal">
                              Happy Client
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-orange-100 p-8 rounded-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Ready to Create Custom Apparel?
              </h3>
              <p className="text-gray-600 mb-6">
                Join businesses and teams who trust us with their custom apparel
                needs.
              </p>
              <Link
                to="#services"
                className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
              >
                Get Started Now
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-white">
        <div className="w-full">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Design - Create - Inspire
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Check us out on social media for creative ways to design your own products
            </p>
          </div>

          <div className="relative">
            {/* Left Navigation Arrow */}
            <button 
              onClick={() => scrollGallery('left')}
              className={`absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                galleryScrollPosition <= 0
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-gray-50 hover:shadow-xl'
              }`}
              disabled={galleryScrollPosition <= 0}
            >
              <svg className="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Right Navigation Arrow */}
            <button 
              onClick={() => scrollGallery('right')}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-all duration-200"
            >
              <svg className="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* Gallery Container */}
            <div 
              ref={galleryScrollRef}
              onScroll={handleGalleryScroll}
              className="overflow-x-auto px-16 scrollbar-hide"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
              }}
            >
              <div className="flex space-x-4">
                {/* Gallery Images */}
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725923064812.jpg&w=1080&q=75"
                    alt="Apparel design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 5: FAQ Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to know about our custom apparel printing
              services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                >
                  <span className="font-semibold text-gray-900">
                    {faq.question}
                  </span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">Still have questions?</p>
            <Link
              to="/contact"
              className="text-blue-600 hover:text-blue-700 font-semibold"
            >
              Contact our support team →
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Apparel;