					// Service Types - Backend specific extensions of frontend types
export interface Service {
	id: string;
	name: string;
	description: string;
	category: string;
	price?: number;
	image?: string;
	providerId: string;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
	tags?: string[];
	specifications?: Record<string, any>;
	availability?: ServiceAvailability;
}

export interface ServiceCreateRequest {
	name: string;
	description: string;
	category: string;
	price?: number;
	image?: string;
	tags?: string[];
	specifications?: Record<string, any>;
	availability?: ServiceAvailability;
}

export interface ServiceUpdateRequest {
	name?: string;
	description?: string;
	category?: string;
	price?: number;
	image?: string;
	isActive?: boolean;
	tags?: string[];
	specifications?: Record<string, any>;
	availability?: ServiceAvailability;
}

export interface ServiceCategory {
	id: string;
	name: string;
	description: string;
	icon?: string;
	route: string;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
	parentCategoryId?: string;
	services?: Service[];
}

export interface ServiceCategoryCreateRequest {
	name: string;
	description: string;
	icon?: string;
	route: string;
	parentCategoryId?: string;
}

export interface ServiceCategoryUpdateRequest {
	name?: string;
	description?: string;
	icon?: string;
	route?: string;
	isActive?: boolean;
	parentCategoryId?: string;
}

export interface Provider {
	id: string;
	name: string;
	email: string;
	description?: string;
	services: Service[];
	location?: string;
	rating?: number;
	userId: string; // Reference to User table
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
	contactPhone?: string;
	businessHours?: BusinessHours;
	specialties?: string[];
	portfolio?: string[];
}

export interface ProviderCreateRequest {
	name: string;
	email: string;
	description?: string;
	location?: string;
	contactPhone?: string;
	businessHours?: BusinessHours;
	specialties?: string[];
	portfolio?: string[];
}

export interface ProviderUpdateRequest {
	name?: string;
	description?: string;
	location?: string;
	isActive?: boolean;
	contactPhone?: string;
	businessHours?: BusinessHours;
	specialties?: string[];
	portfolio?: string[];
}

export interface ServiceAvailability {
	isAvailable: boolean;
	leadTime?: number; // in days
	maxOrdersPerDay?: number;
	availableDays?: string[]; // ['monday', 'tuesday', etc.]
	availableHours?: {
		start: string; // '09:00'
		end: string; // '17:00'
	};
}

export interface BusinessHours {
	monday?: DaySchedule;
	tuesday?: DaySchedule;
	wednesday?: DaySchedule;
	thursday?: DaySchedule;
	friday?: DaySchedule;
	saturday?: DaySchedule;
	sunday?: DaySchedule;
}

export interface DaySchedule {
	isOpen: boolean;
	openTime?: string; // '09:00'
	closeTime?: string; // '17:00'
	breakStart?: string;
	breakEnd?: string;
}

// Service Review and Rating
export interface ServiceReview {
	id: string;
	serviceId: string;
	userId: string;
	rating: number; // 1-5
	comment?: string;
	createdAt: Date;
	updatedAt: Date;
	isApproved: boolean;
}

export interface ServiceReviewCreateRequest {
	serviceId: string;
	rating: number;
	comment?: string;
}

export interface ServiceReviewUpdateRequest {
	rating?: number;
	comment?: string;
}
