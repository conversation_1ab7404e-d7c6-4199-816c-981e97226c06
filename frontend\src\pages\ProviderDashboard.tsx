import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useProviders } from '../contexts/ProviderContext';
import { useServices } from '../contexts/ServiceContext';
import { 
  Package, 
  Clock, 
  CheckCircle, 
  DollarSign, 
  TrendingUp, 
  Star,
  Settings,
  Eye,
  EyeOff,
  Check,
  X,
  AlertCircle,
  MapPin,
  Phone,
  Mail,
  Globe
} from 'lucide-react';

const ProviderDashboard: React.FC = () => {
  const { user } = useAuth();
  const { providers, updateProviderService } = useProviders();
  const { services } = useServices();
  const [activeTab, setActiveTab] = useState<'overview' | 'services' | 'profile'>('overview');

  // Find current provider (in real app, this would be based on authenticated user)
  const currentProvider = providers.find(p => p.email === user?.email) || providers[0];

  const handleServiceToggle = (serviceId: string, isActive: boolean) => {
    if (isActive) {
      // When activating a service, they must agree to the price
      updateProviderService(currentProvider.id, serviceId, {
        isActive: true,
        agreedToPrice: true,
        agreedDate: new Date().toISOString().split('T')[0]
      });
    } else {
      // When deactivating, just set inactive
      updateProviderService(currentProvider.id, serviceId, {
        isActive: false
      });
    }
  };

  const getProviderService = (serviceId: string) => {
    return currentProvider.services.find(ps => ps.serviceId === serviceId);
  };

  const stats = [
    { 
      title: 'Active Services', 
      value: currentProvider.totalServicesOffered.toString(), 
      icon: Package, 
      color: 'bg-blue-500' 
    },
    { 
      title: 'Average Rating', 
      value: currentProvider.averageRating.toString(), 
      icon: Star, 
      color: 'bg-yellow-500' 
    },
    { 
      title: 'Total Reviews', 
      value: currentProvider.reviewCount.toString(), 
      icon: TrendingUp, 
      color: 'bg-green-500' 
    },
    { 
      title: 'Service Areas', 
      value: currentProvider.serviceAreas.length.toString(), 
      icon: MapPin, 
      color: 'bg-purple-500' 
    },
  ];

  return (
    <div className="min-h-screen bg-warm-cream py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <img
              src={currentProvider.logo}
              alt={currentProvider.businessName}
              className="w-16 h-16 rounded-lg object-cover"
            />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{currentProvider.businessName}</h1>
              <p className="text-gray-600">Provider Dashboard</p>
            </div>
          </div>
          
          {/* Status Badge */}
          <div className="flex items-center space-x-4">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              currentProvider.isVerified 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {currentProvider.isVerified ? 'Verified Provider' : 'Pending Verification'}
            </span>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${
              currentProvider.isActive 
                ? 'bg-blue-100 text-blue-800' 
                : 'bg-red-100 text-red-800'
            }`}>
              {currentProvider.isActive ? 'Active' : 'Inactive'}
            </span>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'services', label: 'Service Management' },
              { id: 'profile', label: 'Business Profile' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && (
          <div className="space-y-8">
            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {stats.map((stat, index) => (
                <div key={index} className="bg-white rounded-lg shadow-lg p-6">
                  <div className="flex items-center">
                    <div className={`${stat.color} p-3 rounded-full`}>
                      <stat.icon className="h-6 w-6 text-white" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => setActiveTab('services')}
                  className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Settings className="h-6 w-6 text-blue-600 mr-3" />
                  <span className="font-medium text-gray-900">Manage Services</span>
                </button>
                <button
                  onClick={() => setActiveTab('profile')}
                  className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <Package className="h-6 w-6 text-green-600 mr-3" />
                  <span className="font-medium text-gray-900">Update Profile</span>
                </button>
                <button className="flex items-center justify-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                  <TrendingUp className="h-6 w-6 text-purple-600 mr-3" />
                  <span className="font-medium text-gray-900">View Analytics</span>
                </button>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Recent Activity</h2>
              <div className="space-y-4">
                {[
                  { action: 'Service activated: Business Cards', time: '2 hours ago', type: 'success' },
                  { action: 'Profile updated', time: '1 day ago', type: 'info' },
                  { action: 'New service area added: 10005', time: '3 days ago', type: 'info' },
                ].map((activity, index) => (
                  <div key={index} className="flex items-center space-x-4 p-3 border-l-4 border-blue-500 bg-blue-50">
                    <div className={`w-2 h-2 rounded-full ${activity.type === 'success' ? 'bg-green-500' : 'bg-blue-500'}`} />
                    <div className="flex-1">
                      <p className="text-gray-900">{activity.action}</p>
                      <p className="text-sm text-gray-600">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Services Tab */}
        {activeTab === 'services' && (
          <div className="space-y-8">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Service Management</h2>
                <div className="text-sm text-gray-600">
                  {currentProvider.totalServicesOffered} of {services.length} services active
                </div>
              </div>

              <div className="mb-6 p-4 bg-blue-50 rounded-lg">
                <div className="flex items-start space-x-3">
                  <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <h3 className="font-medium text-blue-900">Fixed Pricing Model</h3>
                    <p className="text-sm text-blue-700 mt-1">
                      All services have fixed prices set by the platform. By activating a service, you agree to provide 
                      that service at the listed price. You can activate or deactivate services at any time.
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {services.map((service) => {
                  const providerService = getProviderService(service.id);
                  const isActive = providerService?.isActive || false;
                  const hasAgreed = providerService?.agreedToPrice || false;

                  return (
                    <div key={service.id} className={`border-2 rounded-lg p-6 transition-all ${
                      isActive 
                        ? 'border-green-500 bg-green-50' 
                        : 'border-gray-200 bg-white hover:border-gray-300'
                    }`}>
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <h3 className="font-semibold text-gray-900 mb-2">{service.name}</h3>
                          <p className="text-sm text-gray-600 mb-3">{service.description}</p>
                          <div className="flex items-center justify-between">
                            <span className="text-2xl font-bold text-blue-600">${service.price}</span>
                            <span className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded">
                              {service.category}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="space-y-3">
                        {isActive && (
                          <div className="flex items-center space-x-2 text-sm text-green-700">
                            <CheckCircle className="h-4 w-4" />
                            <span>Active since {providerService?.agreedDate}</span>
                          </div>
                        )}

                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700">
                            {isActive ? 'Deactivate Service' : 'Activate Service'}
                          </span>
                          <button
                            onClick={() => handleServiceToggle(service.id, !isActive)}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              isActive ? 'bg-green-600' : 'bg-gray-200'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                isActive ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>

                        {!isActive && (
                          <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                            By activating this service, you agree to provide it at the fixed price of ${service.price}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="space-y-8">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Business Profile</h2>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Business Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Information</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Business Name</label>
                      <div className="text-gray-900">{currentProvider.businessName}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Contact Name</label>
                      <div className="text-gray-900">{currentProvider.contactName}</div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                      <div className="text-gray-900">{currentProvider.description}</div>
                    </div>
                  </div>
                </div>

                {/* Contact Information */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Contact Information</h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3">
                      <Mail className="h-5 w-5 text-gray-400" />
                      <span className="text-gray-900">{currentProvider.email}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <Phone className="h-5 w-5 text-gray-400" />
                      <span className="text-gray-900">{currentProvider.phone}</span>
                    </div>
                    {currentProvider.website && (
                      <div className="flex items-center space-x-3">
                        <Globe className="h-5 w-5 text-gray-400" />
                        <a href={currentProvider.website} className="text-blue-600 hover:text-blue-700">
                          {currentProvider.website}
                        </a>
                      </div>
                    )}
                    <div className="flex items-start space-x-3">
                      <MapPin className="h-5 w-5 text-gray-400 mt-1" />
                      <div className="text-gray-900">
                        {currentProvider.address}<br />
                        {currentProvider.city}, {currentProvider.state} {currentProvider.zipCode}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Service Areas */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Areas</h3>
                <div className="flex flex-wrap gap-2">
                  {currentProvider.serviceAreas.map((zipCode) => (
                    <span key={zipCode} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                      {zipCode}
                    </span>
                  ))}
                </div>
              </div>

              {/* Operating Hours */}
              <div className="mt-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Operating Hours</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(currentProvider.operatingHours).map(([day, hours]) => (
                    <div key={day} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                      <span className="font-medium text-gray-900 capitalize">{day}</span>
                      <span className="text-gray-600">
                        {hours.closed ? 'Closed' : `${hours.open} - ${hours.close}`}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProviderDashboard;