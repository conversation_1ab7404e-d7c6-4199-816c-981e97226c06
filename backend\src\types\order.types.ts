import {z} from 'zod';
import {OrderStatus, FileType} from './common.types';

// Order schemas
export const CreateOrderSchema = z.object({
	serviceId: z.string().min(1, 'Service is required'),
	providerId: z.string().optional(),
	description: z.string().optional(),
	requirements: z.record(z.any()).optional(),
	dueDate: z.string().datetime().optional(),
	totalAmount: z.number().positive('Total amount must be positive'),
});

export const UpdateOrderSchema = z.object({
	status: z.nativeEnum(OrderStatus).optional(),
	description: z.string().optional(),
	requirements: z.record(z.any()).optional(),
	dueDate: z.string().datetime().optional(),
	totalAmount: z.number().positive('Total amount must be positive').optional(),
});

export const CreateOrderItemSchema = z.object({
	name: z.string().min(1, 'Item name is required'),
	description: z.string().optional(),
	quantity: z.number().positive('Quantity must be positive'),
	price: z.number().positive('Price must be positive'),
});

export const UpdateOrderItemSchema = CreateOrderItemSchema.partial();

export const CreateOrderFileSchema = z.object({
	filename: z.string().min(1, 'Filename is required'),
	originalName: z.string().min(1, 'Original name is required'),
	mimeType: z.string().min(1, 'MIME type is required'),
	size: z.number().positive('File size must be positive'),
	url: z.string().url('Valid URL is required'),
	type: z.nativeEnum(FileType),
});

export const UpdateOrderFileSchema = CreateOrderFileSchema.partial();

export const OrderSearchSchema = z.object({
	status: z.nativeEnum(OrderStatus).optional(),
	serviceId: z.string().optional(),
	providerId: z.string().optional(),
	customerId: z.string().optional(),
	startDate: z.string().datetime().optional(),
	endDate: z.string().datetime().optional(),
});

// Order types
export type CreateOrderRequest = z.infer<typeof CreateOrderSchema>;
export type UpdateOrderRequest = z.infer<typeof UpdateOrderSchema>;
export type CreateOrderItemRequest = z.infer<typeof CreateOrderItemSchema>;
export type UpdateOrderItemRequest = z.infer<typeof UpdateOrderItemSchema>;
export type CreateOrderFileRequest = z.infer<typeof CreateOrderFileSchema>;
export type UpdateOrderFileRequest = z.infer<typeof UpdateOrderFileSchema>;
export type OrderSearchRequest = z.infer<typeof OrderSearchSchema>;

export interface Order {
	id: string;
	customerId: string;
	providerId?: string;
	serviceId: string;
	status: OrderStatus;
	totalAmount: number;
	description?: string;
	requirements?: Record<string, any>;
	dueDate?: Date;
	completedAt?: Date;
	createdAt: Date;
	updatedAt: Date;
}

export interface OrderItem {
	id: string;
	orderId: string;
	name: string;
	description?: string;
	quantity: number;
	price: number;
	createdAt: Date;
}

export interface OrderFile {
	id: string;
	orderId: string;
	filename: string;
	originalName: string;
	mimeType: string;
	size: number;
	url: string;
	type: FileType;
	createdAt: Date;
}

export interface OrderStatusHistory {
	id: string;
	orderId: string;
	status: OrderStatus;
	comment?: string;
	createdAt: Date;
}

export interface OrderWithDetails extends Order {
	customer: {
		id: string;
		name: string;
		email: string;
	};
	provider?: {
		id: string;
		businessName: string;
	};
	service: {
		id: string;
		name: string;
		description: string;
	};
	items: OrderItem[];
	files: OrderFile[];
	statusHistory: OrderStatusHistory[];
}

export interface OrderSummary {
	id: string;
	status: OrderStatus;
	totalAmount: number;
	serviceName: string;
	providerName?: string;
	createdAt: Date;
	dueDate?: Date;
}

export interface OrderStats {
	totalOrders: number;
	pendingOrders: number;
	inProgressOrders: number;
	completedOrders: number;
	cancelledOrders: number;
	totalRevenue: number;
	averageOrderValue: number;
}
