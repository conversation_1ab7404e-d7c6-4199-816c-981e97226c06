import {
	Provider,
	ProviderServiceArea,
	ProviderOperatingHour,
	ProviderService,
	ProviderWithDetails,
	ProviderSearchResult,
	ProviderStats,
	ProviderSearchRequest,
} from '@/types/provider.types';
import {PaginationParams, PaginatedResponse} from '@/types/common.types';

export interface IProviderRepository {
	// Provider operations
	createProvider(providerData: Partial<Provider>): Promise<Provider>;
	findProviderById(id: string): Promise<Provider | null>;
	findProviderByUserId(userId: string): Promise<Provider | null>;
	findProviderWithDetails(id: string): Promise<ProviderWithDetails | null>;
	updateProvider(
		id: string,
		providerData: Partial<Provider>
	): Promise<Provider>;
	deleteProvider(id: string): Promise<void>;
	listProviders(params: PaginationParams): Promise<PaginatedResponse<Provider>>;

	// Service area operations
	createServiceArea(
		providerId: string,
		areaData: Partial<ProviderServiceArea>
	): Promise<ProviderServiceArea>;
	findServiceAreaById(id: string): Promise<ProviderServiceArea | null>;
	findServiceAreasByProviderId(
		providerId: string
	): Promise<ProviderServiceArea[]>;
	updateServiceArea(
		id: string,
		areaData: Partial<ProviderServiceArea>
	): Promise<ProviderServiceArea>;
	deleteServiceArea(id: string): Promise<void>;

	// Operating hours operations
	createOperatingHour(
		providerId: string,
		hourData: Partial<ProviderOperatingHour>
	): Promise<ProviderOperatingHour>;
	findOperatingHourById(id: string): Promise<ProviderOperatingHour | null>;
	findOperatingHoursByProviderId(
		providerId: string
	): Promise<ProviderOperatingHour[]>;
	updateOperatingHour(
		id: string,
		hourData: Partial<ProviderOperatingHour>
	): Promise<ProviderOperatingHour>;
	deleteOperatingHour(id: string): Promise<void>;

	// Provider service operations
	createProviderService(
		providerId: string,
		serviceData: Partial<ProviderService>
	): Promise<ProviderService>;
	findProviderServiceById(id: string): Promise<ProviderService | null>;
	findProviderServicesByProviderId(
		providerId: string
	): Promise<ProviderService[]>;
	findProviderServiceByProviderAndService(
		providerId: string,
		serviceId: string
	): Promise<ProviderService | null>;
	updateProviderService(
		id: string,
		serviceData: Partial<ProviderService>
	): Promise<ProviderService>;
	deleteProviderService(id: string): Promise<void>;

	// Search and discovery operations
	searchProviders(
		searchParams: ProviderSearchRequest,
		params: PaginationParams
	): Promise<PaginatedResponse<ProviderSearchResult>>;
	findProvidersByService(
		serviceId: string,
		params: PaginationParams
	): Promise<PaginatedResponse<Provider>>;
	findProvidersByLocation(
		location: string,
		radius: number,
		params: PaginationParams
	): Promise<PaginatedResponse<Provider>>;
	findNearbyProviders(
		latitude: number,
		longitude: number,
		radius: number,
		params: PaginationParams
	): Promise<PaginatedResponse<Provider>>;

	// Verification and status operations
	verifyProvider(id: string): Promise<Provider>;
	updateProviderStatus(id: string, isActive: boolean): Promise<Provider>;
	updateProviderRating(
		id: string,
		rating: number,
		reviewCount: number
	): Promise<Provider>;

	// Statistics operations
	getProviderStats(providerId: string): Promise<ProviderStats | null>;
	getProviderStatsByDateRange(
		providerId: string,
		startDate: Date,
		endDate: Date
	): Promise<ProviderStats | null>;
}
