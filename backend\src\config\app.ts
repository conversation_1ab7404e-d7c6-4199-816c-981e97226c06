import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Import database
import {prisma} from './database';

// Import repositories
import {UserRepository} from '@/repositories/implementations/user.repository';
import {AuthRepository} from '@/repositories/implementations/auth.repository';

// Import services
import {AuthService} from '@/services/implementations/auth.service';

// Import controllers
import {AuthController} from '@/controllers/auth.controller';

// Import middleware
import {AuthMiddleware} from '@/middleware/auth.middleware';
import {ErrorMiddleware} from '@/middleware/error.middleware';

// Import routes
import {authRoutes} from '@/routes/auth.routes';

export class App {
	public app: express.Application;

	constructor() {
		this.app = express();
		this.initializeMiddleware();
		this.initializeDependencies();
		this.initializeRoutes();
		this.initializeErrorHandling();
	}

	private initializeMiddleware(): void {
		// Security middleware
		this.app.use(helmet());

		// CORS configuration
		this.app.use(
			cors({
				origin: process.env.FRONTEND_URL || 'http://localhost:3000',
				credentials: true,
				methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
				allowedHeaders: ['Content-Type', 'Authorization', 'X-Refresh-Token'],
			})
		);

		// Body parsing middleware
		this.app.use(express.json({limit: '10mb'}));
		this.app.use(express.urlencoded({extended: true, limit: '10mb'}));

		// Compression middleware
		this.app.use(compression());

		// Logging middleware
		if (process.env.NODE_ENV === 'development') {
			this.app.use(morgan('dev'));
		} else {
			this.app.use(morgan('combined'));
		}

		// Rate limiting
		const limiter = rateLimit({
			windowMs: 15 * 60 * 1000, // 15 minutes
			max: 100, // limit each IP to 100 requests per windowMs
			message: {
				success: false,
				message: 'Too many requests from this IP, please try again later.',
			},
			standardHeaders: true,
			legacyHeaders: false,
		});
		this.app.use('/api/', limiter);

		// Health check endpoint
		this.app.get('/health', (req, res) => {
			res.status(200).json({
				success: true,
				message: 'Server is healthy',
				timestamp: new Date().toISOString(),
				environment: process.env.NODE_ENV,
			});
		});
	}

	private initializeDependencies(): void {
		// Initialize repositories
		const userRepository = new UserRepository(prisma);
		const authRepository = new AuthRepository(prisma);

		// Initialize services with dependency injection
		const authService = new AuthService(
			authRepository,
			userRepository,
			process.env.JWT_SECRET || 'your-jwt-secret',
			process.env.JWT_REFRESH_SECRET || 'your-jwt-refresh-secret',
			process.env.JWT_EXPIRES_IN || '15m',
			process.env.JWT_REFRESH_EXPIRES_IN || '7d'
		);

		// Initialize controllers
		const authController = new AuthController(authService);

		// Initialize middleware
		const authMiddleware = new AuthMiddleware(authService);

		// Attach dependencies to app for route access
		(this.app as any).dependencies = {
			authController,
			authMiddleware,
			authService,
			userRepository,
			authRepository,
		};
	}

	private initializeRoutes(): void {
		// API routes
		this.app.use('/api/auth', authRoutes(this.app));

		// Additional routes will be added here
		// this.app.use('/api/users', userRoutes(this.app));
		// this.app.use('/api/services', serviceRoutes(this.app));
		// this.app.use('/api/providers', providerRoutes(this.app));
		// this.app.use('/api/orders', orderRoutes(this.app));
		// this.app.use('/api/admin', adminRoutes(this.app));
	}

	private initializeErrorHandling(): void {
		// 404 handler
		this.app.use(ErrorMiddleware.notFound);

		// Global error handler
		this.app.use(ErrorMiddleware.handleError);
	}

	public listen(port: number): void {
		this.app.listen(port, () => {
			console.log(`🚀 Server is running on port ${port}`);
			console.log(`📊 Health check: http://localhost:${port}/health`);
			console.log(`🔧 Environment: ${process.env.NODE_ENV}`);
		});
	}
}
