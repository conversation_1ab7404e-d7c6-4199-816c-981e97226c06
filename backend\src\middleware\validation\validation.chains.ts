// Validation Chains - Following SRP and DRY principles
import { body, ValidationChain } from 'express-validator';

// Reusable validation rules (DRY principle)
export const ValidationRules = {
  email: () => body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
    
  password: () => body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
  name: () => body('name')
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Name must be between 2 and 100 characters')
    .matches(/^[a-zA-Z\s]+$/)
    .withMessage('Name can only contain letters and spaces'),
    
  role: () => body('role')
    .optional()
    .isIn(['USER', 'PROVIDER', 'ADMIN'])
    .withMessage('Role must be USER, PROVIDER, or ADMIN'),
    
  phone: () => body('phone')
    .optional()
    .isMobilePhone('any')
    .withMessage('Please provide a valid phone number'),
    
  address: () => body('address')
    .optional()
    .trim()
    .isLength({ min: 5, max: 255 })
    .withMessage('Address must be between 5 and 255 characters'),
    
  city: () => body('city')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('City must be between 2 and 100 characters'),
    
  state: () => body('state')
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('State must be between 2 and 100 characters'),
    
  zipCode: () => body('zipCode')
    .optional()
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Zip code must be between 3 and 20 characters'),
    
  country: () => body('country')
    .optional()
    .isISO3166Alpha2()
    .withMessage('Country must be a valid ISO 3166 country code'),
    
  token: () => body('token')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Token is required'),
    
  refreshToken: () => body('refreshToken')
    .trim()
    .isLength({ min: 1 })
    .withMessage('Refresh token is required'),
    
  currentPassword: () => body('currentPassword')
    .isLength({ min: 1 })
    .withMessage('Current password is required'),
    
  newPassword: () => body('newPassword')
    .isLength({ min: 8 })
    .withMessage('New password must be at least 8 characters long')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('New password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'),
    
  confirmPassword: (passwordField: string = 'password') => body('confirmPassword')
    .custom((value, { req }) => {
      if (value !== req.body[passwordField]) {
        throw new Error('Password confirmation does not match password');
      }
      return true;
    }),
};

// Composite validation chains (OCP - Open/Closed Principle)
export const ValidationChains = {
  // Authentication validation chains
  register: (): ValidationChain[] => [
    ValidationRules.name(),
    ValidationRules.email(),
    ValidationRules.password(),
    ValidationRules.role(),
    ValidationRules.phone(),
    ValidationRules.address(),
    ValidationRules.city(),
    ValidationRules.state(),
    ValidationRules.zipCode(),
    ValidationRules.country(),
  ],
  
  login: (): ValidationChain[] => [
    ValidationRules.email(),
    body('password')
      .isLength({ min: 1 })
      .withMessage('Password is required'),
  ],
  
  refreshToken: (): ValidationChain[] => [
    ValidationRules.refreshToken(),
  ],
  
  verifyEmail: (): ValidationChain[] => [
    ValidationRules.token(),
  ],
  
  forgotPassword: (): ValidationChain[] => [
    ValidationRules.email(),
  ],
  
  resetPassword: (): ValidationChain[] => [
    ValidationRules.token(),
    ValidationRules.password(),
    ValidationRules.confirmPassword('password'),
  ],
  
  changePassword: (): ValidationChain[] => [
    ValidationRules.currentPassword(),
    ValidationRules.newPassword(),
    ValidationRules.confirmPassword('newPassword'),
  ],
  
  updateProfile: (): ValidationChain[] => [
    body('name')
      .optional()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Name must be between 2 and 100 characters'),
    body('phone')
      .optional()
      .isMobilePhone('any')
      .withMessage('Please provide a valid phone number'),
    ValidationRules.address(),
    ValidationRules.city(),
    ValidationRules.state(),
    ValidationRules.zipCode(),
    ValidationRules.country(),
  ],
  
  // Service validation chains
  createService: (): ValidationChain[] => [
    body('name')
      .trim()
      .isLength({ min: 3, max: 200 })
      .withMessage('Service name must be between 3 and 200 characters'),
    body('description')
      .trim()
      .isLength({ min: 10, max: 2000 })
      .withMessage('Description must be between 10 and 2000 characters'),
    body('categoryId')
      .isUUID()
      .withMessage('Category ID must be a valid UUID'),
    body('price')
      .isFloat({ min: 0 })
      .withMessage('Price must be a positive number'),
    body('priceType')
      .isIn(['FIXED', 'HOURLY', 'QUOTE'])
      .withMessage('Price type must be FIXED, HOURLY, or QUOTE'),
    body('duration')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Duration must be a positive integer (minutes)'),
    body('location')
      .optional()
      .trim()
      .isLength({ min: 5, max: 255 })
      .withMessage('Location must be between 5 and 255 characters'),
    body('tags')
      .optional()
      .isArray()
      .withMessage('Tags must be an array'),
    body('requirements')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Requirements must not exceed 1000 characters'),
    body('deliverables')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Deliverables must not exceed 1000 characters'),
  ],
  
  updateService: (): ValidationChain[] => [
    body('name')
      .optional()
      .trim()
      .isLength({ min: 3, max: 200 })
      .withMessage('Service name must be between 3 and 200 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ min: 10, max: 2000 })
      .withMessage('Description must be between 10 and 2000 characters'),
    body('price')
      .optional()
      .isFloat({ min: 0 })
      .withMessage('Price must be a positive number'),
    body('priceType')
      .optional()
      .isIn(['FIXED', 'HOURLY', 'QUOTE'])
      .withMessage('Price type must be FIXED, HOURLY, or QUOTE'),
    body('duration')
      .optional()
      .isInt({ min: 1 })
      .withMessage('Duration must be a positive integer (minutes)'),
    body('location')
      .optional()
      .trim()
      .isLength({ min: 5, max: 255 })
      .withMessage('Location must be between 5 and 255 characters'),
    body('isActive')
      .optional()
      .isBoolean()
      .withMessage('isActive must be a boolean'),
    body('isFeatured')
      .optional()
      .isBoolean()
      .withMessage('isFeatured must be a boolean'),
  ],
  
  // Provider validation chains
  createProvider: (): ValidationChain[] => [
    body('businessName')
      .trim()
      .isLength({ min: 2, max: 200 })
      .withMessage('Business name must be between 2 and 200 characters'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description must not exceed 1000 characters'),
    body('website')
      .optional()
      .isURL()
      .withMessage('Website must be a valid URL'),
    body('businessPhone')
      .optional()
      .isMobilePhone('any')
      .withMessage('Please provide a valid business phone number'),
    body('businessAddress')
      .optional()
      .trim()
      .isLength({ min: 5, max: 255 })
      .withMessage('Business address must be between 5 and 255 characters'),
    body('licenseNumber')
      .optional()
      .trim()
      .isLength({ min: 3, max: 100 })
      .withMessage('License number must be between 3 and 100 characters'),
    body('insuranceNumber')
      .optional()
      .trim()
      .isLength({ min: 3, max: 100 })
      .withMessage('Insurance number must be between 3 and 100 characters'),
  ],
  
  // Order validation chains
  createOrder: (): ValidationChain[] => [
    body('serviceId')
      .isUUID()
      .withMessage('Service ID must be a valid UUID'),
    body('customerNotes')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Customer notes must not exceed 1000 characters'),
    body('scheduledDate')
      .optional()
      .isISO8601()
      .withMessage('Scheduled date must be a valid ISO 8601 date'),
  ],
  
  updateOrder: (): ValidationChain[] => [
    body('status')
      .optional()
      .isIn(['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'REFUNDED'])
      .withMessage('Status must be a valid order status'),
    body('providerNotes')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Provider notes must not exceed 1000 characters'),
    body('scheduledDate')
      .optional()
      .isISO8601()
      .withMessage('Scheduled date must be a valid ISO 8601 date'),
    body('completedDate')
      .optional()
      .isISO8601()
      .withMessage('Completed date must be a valid ISO 8601 date'),
  ],
  
  // Review validation chains
  createReview: (): ValidationChain[] => [
    body('rating')
      .isInt({ min: 1, max: 5 })
      .withMessage('Rating must be between 1 and 5'),
    body('comment')
      .optional()
      .trim()
      .isLength({ min: 10, max: 1000 })
      .withMessage('Comment must be between 10 and 1000 characters'),
    body('isPublic')
      .optional()
      .isBoolean()
      .withMessage('isPublic must be a boolean'),
  ],

  // Category validation chains
  createCategory: (): ValidationChain[] => [
    body('name')
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Category name must be between 2 and 100 characters')
      .matches(/^[a-zA-Z0-9\s\-]+$/)
      .withMessage('Category name can only contain letters, numbers, spaces, and hyphens'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description cannot exceed 1000 characters'),
    body('icon')
      .optional()
      .trim()
      .isLength({ max: 255 })
      .withMessage('Icon cannot exceed 255 characters'),
  ],

  updateCategory: (): ValidationChain[] => [
    body('name')
      .optional()
      .trim()
      .isLength({ min: 2, max: 100 })
      .withMessage('Category name must be between 2 and 100 characters')
      .matches(/^[a-zA-Z0-9\s\-]+$/)
      .withMessage('Category name can only contain letters, numbers, spaces, and hyphens'),
    body('description')
      .optional()
      .trim()
      .isLength({ max: 1000 })
      .withMessage('Description cannot exceed 1000 characters'),
    body('icon')
      .optional()
      .trim()
      .isLength({ max: 255 })
      .withMessage('Icon cannot exceed 255 characters'),
    body('isActive')
      .optional()
      .isBoolean()
      .withMessage('isActive must be a boolean'),
  ],

  reorderCategories: (): ValidationChain[] => [
    body('categoryOrders')
      .isArray({ min: 1 })
      .withMessage('categoryOrders must be a non-empty array'),
    body('categoryOrders.*.id')
      .isUUID()
      .withMessage('Each category ID must be a valid UUID'),
    body('categoryOrders.*.sortOrder')
      .isInt({ min: 0 })
      .withMessage('Sort order must be a non-negative integer'),
  ],

  setFeatured: (): ValidationChain[] => [
    body('featured')
      .isBoolean()
      .withMessage('featured must be a boolean value'),
  ],
};