import React from 'react';
import { Link } from 'react-router-dom';

interface ServiceThumbnailCardProps {
  serviceName: string;
  imageUrl: string;
  route: string;
}

const ServiceThumbnailCard: React.FC<ServiceThumbnailCardProps> = ({
  serviceName,
  imageUrl,
  route,
}) => {
  return (
    <Link to={route} className="block group">
      <div className="relative bg-white rounded-xl	 overflow-hidden shadow-sm hover:shadow-md transition-shadow duration-300">
        {/* Service Image */}
        <div className="aspect-[2/3] overflow-hidden">
          <img
            src={imageUrl}
            alt={serviceName}
            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
          />
        </div>

        {/* Service Name Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent flex items-end">
          <div className="p-4 w-full">
            <h3 className="text-white font-semibold text-lg text-center">
              {serviceName}
            </h3>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default ServiceThumbnailCard;
