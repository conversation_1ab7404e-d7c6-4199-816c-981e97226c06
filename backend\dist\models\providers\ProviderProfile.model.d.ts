import { ProviderProfile as PrismaProviderProfile } from '@prisma/client';
import { BaseModel, Result, ValueObject } from '../base/BaseModel';
export declare class BusinessInfo extends ValueObject {
    private readonly businessName;
    private readonly description?;
    private readonly website?;
    constructor(businessName: string, description?: string | undefined, website?: string | undefined);
    private validate;
    getBusinessName(): string;
    getDescription(): string | undefined;
    getWebsite(): string | undefined;
    getDisplayName(): string;
    protected getEqualityComponents(): any[];
}
export declare class BusinessAddress extends ValueObject {
    private readonly businessPhone?;
    private readonly businessAddress?;
    private readonly businessCity?;
    private readonly businessState?;
    private readonly businessZip?;
    constructor(businessPhone?: string | undefined, businessAddress?: string | undefined, businessCity?: string | undefined, businessState?: string | undefined, businessZip?: string | undefined);
    private validate;
    getBusinessPhone(): string | undefined;
    getBusinessAddress(): string | undefined;
    getBusinessCity(): string | undefined;
    getBusinessState(): string | undefined;
    getBusinessZip(): string | undefined;
    getFullAddress(): string;
    isComplete(): boolean;
    protected getEqualityComponents(): any[];
}
export declare class VerificationInfo extends ValueObject {
    private readonly licenseNumber?;
    private readonly insuranceNumber?;
    private readonly isVerified;
    private readonly verifiedAt?;
    constructor(licenseNumber?: string | undefined, insuranceNumber?: string | undefined, isVerified?: boolean, verifiedAt?: Date | undefined);
    private validate;
    getLicenseNumber(): string | undefined;
    getInsuranceNumber(): string | undefined;
    isVerified(): boolean;
    getVerifiedAt(): Date | undefined;
    getVerificationStatus(): 'verified' | 'pending' | 'unverified';
    hasDocumentation(): boolean;
    protected getEqualityComponents(): any[];
}
export declare class ProviderRating extends ValueObject {
    private readonly rating;
    private readonly totalReviews;
    private readonly totalOrders;
    constructor(rating: number, totalReviews: number, totalOrders: number);
    private validate;
    getRating(): number;
    getTotalReviews(): number;
    getTotalOrders(): number;
    getDisplayRating(): string;
    getReputationLevel(): 'new' | 'established' | 'experienced' | 'expert';
    isHighlyRated(): boolean;
    getCompletionRate(): number;
    protected getEqualityComponents(): any[];
}
export declare class ProviderProfileModel extends BaseModel<PrismaProviderProfile> {
    private businessInfo;
    private businessAddress;
    private verificationInfo;
    private rating;
    constructor(data: PrismaProviderProfile);
    getUserId(): string;
    getBusinessInfo(): BusinessInfo;
    getBusinessAddress(): BusinessAddress;
    getVerificationInfo(): VerificationInfo;
    getRating(): ProviderRating;
    isActive(): boolean;
    isVerified(): boolean;
    getProfileCompleteness(): {
        percentage: number;
        missingFields: string[];
    };
    canAcceptOrders(): boolean;
    getBusinessHours(): string;
    updateBusinessInfo(updates: {
        businessName?: string;
        description?: string;
        website?: string;
    }): Result<void, string>;
    updateBusinessAddress(updates: {
        businessPhone?: string;
        businessAddress?: string;
        businessCity?: string;
        businessState?: string;
        businessZip?: string;
    }): Result<void, string>;
    verify(verifiedBy: string, licenseNumber?: string, insuranceNumber?: string): Result<void, string>;
    revokeVerification(reason: string, revokedBy: string): Result<void, string>;
    activate(): Result<void, string>;
    deactivate(reason?: string): Result<void, string>;
    updateRating(newRating: number, totalReviews: number, totalOrders: number): Result<void, string>;
    protected validateBusinessRules(): string[];
    static createProviderProfile(providerData: {
        userId: string;
        businessName: string;
        description?: string;
        website?: string;
        businessPhone?: string;
        businessAddress?: string;
        businessCity?: string;
        businessState?: string;
        businessZip?: string;
        licenseNumber?: string;
        insuranceNumber?: string;
    }): Result<ProviderProfileModel, string>;
    static fromPrismaProviderProfile(prismaProviderProfile: PrismaProviderProfile): ProviderProfileModel;
    toPublicObject(): PrismaProviderProfile & {
        profileCompleteness: {
            percentage: number;
            missingFields: string[];
        };
        canAcceptOrders: boolean;
        reputationLevel: string;
        verificationStatus: string;
    };
    toSearchResult(): {
        id: string;
        businessName: string;
        description?: string;
        rating: number;
        totalReviews: number;
        totalOrders: number;
        isVerified: boolean;
        location: string;
        reputationLevel: string;
    };
}
//# sourceMappingURL=ProviderProfile.model.d.ts.map