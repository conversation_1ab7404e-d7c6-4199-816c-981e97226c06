// Formatting utilities - Following SRP and DRY principles
import { PaginatedResult, ApiResponse, ApiError } from '@/types/common';

// Response formatting (SRP)
export class ResponseFormatter {
  static success<T>(
    message: string,
    data?: T,
    statusCode: number = 200
  ): ApiResponse<T> {
    return {
      success: true,
      message,
      data,
      timestamp: new Date().toISOString(),
    };
  }
  
  static error(
    message: string,
    error: string,
    statusCode: number = 500,
    path?: string
  ): ApiError {
    return {
      success: false,
      message,
      error,
      statusCode,
      timestamp: new Date().toISOString(),
      path: path || '',
    };
  }
  
  static paginated<T>(
    data: T[],
    page: number,
    limit: number,
    total: number,
    message: string = 'Data retrieved successfully'
  ): ApiResponse<PaginatedResult<T>> {
    const totalPages = Math.ceil(total / limit);
    
    return this.success(message, {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  }
}

// String formatting utilities (SRP)
export class StringFormatter {
  static slugify(text: string): string {
    return text
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '') // Remove non-word chars
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
  }
  
  static capitalize(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  }
  
  static titleCase(text: string): string {
    return text
      .toLowerCase()
      .split(' ')
      .map(word => this.capitalize(word))
      .join(' ');
  }
  
  static truncate(text: string, length: number, suffix: string = '...'): string {
    if (text.length <= length) return text;
    return text.substring(0, length - suffix.length) + suffix;
  }
  
  static sanitizeFileName(fileName: string): string {
    return fileName
      .replace(/[^a-zA-Z0-9.-]/g, '_') // Replace invalid chars with underscore
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, ''); // Remove leading/trailing underscores
  }
}

// Number formatting utilities (SRP)
export class NumberFormatter {
  static currency(
    amount: number,
    currency: string = 'USD',
    locale: string = 'en-US'
  ): string {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency,
    }).format(amount);
  }
  
  static decimal(
    number: number,
    decimals: number = 2,
    locale: string = 'en-US'
  ): string {
    return new Intl.NumberFormat(locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    }).format(number);
  }
  
  static percentage(
    value: number,
    total: number,
    decimals: number = 1
  ): string {
    const percentage = (value / total) * 100;
    return `${percentage.toFixed(decimals)}%`;
  }
  
  static fileSize(bytes: number): string {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(2)} ${sizes[i]}`;
  }
}

// Date formatting utilities (SRP)
export class DateFormatter {
  static toISO(date: Date): string {
    return date.toISOString();
  }
  
  static toLocaleString(
    date: Date,
    locale: string = 'en-US',
    options?: Intl.DateTimeFormatOptions
  ): string {
    return date.toLocaleString(locale, options);
  }
  
  static toDateString(date: Date): string {
    return date.toISOString().split('T')[0];
  }
  
  static toTimeString(date: Date): string {
    return date.toTimeString().split(' ')[0];
  }
  
  static relativetime(date: Date): string {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  }
  
  static addDays(date: Date, days: number): Date {
    const result = new Date(date);
    result.setDate(result.getDate() + days);
    return result;
  }
  
  static addHours(date: Date, hours: number): Date {
    const result = new Date(date);
    result.setHours(result.getHours() + hours);
    return result;
  }
}

// URL formatting utilities (SRP)
export class UrlFormatter {
  static buildQueryString(params: Record<string, any>): string {
    const filtered = Object.entries(params)
      .filter(([_, value]) => value !== undefined && value !== null && value !== '')
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
    
    return filtered.length > 0 ? `?${filtered.join('&')}` : '';
  }
  
  static joinPaths(...paths: string[]): string {
    return paths
      .map(path => path.replace(/^\/+|\/+$/g, '')) // Remove leading/trailing slashes
      .filter(path => path.length > 0)
      .join('/');
  }
  
  static getFileExtension(url: string): string {
    const parts = url.split('.');
    return parts.length > 1 ? parts[parts.length - 1].toLowerCase() : '';
  }
}

// Validation result formatting (SRP)
export class ValidationFormatter {
  static formatValidationErrors(errors: any[]): Record<string, string> {
    return errors.reduce((acc, error) => {
      acc[error.path || error.param] = error.msg;
      return acc;
    }, {});
  }
  
  static hasValidationErrors(errors: any[]): boolean {
    return errors.length > 0;
  }
}