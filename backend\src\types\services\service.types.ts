// Service Types - Aligned with Prisma schema and business logic
import { ServicePriceType } from '@prisma/client';

// Data Transfer Types for API operations
export interface CreateServiceData {
  name: string;
  description: string;
  categoryId: string;
  providerId: string;
  userId: string;
  price: number;
  priceType: ServicePriceType;
  duration?: number; // in minutes
  location?: string;
  images: string[];
  tags: string[];
  requirements?: string;
  deliverables?: string;
  isActive: boolean;
  isFeatured: boolean;
  rating?: number;
  totalReviews: number;
  totalOrders: number;
}

export interface UpdateServiceData {
  name?: string;
  description?: string;
  categoryId?: string;
  price?: number;
  priceType?: ServicePriceType;
  duration?: number;
  location?: string;
  images?: string[];
  tags?: string[];
  requirements?: string;
  deliverables?: string;
  isActive?: boolean;
  isFeatured?: boolean;
}

export interface CreateCategoryData {
  name: string;
  description?: string;
  icon?: string;
  isActive: boolean;
}

export interface UpdateCategoryData {
  name?: string;
  description?: string;
  icon?: string;
  isActive?: boolean;
  sortOrder?: number;
}

// Search and Filter Types
export interface ServiceFilters {
  categoryId?: string;
  providerId?: string;
  priceType?: ServicePriceType;
  minPrice?: number;
  maxPrice?: number;
  tags?: string[];
  location?: string;
  isActive?: boolean;
  isFeatured?: boolean;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface ServiceSearchRequest {
  query?: string;
  filters?: ServiceFilters;
  pagination?: {
    page: number;
    limit: number;
  };
}

// API Request/Response Types
export interface CreateServiceRequest {
  name: string;
  description: string;
  categoryId: string;
  price: number;
  priceType: ServicePriceType;
  duration?: number;
  location?: string;
  tags?: string[];
  requirements?: string;
  deliverables?: string;
  providerId?: string; // Optional, defaults to user ID
}

export interface UpdateServiceRequest {
  name?: string;
  description?: string;
  categoryId?: string;
  price?: number;
  priceType?: ServicePriceType;
  duration?: number;
  location?: string;
  tags?: string[];
  requirements?: string;
  deliverables?: string;
  isActive?: boolean;
  isFeatured?: boolean;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
  icon?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  icon?: string;
  isActive?: boolean;
}

// Business Logic Types
export interface ServiceBusinessRules {
  maxImagesPerService: number;
  maxTagsPerService: number;
  minServicePrice: number;
  maxServicePrice: number;
  allowedPriceTypes: ServicePriceType[];
  maxDescriptionLength: number;
  maxNameLength: number;
}

export interface CategoryBusinessRules {
  maxCategoriesPerLevel: number;
  maxCategoryDepth: number;
  reservedCategoryNames: string[];
  maxCategoryNameLength: number;
  maxCategoryDescriptionLength: number;
}

// Validation Error Types
export interface ServiceValidationError {
  field: string;
  message: string;
  code: string;
}

export interface CategoryValidationError {
  field: string;
  message: string;
  code: string;
}

// Utility Types
export type ServiceSortField = 'name' | 'price' | 'rating' | 'orders' | 'date';
export type ServiceSortOrder = 'asc' | 'desc';

export interface ServiceSortOptions {
  field: ServiceSortField;
  order: ServiceSortOrder;
}

// Extended Types for specific use cases
export interface ServiceWithMetrics {
  id: string;
  name: string;
  price: number;
  rating: number;
  totalOrders: number;
  totalReviews: number;
  conversionRate: number;
  averageOrderValue: number;
  lastOrderDate?: Date;
}

export interface CategoryWithMetrics {
  id: string;
  name: string;
  servicesCount: number;
  activeServicesCount: number;
  totalOrders: number;
  averagePrice: number;
  topProviders: string[];
}

// Event Types for business logic
export interface ServiceCreatedEvent {
  serviceId: string;
  providerId: string;
  categoryId: string;
  timestamp: Date;
}

export interface ServiceUpdatedEvent {
  serviceId: string;
  providerId: string;
  changes: Partial<UpdateServiceData>;
  timestamp: Date;
}

export interface ServiceDeletedEvent {
  serviceId: string;
  providerId: string;
  timestamp: Date;
}

export interface CategoryCreatedEvent {
  categoryId: string;
  name: string;
  createdBy: string;
  timestamp: Date;
}

export interface CategoryUpdatedEvent {
  categoryId: string;
  changes: Partial<UpdateCategoryData>;
  updatedBy: string;
  timestamp: Date;
}