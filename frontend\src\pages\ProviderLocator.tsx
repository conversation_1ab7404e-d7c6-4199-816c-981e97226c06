import React, { useState } from 'react';
import { useProviders } from '../contexts/ProviderContext';
import { useServices } from '../contexts/ServiceContext';
import { 
  MapPin, 
  Search, 
  Star, 
  Phone, 
  Mail, 
  ExternalLink,
  Filter,
  CheckCircle,
  Clock,
  Award
} from 'lucide-react';
import { Link } from 'react-router-dom';

const ProviderLocator: React.FC = () => {
  const { providers, getProvidersByLocation, getProvidersByService } = useProviders();
  const { services } = useServices();
  const [searchLocation, setSearchLocation] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [filteredProviders, setFilteredProviders] = useState(providers.filter(p => p.isActive && p.isVerified));
  const [sortBy, setSortBy] = useState<'rating' | 'distance' | 'reviews'>('rating');

  const handleSearch = () => {
    let results = providers.filter(p => p.isActive && p.isVerified);

    // Filter by location if provided
    if (searchLocation.trim()) {
      results = getProvidersByLocation(searchLocation.trim());
    }

    // Filter by service if selected
    if (selectedService) {
      const serviceProviders = getProvidersByService(selectedService);
      results = results.filter(p => serviceProviders.some(sp => sp.id === p.id));
    }

    // Sort results
    results.sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.averageRating - a.averageRating;
        case 'reviews':
          return b.reviewCount - a.reviewCount;
        case 'distance':
          // Mock distance sorting - in real app would calculate actual distance
          return a.businessName.localeCompare(b.businessName);
        default:
          return 0;
      }
    });

    setFilteredProviders(results);
  };

  const resetFilters = () => {
    setSearchLocation('');
    setSelectedService('');
    setFilteredProviders(providers.filter(p => p.isActive && p.isVerified));
  };

  return (
    <div className="min-h-screen bg-white py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">Find Printing Providers</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover verified printing providers in your area. Compare ratings, services, and contact information.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            {/* Location Search */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
              <div className="relative">
                <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  value={searchLocation}
                  onChange={(e) => setSearchLocation(e.target.value)}
                  placeholder="ZIP code or city"
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Service Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Service</label>
              <select
                value={selectedService}
                onChange={(e) => setSelectedService(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">All Services</option>
                {services.filter(s => s.isActive).map(service => (
                  <option key={service.id} value={service.id}>{service.name}</option>
                ))}
              </select>
            </div>

            {/* Sort By */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="rating">Highest Rated</option>
                <option value="reviews">Most Reviews</option>
                <option value="distance">Distance</option>
              </select>
            </div>

            {/* Search Button */}
            <div className="flex items-end space-x-2">
              <button
                onClick={handleSearch}
                className="flex-1 bg-brand-orange text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center space-x-2"
              >
                <Search className="h-4 w-4" />
                <span>Search</span>
              </button>
              <button
                onClick={resetFilters}
                className="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Filter className="h-4 w-4" />
              </button>
            </div>
          </div>

          {/* Results Summary */}
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>{filteredProviders.length} providers found</span>
            {(searchLocation || selectedService) && (
              <button
                onClick={resetFilters}
                className="text-blue-600 hover:text-blue-700"
              >
                Clear filters
              </button>
            )}
          </div>
        </div>

        {/* Provider Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProviders.map((provider) => (
            <div key={provider.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
              {/* Provider Header */}
              <div className="p-6">
                <div className="flex items-start space-x-4 mb-4">
                  <img
                    src={provider.logo}
                    alt={provider.businessName}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-bold text-gray-900">{provider.businessName}</h3>
                      {provider.isVerified && (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      )}
                    </div>
                    <div className="flex items-center mb-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star 
                            key={i} 
                            className={`h-4 w-4 ${
                              i < Math.floor(provider.averageRating) 
                                ? 'text-yellow-400 fill-current' 
                                : 'text-gray-300'
                            }`} 
                          />
                        ))}
                      </div>
                      <span className="ml-2 text-sm text-gray-600">
                        {provider.averageRating} ({provider.reviewCount})
                      </span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-1" />
                      {provider.city}, {provider.state}
                    </div>
                  </div>
                </div>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">{provider.description}</p>

                {/* Stats */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Award className="h-4 w-4 text-blue-500" />
                    </div>
                    <div className="text-sm font-medium text-gray-900">{provider.totalServicesOffered}</div>
                    <div className="text-xs text-gray-500">Services</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Star className="h-4 w-4 text-yellow-500" />
                    </div>
                    <div className="text-sm font-medium text-gray-900">{provider.averageRating}</div>
                    <div className="text-xs text-gray-500">Rating</div>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center mb-1">
                      <Clock className="h-4 w-4 text-green-500" />
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      {new Date().getFullYear() - new Date(provider.joinedDate).getFullYear() || 'New'}
                    </div>
                    <div className="text-xs text-gray-500">Years</div>
                  </div>
                </div>

                {/* Service Areas */}
                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-700 mb-2">Service Areas:</div>
                  <div className="flex flex-wrap gap-1">
                    {provider.serviceAreas.slice(0, 3).map((area) => (
                      <span key={area} className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                        {area}
                      </span>
                    ))}
                    {provider.serviceAreas.length > 3 && (
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                        +{provider.serviceAreas.length - 3} more
                      </span>
                    )}
                  </div>
                </div>

                {/* Contact Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-3">
                    <a 
                      href={`tel:${provider.phone}`}
                      className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                    >
                      <Phone className="h-4 w-4 mr-1" />
                      Call
                    </a>
                    <a 
                      href={`mailto:${provider.email}`}
                      className="flex items-center text-blue-600 hover:text-blue-700 text-sm"
                    >
                      <Mail className="h-4 w-4 mr-1" />
                      Email
                    </a>
                  </div>
                  <Link
                    to={`/provider/${provider.id}`}
                    className="flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    View Profile
                    <ExternalLink className="h-4 w-4 ml-1" />
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredProviders.length === 0 && (
          <div className="text-center py-12">
            <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Providers Found</h3>
            <p className="text-gray-600 mb-4">
              No printing providers found matching your criteria.
            </p>
            <button
              onClick={resetFilters}
              className="bg-brand-orange text-white px-6 py-2 rounded-lg hover:bg-orange-600 transition-colors"
            >
              Clear Filters
            </button>
          </div>
        )}

        {/* CTA Section */}
        <div className="mt-16 bg-orange-100 text-orange-500 rounded-lg p-8 text-center">
          <h2 className="text-2xl font-bold mb-4">Are you a printing provider?</h2>
          <p className="text-orange-500 mb-6">
            Join our network of verified providers and connect with customers in your area.
          </p>
          <Link
            to="/provider-services"
            className="bg-white text-brand-orange px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors inline-block"
          >
            Become a Provider
          </Link>
        </div>
      </div>
    </div>
  );
};

export default ProviderLocator;