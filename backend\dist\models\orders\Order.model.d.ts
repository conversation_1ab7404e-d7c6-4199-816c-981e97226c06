import { Order as PrismaOrder, OrderStatus, PaymentStatus } from '@prisma/client';
import { BaseModel, Result, ValueObject } from '../base/BaseModel';
export declare class OrderNumber extends ValueObject {
    private readonly value;
    constructor(value: string);
    private validate;
    getValue(): string;
    static generate(): OrderNumber;
    protected getEqualityComponents(): any[];
}
export declare class OrderAmount extends ValueObject {
    private readonly amount;
    constructor(amount: number);
    private validate;
    getAmount(): number;
    getFormattedAmount(): string;
    calculateTax(taxRate?: number): number;
    calculateTotal(taxRate?: number): number;
    protected getEqualityComponents(): any[];
}
export declare class OrderTimeline extends ValueObject {
    private readonly status;
    private readonly scheduledDate?;
    private readonly completedDate?;
    private readonly cancelledDate?;
    constructor(status: OrderStatus, scheduledDate?: Date | undefined, completedDate?: Date | undefined, cancelledDate?: Date | undefined);
    private validate;
    getStatus(): OrderStatus;
    getScheduledDate(): Date | undefined;
    getCompletedDate(): Date | undefined;
    getCancelledDate(): Date | undefined;
    isOverdue(): boolean;
    getDaysUntilScheduled(): number;
    getProcessingTime(): number | undefined;
    canTransitionTo(newStatus: OrderStatus): boolean;
    protected getEqualityComponents(): any[];
}
export declare class OrderFiles extends ValueObject {
    private readonly files;
    constructor(files: string[]);
    private validate;
    getFiles(): string[];
    hasFiles(): boolean;
    getFileCount(): number;
    protected getEqualityComponents(): any[];
}
export declare class OrderModel extends BaseModel<PrismaOrder> {
    private orderNumber;
    private amount;
    private timeline;
    private files;
    constructor(data: PrismaOrder);
    getOrderNumber(): OrderNumber;
    getServiceId(): string;
    getUserId(): string;
    getProviderId(): string;
    getAmount(): OrderAmount;
    getTimeline(): OrderTimeline;
    getFiles(): OrderFiles;
    getPaymentStatus(): PaymentStatus;
    getPaymentMethod(): string | undefined;
    getCustomerNotes(): string | undefined;
    getProviderNotes(): string | undefined;
    getCancellationReason(): string | undefined;
    isActive(): boolean;
    canBeCancelled(): boolean;
    canBeRefunded(): boolean;
    requiresProviderAction(): boolean;
    requiresCustomerAction(): boolean;
    updateStatus(newStatus: OrderStatus, notes?: string, performedBy?: string): Result<void, string>;
    updatePaymentStatus(paymentStatus: PaymentStatus, paymentMethod?: string, transactionId?: string): Result<void, string>;
    updateScheduledDate(scheduledDate: Date): Result<void, string>;
    addFiles(newFiles: string[]): Result<void, string>;
    cancel(reason: string, cancelledBy: string): Result<void, string>;
    complete(completedBy: string, notes?: string): Result<void, string>;
    updateNotes(customerNotes?: string, providerNotes?: string): Result<void, string>;
    protected validateBusinessRules(): string[];
    static createOrder(orderData: {
        serviceId: string;
        userId: string;
        providerId: string;
        totalAmount: number;
        customerNotes?: string;
        scheduledDate?: Date;
        files?: string[];
    }): Result<OrderModel, string>;
    static fromPrismaOrder(prismaOrder: PrismaOrder): OrderModel;
    toPublicObject(): PrismaOrder & {
        formattedAmount: string;
        isOverdue: boolean;
        canBeCancelled: boolean;
        canBeRefunded: boolean;
        requiresAction: 'customer' | 'provider' | 'none';
        daysUntilScheduled: number;
    };
    toCustomerView(): {
        id: string;
        orderNumber: string;
        status: OrderStatus;
        totalAmount: number;
        formattedAmount: string;
        paymentStatus: PaymentStatus;
        scheduledDate?: Date;
        completedDate?: Date;
        customerNotes?: string;
        files: string[];
        isOverdue: boolean;
        canBeCancelled: boolean;
        daysUntilScheduled: number;
    };
    toProviderView(): {
        id: string;
        orderNumber: string;
        status: OrderStatus;
        totalAmount: number;
        formattedAmount: string;
        paymentStatus: PaymentStatus;
        scheduledDate?: Date;
        customerNotes?: string;
        providerNotes?: string;
        files: string[];
        requiresAction: boolean;
        isOverdue: boolean;
    };
}
//# sourceMappingURL=Order.model.d.ts.map