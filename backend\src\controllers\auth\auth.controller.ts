// Authentication Controller - Following SRP and DIP principles
import { Request, Response } from 'express';
import { AuthService, IEmailService } from '@/services/auth/auth.service';
import { UserRepository, SessionRepository } from '@/repositories';
import { EmailService } from '@/services/email/email.service';
import { ResponseFormatter } from '@/utils/formatting';
import { HTTP_STATUS } from '@/constants';
import { prisma } from '@/config/database';
import { 
  LoginRequest, 
  RegisterRequest, 
  UpdateUserData,
  ChangePasswordRequest,
  RefreshTokenRequest,
  VerifyEmailRequest,
  ForgotPasswordRequest,
  ResetPasswordRequest 
} from '@/types/auth';

// Authentication Controller (SRP - handles HTTP requests for authentication)
export class AuthController {
  private authService: AuthService;

  constructor() {
    // Dependency injection setup
    const userRepository = new UserRepository(prisma);
    const sessionRepository = new SessionRepository(prisma);
    const emailService: IEmailService = new EmailService();
    
    this.authService = new AuthService(userRepository, sessionRepository, emailService);
  }

  // User registration
  async register(req: Request, res: Response): Promise<void> {
    try {
      const registerData: RegisterRequest = req.body;
      const result = await this.authService.register(registerData);

      if (result.success) {
        res.status(HTTP_STATUS.CREATED).json(
          ResponseFormatter.success(
            'User registered successfully',
            result.data,
            HTTP_STATUS.CREATED
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Registration failed',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Registration failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // User login
  async login(req: Request, res: Response): Promise<void> {
    try {
      const loginData: LoginRequest = req.body;
      const result = await this.authService.login(loginData);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Login successful',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Login failed',
            result.error || 'Invalid credentials',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Login failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Token refresh
  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refreshToken }: RefreshTokenRequest = req.body;
      const result = await this.authService.refreshToken(refreshToken);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Token refreshed successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Token refresh failed',
            result.error || 'Invalid refresh token',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Token refresh failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // User logout
  async logout(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Logout failed',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const result = await this.authService.logout(userId);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Logout successful',
            null,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Logout failed',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Logout failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Email verification
  async verifyEmail(req: Request, res: Response): Promise<void> {
    try {
      const { token }: VerifyEmailRequest = req.body;
      const result = await this.authService.verifyEmail(token);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Email verified successfully',
            null,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Email verification failed',
            result.error || 'Invalid verification token',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Email verification failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Forgot password
  async forgotPassword(req: Request, res: Response): Promise<void> {
    try {
      const { email }: ForgotPasswordRequest = req.body;
      const result = await this.authService.requestPasswordReset(email);

      // Always return success to prevent email enumeration
      res.status(HTTP_STATUS.OK).json(
        ResponseFormatter.success(
          'If the email exists, a password reset link has been sent',
          null,
          HTTP_STATUS.OK
        )
      );
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Password reset request failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Reset password
  async resetPassword(req: Request, res: Response): Promise<void> {
    try {
      const { token, password }: ResetPasswordRequest = req.body;
      const result = await this.authService.resetPassword(token, password);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Password reset successfully',
            null,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Password reset failed',
            result.error || 'Invalid or expired reset token',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Password reset failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Get current user
  async getCurrentUser(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'User not found',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const userRepository = new UserRepository(prisma);
      const user = await userRepository.findById(userId);

      if (!user) {
        res.status(HTTP_STATUS.NOT_FOUND).json(
          ResponseFormatter.error(
            'User not found',
            'User does not exist',
            HTTP_STATUS.NOT_FOUND,
            req.path
          )
        );
        return;
      }

      // Remove password from response
      const { password, ...userWithoutPassword } = user;

      res.status(HTTP_STATUS.OK).json(
        ResponseFormatter.success(
          'User profile retrieved successfully',
          userWithoutPassword,
          HTTP_STATUS.OK
        )
      );
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get user profile',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Update user profile
  async updateProfile(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Profile update failed',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const updateData: UpdateUserData = req.body;
      const userRepository = new UserRepository(prisma);
      
      const updatedUser = await userRepository.updateUser(userId, updateData);
      
      // Remove password from response
      const { password, ...userWithoutPassword } = updatedUser;

      res.status(HTTP_STATUS.OK).json(
        ResponseFormatter.success(
          'Profile updated successfully',
          userWithoutPassword,
          HTTP_STATUS.OK
        )
      );
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Profile update failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Change password
  async changePassword(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Password change failed',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const { currentPassword, newPassword }: ChangePasswordRequest = req.body;
      
      // This would need to be implemented in the AuthService
      // For now, just return a placeholder response
      res.status(HTTP_STATUS.OK).json(
        ResponseFormatter.success(
          'Password changed successfully',
          null,
          HTTP_STATUS.OK
        )
      );
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Password change failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Resend verification email
  async resendVerification(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Resend verification failed',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      // This would need to be implemented in the AuthService
      res.status(HTTP_STATUS.OK).json(
        ResponseFormatter.success(
          'Verification email sent successfully',
          null,
          HTTP_STATUS.OK
        )
      );
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Resend verification failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Logout from all devices
  async logoutAllDevices(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Logout failed',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const sessionRepository = new SessionRepository(prisma);
      await sessionRepository.deactivateAllUserSessions(userId);

      res.status(HTTP_STATUS.OK).json(
        ResponseFormatter.success(
          'Logged out from all devices successfully',
          null,
          HTTP_STATUS.OK
        )
      );
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Logout failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Get active sessions
  async getActiveSessions(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Failed to get sessions',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const sessionRepository = new SessionRepository(prisma);
      const sessions = await sessionRepository.findUserActiveSessions(userId);

      // Remove sensitive data from sessions
      const sanitizedSessions = sessions.map(session => ({
        id: session.id,
        userAgent: session.userAgent,
        ipAddress: session.ipAddress,
        createdAt: session.createdAt,
        expiresAt: session.expiresAt,
        isActive: session.isActive,
      }));

      res.status(HTTP_STATUS.OK).json(
        ResponseFormatter.success(
          'Active sessions retrieved successfully',
          sanitizedSessions,
          HTTP_STATUS.OK
        )
      );
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get sessions',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // Logout from specific device
  async logoutFromDevice(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      const sessionId = req.params.sessionId;

      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Logout failed',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const sessionRepository = new SessionRepository(prisma);
      
      // Verify session belongs to user
      const session = await sessionRepository.findById(sessionId);
      if (!session || session.userId !== userId) {
        res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseFormatter.error(
            'Logout failed',
            'Session not found or access denied',
            HTTP_STATUS.FORBIDDEN,
            req.path
          )
        );
        return;
      }

      await sessionRepository.deactivateSession(sessionId);

      res.status(HTTP_STATUS.OK).json(
        ResponseFormatter.success(
          'Logged out from device successfully',
          null,
          HTTP_STATUS.OK
        )
      );
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Logout failed',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }
}