import React from 'react';
import { Link } from 'react-router-dom';
import { useServices } from '../contexts/ServiceContext';
import ServiceCard from '../components/ServiceCard';
import {
  CheckCircle,
  Clock,
  Shield,
  Award,
  ChevronDown,
  ChevronUp,
  Star,
  Eye,
  MapPin,
  Megaphone,
  Building,
} from 'lucide-react';

const SignsBanners: React.FC = () => {
  const { services } = useServices();
  const [openFaq, setOpenFaq] = React.useState<number | null>(null);
  const [galleryScrollPosition, setGalleryScrollPosition] = React.useState(0);
  const galleryScrollRef = React.useRef<HTMLDivElement>(null);

  const signsServices = services.filter(
    (service) => service.category === 'Signs & Banners' && service.isActive
  );

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const scrollGallery = (direction: 'left' | 'right') => {
    if (galleryScrollRef.current) {
      const scrollAmount = 336; // Width of image (320px) + gap (16px)
      const currentScroll = galleryScrollRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      galleryScrollRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
      setGalleryScrollPosition(newScroll);
    }
  };

  const handleGalleryScroll = () => {
    if (galleryScrollRef.current) {
      setGalleryScrollPosition(galleryScrollRef.current.scrollLeft);
    }
  };

  const faqs = [
    {
      question: 'What types of signs and banners do you offer?',
      answer:
        'We offer vinyl banners, yard signs, window clings, vehicle decals, trade show displays, A-frame signs, and large format posters. All can be customized for indoor or outdoor use.',
    },
    {
      question: 'Are your banners weather-resistant?',
      answer:
        "Yes! Our vinyl banners are made with durable, weather-resistant materials that can withstand rain, wind, and UV exposure. They're perfect for outdoor advertising and events.",
    },
    {
      question: "What's the largest size banner you can print?",
      answer:
        'We can print banners up to 16 feet wide with virtually unlimited length. For larger installations, we can create multiple panels that seamlessly connect.',
    },
    {
      question: 'Do banners come with grommets or pole pockets?',
      answer:
        'Yes! We include grommets (metal eyelets) for easy hanging as standard. Pole pockets, wind slits, and other finishing options are available upon request.',
    },
    {
      question: 'How long do outdoor signs typically last?',
      answer:
        'Our outdoor signs are designed to last 3-5 years with proper care. UV-resistant inks and weather-proof materials ensure your message stays vibrant and readable.',
    },
    {
      question: 'Can you help with sign permits and regulations?',
      answer:
        "While we don't handle permits directly, we can provide specifications and guidance to help you understand local signage requirements. We recommend checking with your local authorities.",
    },
  ];

  const clientSamples = [
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      title: 'Grand Opening Banner',
      description: 'Large vinyl banner with vibrant colors and bold messaging',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      title: 'Trade Show Display',
      description: 'Professional backdrop banner for exhibition booth',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      title: 'Real Estate Yard Signs',
      description: 'Weather-resistant signs with stakes for property listings',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      title: 'Restaurant A-Frame',
      description: 'Double-sided sidewalk sign for daily specials',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Section 1: Hero with Headline and Image */}
      <section className="bg-white py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-600">
                Signs & Banners That Get Noticed
              </h1>
              <p className="text-xl mb-8 text-gray-700">
                From grand opening banners to professional yard signs, create
                eye-catching displays that attract customers and promote your
                business effectively.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="#services"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  View Options
                </Link>
                <Link
                  to="#samples"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  See Examples
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726834246629.jpg&w=1080&q=75"
                alt="Signs and Banners"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-2 rounded-full">
                    <Eye className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      High Visibility
                    </p>
                    <p className="text-sm text-gray-600">
                      Weather-resistant materials
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 2: Services on this Page */}
      <section id="services" className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Signs & Banners Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Professional signage solutions for indoor and outdoor advertising,
              events, and business promotion.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {signsServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>

          {signsServices.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">
                No signs and banners services available at the moment.
              </p>
            </div>
          )}
        </div>
      </section>

      {/* Section 3: Information About This Category */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Maximize Your Business Visibility
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Signs and banners are powerful tools for attracting customers
                and promoting your business. Whether you need outdoor
                advertising, event signage, or storefront displays, professional
                signage makes a lasting impression.
              </p>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Increased Foot Traffic
                    </h3>
                    <p className="text-gray-600">
                      Eye-catching signs draw customers to your business
                      location.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      24/7 Advertising
                    </h3>
                    <p className="text-gray-600">
                      Your message works around the clock to promote your
                      business.
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <CheckCircle className="h-6 w-6 text-green-500 mt-1" />
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Cost-Effective Marketing
                    </h3>
                    <p className="text-gray-600">
                      Long-lasting signage provides excellent return on
                      investment.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-blue-50 p-6 rounded-lg text-center">
                <MapPin className="h-8 w-8 text-blue-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Location Marketing
                </h3>
                <p className="text-sm text-gray-600">
                  Drive traffic to your location
                </p>
              </div>
              <div className="bg-green-50 p-6 rounded-lg text-center">
                <Shield className="h-8 w-8 text-green-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Weather Resistant
                </h3>
                <p className="text-sm text-gray-600">
                  Durable outdoor materials
                </p>
              </div>
              <div className="bg-purple-50 p-6 rounded-lg text-center">
                <Megaphone className="h-8 w-8 text-purple-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Brand Awareness
                </h3>
                <p className="text-sm text-gray-600">
                  Increase brand recognition
                </p>
              </div>
              <div className="bg-orange-50 p-6 rounded-lg text-center">
                <Building className="h-8 w-8 text-orange-600 mx-auto mb-3" />
                <h3 className="font-semibold text-gray-900 mb-2">
                  Professional Image
                </h3>
                <p className="text-sm text-gray-600">
                  Enhance business credibility
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      {/* Portfolio Section */}
      <section id="portfolio" className="w-full py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Client Love Content */}
            <div className="lg:col-span-1">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                What our clients say ...
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We're a global team of skilled designers united by a passion for design and a commitment to excellence.
              </p>
              
              {/* Statistics */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Star className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">High Visibility</div>
                    <div className="text-gray-600">Signs and banners that capture attention and drive traffic.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Shield className="h-8 w-8 text-green-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Weather Resistant</div>
                    <div className="text-gray-600">Durable materials that withstand outdoor conditions.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Award className="h-8 w-8 text-orange-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Custom Sizes</div>
                    <div className="text-gray-600">Any size from small yard signs to large format banners.</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Scrollable Portfolio */}
            <div className="lg:col-span-2">
              <div className="relative">
                {/* Left Navigation Button */}
                <button
                  onClick={() => scrollGallery('left')}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                    galleryScrollPosition <= 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-50 hover:shadow-xl'
                  }`}
                  disabled={galleryScrollPosition <= 0}
                >
                  <svg className="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>

                {/* Right Navigation Button */}
                <button
                  onClick={() => scrollGallery('right')}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:bg-gray-50 hover:shadow-xl"
                >
                  <svg className="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                {/* Scrollable Container */}
                <div 
                  ref={galleryScrollRef}
                  onScroll={handleGalleryScroll}
                  className="overflow-x-auto whitespace-nowrap py-4 px-12 scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                  <div className="flex space-x-6">
                    {clientSamples.map((sample, index) => (
                      <div
                        key={index}
                        className="inline-block w-64 bg-white rounded-lg shadow-lg overflow-hidden flex-shrink-0 hover:shadow-xl transition-shadow duration-300"
                      >
                        <img
                          src={sample.image}
                          alt={sample.title}
                          className="w-full h-48 object-cover"
                        />
                        <div className="p-6">
                          <h3 className="font-semibold text-gray-900 mb-2 whitespace-normal">
                            {sample.title}
                          </h3>
                          <p className="text-gray-600 text-sm mb-4 whitespace-normal">{sample.description}</p>
                          
                          {/* Star Rating */}
                          <div className="flex items-center mb-4">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className="h-4 w-4 text-yellow-400 fill-current"
                              />
                            ))}
                            <span className="text-sm text-gray-500 ml-2">5.0</span>
                          </div>
                          
                          {/* Reviewer Info */}
                          <div className="flex items-center space-x-3">
                            <img
                              src={sample.image}
                              alt="Client"
                              className="w-8 h-8 rounded-full object-cover"
                            />
                            <p className="text-sm font-medium text-gray-900 whitespace-normal">
                              Happy Client
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-orange-100 p-8 rounded-lg">
              <h3 className="text-xl font-semibold text-gray-900 mb-4">
                Ready to Increase Your Visibility?
              </h3>
              <p className="text-gray-600 mb-6">
                Join businesses who trust us with their signage needs.
              </p>
              <Link
                to="#services"
                className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
              >
                Get Started Now
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-20 bg-white">
        <div className="w-full">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Design - Create - Inspire
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Check us out on social media for creative ways to design your own products
            </p>
          </div>

          <div className="relative">
            {/* Left Navigation Arrow */}
            <button 
              onClick={() => scrollGallery('left')}
              className={`absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                galleryScrollPosition <= 0
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-gray-50 hover:shadow-xl'
              }`}
              disabled={galleryScrollPosition <= 0}
            >
              <svg className="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            {/* Right Navigation Arrow */}
            <button 
              onClick={() => scrollGallery('right')}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200 flex items-center justify-center hover:bg-gray-50 transition-all duration-200"
            >
              <svg className="h-6 w-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>

            {/* Gallery Container */}
            <div 
              ref={galleryScrollRef}
              onScroll={handleGalleryScroll}
              className="overflow-x-auto px-16 scrollbar-hide"
              style={{
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
              }}
            >
              <div className="flex space-x-4">
                {/* Gallery Images */}
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex-shrink-0 w-80 h-64 rounded-lg overflow-hidden">
                  <img
                    src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725923064812.jpg&w=1080&q=75"
                    alt="Signs and banners design inspiration"
                    className="w-full h-full object-cover"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Section 5: FAQ Section */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-gray-600">
              Everything you need to know about our signs and banners printing
              services.
            </p>
          </div>

          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white rounded-lg shadow-lg">
                <button
                  onClick={() => toggleFaq(index)}
                  className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 transition-colors"
                >
                  <span className="font-semibold text-gray-900">
                    {faq.question}
                  </span>
                  {openFaq === index ? (
                    <ChevronUp className="h-5 w-5 text-gray-500" />
                  ) : (
                    <ChevronDown className="h-5 w-5 text-gray-500" />
                  )}
                </button>
                {openFaq === index && (
                  <div className="px-6 pb-4">
                    <p className="text-gray-600">{faq.answer}</p>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="text-center mt-12">
            <p className="text-gray-600 mb-4">Still have questions?</p>
            <Link
              to="/contact"
              className="text-blue-600 hover:text-blue-700 font-semibold"
            >
              Contact our support team →
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SignsBanners;