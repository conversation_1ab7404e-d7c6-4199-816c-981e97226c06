# PrintWeditt Backend API

A robust, scalable backend API for the PrintWeditt platform built with Node.js,
Express, TypeScript, and Prisma following clean architecture principles.

## 🏗️ Architecture

This backend follows **Clean Architecture** principles with clear separation of
concerns:

```
src/
├── controllers/          # HTTP route handlers (thin layer)
├── services/            # Business logic layer
├── repositories/        # Data access layer with interfaces
├── types/              # Domain models and Zod schemas
├── middleware/         # Authentication, validation, error handling
├── routes/             # API route definitions
├── config/             # Configuration and environment setup
└── utils/              # Helper functions and utilities
```

### Architecture Principles

- **Dependency Inversion**: Services depend on repository interfaces, not
  concrete implementations
- **Single Responsibility**: Each class has one reason to change
- **Open/Closed**: Open for extension, closed for modification
- **Interface Segregation**: Clients depend only on interfaces they use
- **Dependency Injection**: All dependencies are injected through constructors

## 🚀 Features

### ✅ Implemented

- **Authentication System**

  - JWT-based authentication with refresh tokens
  - Password hashing with bcrypt
  - Role-based access control (Customer, Provider, Admin)
  - Session management
  - Rate limiting protection

- **User Management**

  - User registration and login
  - Profile management
  - Address management
  - Email verification (ready for implementation)

- **Security Features**

  - Helmet.js for security headers
  - CORS configuration
  - Rate limiting
  - Input validation with Zod
  - Error handling middleware

- **Database Integration**
  - PostgreSQL with Prisma ORM
  - Comprehensive database schema
  - Repository pattern for data access
  - Transaction support

### 🔄 In Progress

- Service management APIs
- Provider marketplace functionality
- Order processing workflows
- File upload system
- Admin dashboard APIs

## 🛠️ Technology Stack

- **Runtime**: Node.js 18+
- **Framework**: Express.js
- **Language**: TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT with refresh tokens
- **Validation**: Zod schemas
- **Security**: Helmet, CORS, Rate limiting
- **Testing**: Jest (configured)
- **Documentation**: OpenAPI/Swagger (planned)

## 📋 Prerequisites

- Node.js 18+
- PostgreSQL 12+
- Redis (optional, for caching)

## 🚀 Quick Start

### 1. Clone and Install

```bash
cd backend
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
```

Edit `.env` with your configuration:

```env
DATABASE_URL="postgresql://username:password@localhost:5432/printweditt_db"
JWT_SECRET="your-super-secret-jwt-key"
JWT_REFRESH_SECRET="your-super-secret-jwt-refresh-key"
```

### 3. Database Setup

```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# (Optional) Run migrations
npm run db:migrate
```

### 4. Start Development Server

```bash
npm run dev
```

The server will start on `http://localhost:5000`

## 📚 API Documentation

### Authentication Endpoints

#### Register User

```http
POST /api/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "SecurePass123",
  "role": "CUSTOMER"
}
```

#### Login

```http
POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "SecurePass123"
}
```

#### Refresh Token

```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "your-refresh-token"
}
```

#### Get Current User

```http
GET /api/auth/me
Authorization: Bearer your-access-token
```

### Health Check

```http
GET /health
```

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev              # Start development server with hot reload
npm run build            # Build for production
npm run start            # Start production server

# Database
npm run db:generate      # Generate Prisma client
npm run db:push          # Push schema to database
npm run db:migrate       # Run migrations
npm run db:studio        # Open Prisma Studio
npm run db:seed          # Seed database

# Testing
npm run test             # Run tests
npm run test:watch       # Run tests in watch mode

# Linting
npm run lint             # Run ESLint
npm run lint:fix         # Fix ESLint issues
```

### Project Structure

```
backend/
├── src/
│   ├── controllers/     # HTTP request handlers
│   │   ├── auth.controller.ts
│   │   ├── user.controller.ts
│   │   └── ...
│   ├── services/        # Business logic
│   │   ├── interfaces/  # Service interfaces
│   │   ├── implementations/ # Service implementations
│   │   └── ...
│   ├── repositories/    # Data access layer
│   │   ├── interfaces/  # Repository interfaces
│   │   ├── implementations/ # Repository implementations
│   │   └── ...
│   ├── types/          # Domain types and schemas
│   │   ├── auth.types.ts
│   │   ├── user.types.ts
│   │   └── ...
│   ├── middleware/     # Express middleware
│   │   ├── auth.middleware.ts
│   │   ├── error.middleware.ts
│   │   └── ...
│   ├── routes/         # Route definitions
│   │   ├── auth.routes.ts
│   │   └── ...
│   ├── config/         # Configuration
│   │   ├── app.ts
│   │   ├── database.ts
│   │   └── ...
│   └── utils/          # Utility functions
├── prisma/             # Database schema and migrations
│   └── schema.prisma
├── tests/              # Test files
├── package.json
├── tsconfig.json
└── README.md
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcrypt with salt rounds
- **Rate Limiting**: Protection against brute force attacks
- **CORS**: Configurable cross-origin resource sharing
- **Helmet**: Security headers
- **Input Validation**: Zod schema validation
- **Error Handling**: Centralized error management

## 🧪 Testing

The project is configured with Jest for testing:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test -- --coverage
```

## 📦 Deployment

### Production Build

```bash
npm run build
npm start
```

### Environment Variables

Ensure all required environment variables are set in production:

- `DATABASE_URL`
- `JWT_SECRET`
- `JWT_REFRESH_SECRET`
- `NODE_ENV=production`
- `PORT`

### Docker (Optional)

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 5000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Follow the existing code structure and patterns
2. Use TypeScript for all new code
3. Write tests for new features
4. Follow the established naming conventions
5. Use Zod for input validation
6. Implement proper error handling

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For questions or issues, please refer to the project documentation or create an
issue in the repository.
