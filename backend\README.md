# PrintWeditt Backend API

A modern, scalable backend API built with Node.js, TypeScript, and Express.js following SOLID principles and clean architecture patterns. This backend complements and mirrors the frontend architecture while providing robust, secure, and maintainable server-side functionality.

## 🏗️ Architecture Overview

### Design Principles

This backend implementation strictly follows **SOLID principles** and modern software engineering best practices:

1. **Single Responsibility Principle (SRP)**: Each module, class, and function has one clear, well-defined purpose
2. **Open/Closed Principle**: Components are open for extension but closed for modification through interfaces and dependency injection
3. **Liskov Substitution Principle**: Derived classes are substitutable for their base classes via consistent interfaces
4. **Interface Segregation Principle**: Specific, focused interfaces rather than large, monolithic ones
5. **Dependency Inversion Principle**: Dependencies on abstractions rather than concretions

### Additional Design Principles

- **Don't Repeat Yourself (DRY)**: Reusable components and utilities eliminate code duplication
- **Separation of Concerns**: Clear boundaries between API routes, business logic, data access, and utilities
- **Convention over Configuration**: Consistent patterns and naming conventions throughout
- **Type Safety**: Comprehensive TypeScript types for enhanced development experience

## 📁 Folder Structure

```
src/
├── controllers/          # API route handlers (SRP)
│   ├── auth/            # Authentication controllers
│   ├── services/        # Service management controllers
│   ├── providers/       # Provider management controllers
│   └── admin/           # Admin controllers
├── services/            # Business logic layer (SRP)
│   ├── auth/           # Authentication services
│   ├── services/       # Service management services
│   ├── providers/      # Provider services
│   ├── email/          # Email services
│   └── file-upload/    # File upload services
├── repositories/        # Data access layer (SRP)
│   ├── auth/           # User & session repositories
│   ├── services/       # Service repositories
│   └── providers/      # Provider repositories
├── middleware/          # Express middleware (SRP)
│   ├── auth/           # Authentication & authorization
│   ├── validation/     # Request validation
│   └── error-handling/ # Error handling & logging
├── types/              # TypeScript type definitions
│   ├── auth/           # Authentication types
│   ├── services/       # Service-related types
│   ├── providers/      # Provider types
│   └── common/         # Shared types
├── utils/              # Utility functions (DRY)
│   ├── validation/     # Validation utilities
│   ├── crypto/         # Cryptographic utilities
│   └── formatting/     # Response & data formatting
├── constants/          # Application constants
├── config/             # Configuration files
│   ├── database/       # Database configuration
│   ├── auth/           # Authentication configuration
│   └── storage/        # File storage configuration
└── routes/             # API route definitions
    ├── auth/           # Authentication routes
    ├── services/       # Service routes
    ├── providers/      # Provider routes
    └── admin/          # Admin routes
```

## 🎯 Frontend-Backend Architecture Alignment

This backend architecture directly mirrors and complements the frontend structure:

### Frontend → Backend Mapping

| Frontend Layer | Backend Layer | Purpose |
|----------------|---------------|---------|
| `types/` | `types/` | Shared type definitions |
| `contexts/` | `services/` | Business logic & state management |
| `components/` | `controllers/` | User interface ↔ API endpoints |
| `utils/` | `utils/` | Shared utility functions |
| `constants/` | `constants/` | Application constants |
| `pages/auth/` | `routes/auth/` | Authentication flow |
| `pages/services/` | `routes/services/` | Service management |
| `pages/dashboard/` | `routes/providers/` | Provider functionality |

### Complementary Design Patterns

1. **Type Consistency**: Backend types extend frontend types with server-specific fields
2. **API Route Mapping**: Backend routes mirror frontend page structure
3. **Error Handling**: Consistent error formats between frontend and backend
4. **Validation**: Shared validation logic using similar patterns
5. **Business Logic**: Backend services implement the logic that frontend contexts simulate

## 🔐 Security Implementation

### Authentication & Authorization

- **JWT-based authentication** with refresh tokens
- **Role-based access control** (user, provider, admin)
- **Rate limiting** with different limits for different endpoints
- **Password security** with bcrypt hashing
- **Email verification** and password reset flows
- **Session management** with secure token handling

### Security Middleware

- **Helmet.js** for security headers
- **CORS** configuration for cross-origin requests
- **Request validation** with express-validator
- **File upload validation** with type and size checks
- **SQL injection prevention** through parameterized queries
- **XSS protection** through input sanitization

## 🚀 SOLID Principles in Action

### Single Responsibility Principle (SRP)

Each component has one clear purpose:
- `AuthService`: Only handles authentication logic
- `ServiceManagementService`: Only manages service operations
- `PasswordService`: Only handles password operations
- `ValidationRules`: Only defines validation rules

### Open/Closed Principle

Components are extensible without modification:
- `ValidationChains`: New validation chains can be added without changing existing ones
- `AuthorizationMiddleware`: New roles can be added via configuration
- `ResponseFormatter`: New response types can be added without changing core logic

### Liskov Substitution Principle

Consistent interfaces enable substitution:
- All repositories implement consistent CRUD interfaces
- All services return `BusinessLogicResult<T>` for consistent error handling
- All controllers follow similar request/response patterns

### Interface Segregation Principle

Focused, specific interfaces:
- `IUserRepository`: Only user-related database operations
- `IEmailService`: Only email-related operations
- `IFileUploadService`: Only file upload operations

### Dependency Inversion Principle

Dependencies on abstractions:
- Services depend on repository interfaces, not implementations
- Controllers depend on service interfaces
- Middleware depends on utility interfaces

## 📊 API Endpoints

### Authentication (`/api/v1/auth`)
- `POST /login` - User authentication
- `POST /register` - User registration
- `POST /logout` - User logout
- `POST /refresh` - Token refresh
- `POST /verify-email` - Email verification
- `POST /forgot-password` - Password reset request
- `PUT /reset-password` - Password reset

### Services (`/api/v1/services`)
- `GET /` - List services with filtering and pagination
- `GET /:id` - Get service details
- `POST /` - Create new service (provider only)
- `PUT /:id` - Update service (owner/admin only)
- `DELETE /:id` - Delete service (owner/admin only)
- `GET /categories` - List service categories
- `GET /featured` - Get featured services
- `GET /search` - Advanced service search

### Providers (`/api/v1/providers`)
- `GET /` - List providers
- `GET /:id` - Get provider details
- `POST /register` - Provider registration
- `PUT /:id` - Update provider profile
- `GET /:id/services` - Get provider's services
- `GET /:id/reviews` - Get provider reviews

### Admin (`/api/v1/admin`)
- `GET /users` - Manage users
- `GET /providers` - Manage providers
- `GET /services` - Manage services
- `GET /analytics` - System analytics

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+
- TypeScript 5+
- PostgreSQL (or your preferred database)
- Redis (optional, for caching)

### Installation

```bash
# Clone the repository
cd backend

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Configure your environment variables
# Edit .env with your database and other configurations

# Generate Prisma client (if using Prisma)
npm run db:generate

# Run database migrations
npm run migrate

# Seed the database (optional)
npm run db:seed
```

### Development

```bash
# Start development server with hot reload
npm run dev

# Build for production
npm run build

# Start production server
npm start

# Run tests
npm test

# Run linting
npm run lint
```

## 🧪 Testing Strategy

### Unit Tests
- Service layer logic testing
- Utility function testing
- Validation rule testing
- Authentication flow testing

### Integration Tests
- API endpoint testing
- Database operation testing
- Middleware integration testing
- Authentication middleware testing

### Test Structure
```
tests/
├── unit/
│   ├── services/
│   ├── utils/
│   └── middleware/
├── integration/
│   ├── auth/
│   ├── services/
│   └── providers/
└── fixtures/
    ├── users.json
    ├── services.json
    └── providers.json
```

## 📈 Performance Considerations

### Optimization Strategies
- **Response compression** with gzip
- **Database query optimization** with proper indexing
- **Caching strategy** with Redis for frequently accessed data
- **Rate limiting** to prevent abuse
- **File upload optimization** with size and type restrictions
- **Pagination** for large data sets

### Monitoring
- **Request logging** with structured format
- **Error tracking** with stack traces
- **Performance metrics** collection
- **Health check endpoints** for monitoring

## 🔄 Deployment

### Production Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] SSL certificates installed
- [ ] Rate limiting configured
- [ ] Error monitoring setup
- [ ] Backup strategy implemented
- [ ] Health checks configured

### Docker Deployment
```dockerfile
# Dockerfile example
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3001
CMD ["node", "dist/app.js"]
```

## 📋 Contributing

### Code Standards
- Follow TypeScript strict mode
- Use ESLint configuration
- Write unit tests for new features
- Follow SOLID principles
- Document complex business logic
- Use meaningful commit messages

### Pull Request Process
1. Create feature branch from main
2. Implement changes following SOLID principles
3. Add comprehensive tests
4. Update documentation if needed
5. Submit pull request with clear description

## 📚 API Documentation

Detailed API documentation is available at `/api/v1/docs` when running the server. The documentation includes:

- Complete endpoint specifications
- Request/response examples
- Authentication requirements
- Error response formats
- Rate limiting information

## 🆘 Troubleshooting

### Common Issues

**Database Connection Issues**
- Verify DATABASE_URL in .env
- Ensure database server is running
- Check network connectivity

**Authentication Issues**
- Verify JWT_SECRET is set
- Check token expiration settings
- Validate CORS configuration

**File Upload Issues**
- Check file size limits
- Verify allowed file types
- Ensure upload directory permissions

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

---

Built with ❤️ following SOLID principles and modern software engineering best practices.