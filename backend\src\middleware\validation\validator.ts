// Validation Error Handler - Following SRP principle
import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import { ResponseFormatter } from '@/utils/formatting';
import { HTTP_STATUS } from '@/constants';

// Main validation error handler (SRP)
export const handleValidation = (req: Request, res: Response, next: NextFunction): void => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const formattedErrors = errors.array().map(error => ({
      field: error.param || 'unknown',
      message: error.msg,
      value: error.value,
      location: error.location,
    }));
    
    res.status(HTTP_STATUS.VALIDATION_ERROR).json(
      ResponseFormatter.error(
        'Validation failed',
        'One or more fields contain invalid data',
        HTTP_STATUS.VALIDATION_ERROR,
        req.path,
        {
          validationErrors: formattedErrors,
          errorCount: formattedErrors.length,
        }
      )
    );
    return;
  }
  
  next();
};