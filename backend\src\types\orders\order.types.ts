// Order Types - Following established patterns and SOLID principles
import {OrderStatus, PaymentStatus, ServicePriceType} from '@prisma/client';

// Base Order Interface (LSP - Liskov Substitution Principle)
export interface BaseOrder {
	id: string;
	orderNumber: string;
	serviceId: string;
	userId: string;
	providerId: string;
	status: OrderStatus;
	totalAmount: number;
	paymentStatus: PaymentStatus;
	paymentMethod?: string;
	customerNotes?: string;
	providerNotes?: string;
	scheduledDate?: Date;
	completedDate?: Date;
	cancelledDate?: Date;
	cancellationReason?: string;
	files: string[];
	createdAt: Date;
	updatedAt: Date;
}

// Order with Relations Interface (ISP - Interface Segregation Principle)
export interface OrderWithRelations extends BaseOrder {
	service: {
		id: string;
		name: string;
		description: string;
		price: number;
		priceType: ServicePriceType;
		images: string[];
		provider: {
			id: string;
			businessName: string;
			rating?: number;
		};
	};
	user: {
		id: string;
		name: string;
		email: string;
		profileImage?: string;
	};
	provider: {
		id: string;
		businessName: string;
		rating?: number;
		totalReviews: number;
	};
	review?: {
		id: string;
		rating: number;
		comment?: string;
		createdAt: Date;
	};
}

// Data Transfer Objects (DTOs) for API operations
export interface CreateOrderRequest {
	serviceId: string;
	customerNotes?: string;
	scheduledDate?: Date;
	files?: Express.Multer.File[];
	customRequirements?: string;
	quantity?: number;
	specialInstructions?: string;
}

export interface UpdateOrderRequest {
	customerNotes?: string;
	providerNotes?: string;
	scheduledDate?: Date;
	status?: OrderStatus;
	paymentStatus?: PaymentStatus;
	cancellationReason?: string;
	files?: Express.Multer.File[];
}

// Order Status Update Request (SRP)
export interface OrderStatusUpdateRequest {
	status: OrderStatus;
	notes?: string;
	scheduledDate?: Date;
	completedDate?: Date;
	cancelledDate?: Date;
	cancellationReason?: string;
}

// Payment Update Request (SRP)
export interface PaymentUpdateRequest {
	paymentStatus: PaymentStatus;
	paymentMethod?: string;
	transactionId?: string;
	notes?: string;
}

// Order Search and Filter Types (OCP - Open/Closed Principle)
export interface OrderFilters {
	status?: OrderStatus[];
	paymentStatus?: PaymentStatus[];
	serviceId?: string;
	providerId?: string;
	userId?: string;
	minAmount?: number;
	maxAmount?: number;
	scheduledDateFrom?: Date;
	scheduledDateTo?: Date;
	createdAtFrom?: Date;
	createdAtTo?: Date;
	completedDateFrom?: Date;
	completedDateTo?: Date;
	sortBy?: OrderSortField;
	sortOrder?: 'asc' | 'desc';
}

export interface OrderSearchRequest {
	query?: string;
	filters?: OrderFilters;
	pagination?: {
		page: number;
		limit: number;
	};
}

// Order Sort Options (OCP)
export type OrderSortField =
	| 'orderNumber'
	| 'totalAmount'
	| 'status'
	| 'createdAt'
	| 'scheduledDate'
	| 'completedDate';

// Business Logic Types (SRP)
export interface OrderBusinessRules {
	maxCustomerNotesLength: number;
	maxProviderNotesLength: number;
	maxCancellationReasonLength: number;
	maxFilesPerOrder: number;
	maxFileSize: number; // in bytes
	allowedFileTypes: string[];
	minScheduledDateAdvance: number; // in hours
	maxScheduledDateAdvance: number; // in days
	autoCancelAfterHours: number;
	refundPolicy: {
		fullRefundWithinHours: number;
		partialRefundWithinHours: number;
		noRefundAfterHours: number;
	};
}

// Validation Error Types (SRP)
export interface OrderValidationError {
	field: string;
	message: string;
	code: string;
}

// Order Metrics Interface (SRP)
export interface OrderMetrics {
	totalOrders: number;
	pendingOrders: number;
	confirmedOrders: number;
	inProgressOrders: number;
	completedOrders: number;
	cancelledOrders: number;
	refundedOrders: number;
	totalRevenue: number;
	averageOrderValue: number;
	completionRate: number;
	cancellationRate: number;
	averageProcessingTime: number; // in hours
	topServices: Array<{
		serviceId: string;
		serviceName: string;
		orderCount: number;
		revenue: number;
	}>;
	monthlyStats: Array<{
		month: string;
		orders: number;
		revenue: number;
	}>;
}

// Order Statistics Interface (SRP)
export interface OrderStatistics {
	totalOrders: number;
	totalRevenue: number;
	averageOrderValue: number;
	ordersByStatus: Record<OrderStatus, number>;
	ordersByPaymentStatus: Record<PaymentStatus, number>;
	averageProcessingTime: number;
	completionRate: number;
	cancellationRate: number;
	topProviders: Array<{
		providerId: string;
		providerName: string;
		orderCount: number;
		revenue: number;
	}>;
	topServices: Array<{
		serviceId: string;
		serviceName: string;
		orderCount: number;
		revenue: number;
	}>;
}

// Event Types for business logic (SRP)
export interface OrderCreatedEvent {
	orderId: string;
	serviceId: string;
	userId: string;
	providerId: string;
	totalAmount: number;
	timestamp: Date;
}

export interface OrderStatusChangedEvent {
	orderId: string;
	previousStatus: OrderStatus;
	newStatus: OrderStatus;
	changedBy: string;
	notes?: string;
	timestamp: Date;
}

export interface OrderCancelledEvent {
	orderId: string;
	cancelledBy: string;
	reason?: string;
	refundAmount?: number;
	timestamp: Date;
}

export interface OrderCompletedEvent {
	orderId: string;
	completedBy: string;
	completionNotes?: string;
	timestamp: Date;
}

export interface PaymentProcessedEvent {
	orderId: string;
	paymentStatus: PaymentStatus;
	paymentMethod: string;
	transactionId?: string;
	amount: number;
	timestamp: Date;
}

// Utility Types (DRY - Don't Repeat Yourself)
export type OrderAction =
	| 'confirm'
	| 'start'
	| 'complete'
	| 'cancel'
	| 'refund';

export interface OrderActionRequest {
	action: OrderAction;
	notes?: string;
	scheduledDate?: Date;
	cancellationReason?: string;
	refundAmount?: number;
}

// Extended Types for specific use cases (OCP)
export interface OrderWithMetrics extends OrderWithRelations {
	metrics: {
		processingTime: number; // in hours
		isOverdue: boolean;
		daysUntilScheduled: number;
		canBeCancelled: boolean;
		canBeRefunded: boolean;
		refundAmount: number;
	};
	timeline: Array<{
		action: string;
		timestamp: Date;
		performedBy: string;
		notes?: string;
	}>;
}

export interface OrderSearchResult {
	orders: OrderWithMetrics[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}

// Order Timeline Interface (SRP)
export interface OrderTimelineEntry {
	id: string;
	orderId: string;
	action: string;
	status: OrderStatus;
	notes?: string;
	performedBy: string;
	timestamp: Date;
	metadata?: Record<string, any>;
}

// Order Notification Types (SRP)
export interface OrderNotification {
	type:
		| 'order_created'
		| 'order_confirmed'
		| 'order_started'
		| 'order_completed'
		| 'order_cancelled'
		| 'payment_received';
	orderId: string;
	recipientId: string;
	recipientType: 'customer' | 'provider' | 'admin';
	message: string;
	data?: Record<string, any>;
	timestamp: Date;
}

// Order Review Interface (SRP)
export interface OrderReview {
	id: string;
	orderId: string;
	rating: number;
	comment?: string;
	isPublic: boolean;
	isVerified: boolean;
	createdAt: Date;
	updatedAt: Date;
}

// Order Refund Interface (SRP)
export interface OrderRefund {
	id: string;
	orderId: string;
	amount: number;
	reason: string;
	processedBy: string;
	transactionId?: string;
	notes?: string;
	createdAt: Date;
}

// Order Invoice Interface (SRP)
export interface OrderInvoice {
	id: string;
	orderId: string;
	invoiceNumber: string;
	amount: number;
	taxAmount: number;
	totalAmount: number;
	currency: string;
	status: 'draft' | 'sent' | 'paid' | 'overdue' | 'cancelled';
	dueDate: Date;
	paidDate?: Date;
	createdAt: Date;
	updatedAt: Date;
}
