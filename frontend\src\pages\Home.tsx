import React from 'react';
import { Link } from 'react-router-dom';
import { useServices } from '../contexts/ServiceContext';
import ServiceThumbnailCard from '../components/ServiceThumbnailCard';
import ProductCardHome from '../components/ProductCardHome';
import { ArrowRight, ChevronLeft, ChevronRight } from 'lucide-react';
import { shuffleArray } from '../utils/arrayUtils';

const Home: React.FC = () => {
  const { services } = useServices();
  const [currentIndex, setCurrentIndex] = React.useState(0);
  const [currentIndexFeatured, setCurrentIndexFeatured] = React.useState(0);
  const [activeCategory, setActiveCategory] = React.useState('Business Cards');
  const [currentHeroImageIndex, setCurrentHeroImageIndex] = React.useState(0);

  // Hero images array
  const heroImages = [
    {
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      alt: 'Professional Printing Services',
    },
    {
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      alt: 'Custom Design Solutions',
    },
    {
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      alt: 'Marketing Materials',
    },
    {
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      alt: 'Business Branding',
    },
  ];

  // Get active services for the carousel
  const activeServices = React.useMemo(() => {
    return services.filter((service) => service.isActive);
  }, [services]);

  // Get random services for the 4x4 grid (16 services)
  const randomServices = React.useMemo(() => {
    return shuffleArray(activeServices).slice(0, 16);
  }, [activeServices]);

  const handlePrevious = () => {
    setCurrentIndex((prev) => Math.max(0, prev - 1));
  };

  const handleNext = () => {
    const maxIndex = Math.max(0, activeServices.length - getVisibleCount());
    setCurrentIndex((prev) => Math.min(maxIndex, prev + 1));
  };

  const handlePreviousFeatured = () => {
    setCurrentIndexFeatured((prev) => Math.max(0, prev - 1));
  };

  const handleNextFeatured = () => {
    const maxIndex = Math.max(
      0,
      activeServices.length - getVisibleCountFeatured()
    );
    setCurrentIndexFeatured((prev) => Math.min(maxIndex, prev + 1));
  };
  const getVisibleCount = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth >= 1536) return 8; // 2xl
      if (window.innerWidth >= 1280) return 8; // xl
      if (window.innerWidth >= 1024) return 6; // lg
      if (window.innerWidth >= 768) return 4; // md
      if (window.innerWidth >= 640) return 3; // sm
      return 2; // mobile
    }
    return 8; // default for SSR
  };

  const getVisibleCountFeatured = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth >= 1536) return 5; // 2xl
      if (window.innerWidth >= 1280) return 4; // xl
      if (window.innerWidth >= 1024) return 3; // lg
      if (window.innerWidth >= 768) return 2; // md
      return 1; // mobile
    }
    return 5; // default for SSR
  };
  React.useEffect(() => {
    const handleResize = () => {
      const maxIndex = Math.max(0, activeServices.length - getVisibleCount());
      if (currentIndex > maxIndex) {
        setCurrentIndex(maxIndex);
      }
      const maxIndexFeatured = Math.max(
        0,
        activeServices.length - getVisibleCountFeatured()
      );
      if (currentIndexFeatured > maxIndexFeatured) {
        setCurrentIndexFeatured(maxIndexFeatured);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [currentIndex, currentIndexFeatured, activeServices.length]);

  // Auto-advance hero images every 5 seconds
  React.useEffect(() => {
    const interval = setInterval(() => {
      setCurrentHeroImageIndex((prev) => (prev + 1) % heroImages.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [heroImages.length]);

  // Get unique categories for filter buttons
  const allCategories = React.useMemo(() => {
    const categories = [
      ...new Set(activeServices.map((service) => service.category)),
    ];
    return categories.filter(
      (category) => category !== 'Bags' && category !== 'Design Services'
    );
  }, [activeServices]);

  // Filter services based on active category
  const displayedServices = React.useMemo(() => {
    return activeServices
      .filter((service) => service.category === activeCategory)
      .slice(0, 4);
  }, [activeServices, activeCategory]);

  return (
    <div className="min-h-screen">
      {/* Hero Section with Background Image */}
      <section className="bg-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
                Professional Printing Services
              </h1>
              <p className="text-xl mb-8 text-gray-600">
                From business cards to large format printing, we deliver quality
                results that make your business stand out.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/services"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  Browse Services
                </Link>
                <Link
                  to="/design-services"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  Get Design
                </Link>
              </div>
            </div>
            <div className="relative">
              {/* Image Slider Container */}
              <div className="relative overflow-hidden rounded-lg shadow-2xl">
                <div className="relative h-96 md:h-[500px]">
                  {heroImages.map((image, index) => (
                    <div
                      key={index}
                      className={`absolute inset-0 transition-opacity duration-500 ease-in-out ${
                        index === currentHeroImageIndex
                          ? 'opacity-100'
                          : 'opacity-0'
                      }`}
                    >
                      <img
                        src={image.url}
                        alt={image.alt}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>

                {/* Navigation Dots */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                  {heroImages.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentHeroImageIndex(index)}
                      className={`w-3 h-3 rounded-full transition-all duration-300 ${
                        index === currentHeroImageIndex
                          ? 'bg-white scale-110'
                          : 'bg-white/50 hover:bg-white/75'
                      }`}
                      aria-label={`View image ${index + 1}`}
                    />
                  ))}
                </div>
              </div>

              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">99%</div>
                  <div className="text-sm text-gray-600">
                    Customer Satisfaction
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Category Grid Section */}
      <section className="py-16" style={{ backgroundColor: '#f5fbff' }}>
        <div className="px-4 sm:px-6 lg:px-8">
          <div className="relative">
            {/* Navigation Arrows */}
            <button
              onClick={handlePrevious}
              disabled={currentIndex === 0}
              className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                currentIndex === 0
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-orange-50 hover:shadow-xl'
              }`}
            >
              <ChevronLeft className="h-6 w-6 text-gray-600" />
            </button>

            <button
              onClick={handleNext}
              disabled={
                currentIndex >= activeServices.length - getVisibleCount()
              }
              className={`absolute right-0 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                currentIndex >= activeServices.length - getVisibleCount()
                  ? 'opacity-50 cursor-not-allowed'
                  : 'hover:bg-orange-50 hover:shadow-xl'
              }`}
            >
              <ChevronRight className="h-6 w-6 text-gray-600" />
            </button>

            {/* Carousel Container */}
            <div className="overflow-hidden mx-14">
              <div
                className="flex transition-transform duration-300 ease-in-out gap-4"
                style={{
                  transform: `translateX(-${
                    currentIndex * (100 / getVisibleCount())
                  }%)`,
                }}
              >
                {activeServices.map((service, index) => (
                  <div
                    key={index}
                    className="flex-shrink-0"
                    style={{
                      width: `calc(${100 / getVisibleCount()}% - ${
                        ((getVisibleCount() - 1) * 16) / getVisibleCount()
                      }px)`,
                    }}
                  >
                    <ServiceThumbnailCard
                      serviceName={service.name}
                      imageUrl={service.image}
                      route={`/service/${service.id}`}
                    />
                  </div>
                ))}
              </div>
            </div>

            {/* Dots Indicator */}
          </div>
        </div>
      </section>

      {/* Random Services Grid Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Explore More Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover our full range of printing and design services to meet
              all your business needs.
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {randomServices.map((service) => (
              <ProductCardHome
                key={service.id}
                productName={service.name}
                imageUrl={service.image}
                price={`$${service.price}`}
                route={`/service/${service.id}`}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Essential Business Services Grid Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Six Pieces That Turn Heads and Win Trust
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Before you make your first sale, your brand speaks through paper,
              packaging, and signage. Arm yourself with these six foundational
              print pieces to turn curious passers‑by into loyal customers and
              give your new venture instant credibility.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-3 gap-8">
            {/* Row 1 */}
            <div className="bg-orange-50 p-6 rounded-3xl hover:shadow-lg transition-shadow duration-300">
              <div className="bg-orange-300 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="h-6 w-6 text-purple-900"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Business Cards
              </h3>
              <p className="text-gray-600">
                Professional first impressions with premium quality business
                cards that represent your brand perfectly.
              </p>
            </div>

            <div
              className="p-6 rounded-3xl hover:shadow-lg transition-shadow duration-300"
              style={{ backgroundColor: '#f1f2db' }}
            >
              <div className="bg-purple-300 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Brochures & Flyers
              </h3>
              <p className="text-gray-600">
                Eye-catching marketing materials that effectively communicate
                your message and attract customers.
              </p>
            </div>

            <div className="bg-orange-50 p-6 rounded-3xl hover:shadow-lg transition-shadow duration-300">
              <div className="bg-orange-500 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v14a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Banners & Signs
              </h3>
              <p className="text-gray-600">
                Large format displays that maximize visibility and drive foot
                traffic to your business location.
              </p>
            </div>

            {/* Row 2 */}
            <div className="bg-gray-50 p-6 rounded-3xl hover:shadow-lg transition-shadow duration-300">
              <div className="bg-purple-500 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Catalogs & Booklets
              </h3>
              <p className="text-gray-600">
                Professional bound materials perfect for product showcases,
                company profiles, and detailed presentations.
              </p>
            </div>

            <div className="bg-orange-50 p-6 rounded-3xl hover:shadow-lg transition-shadow duration-300">
              <div className="bg-orange-500 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Letterheads & Envelopes
              </h3>
              <p className="text-gray-600">
                Professional stationery that reinforces your brand identity in
                every business correspondence.
              </p>
            </div>

            <div className="bg-gray-50 p-6 rounded-3xl hover:shadow-lg transition-shadow duration-300">
              <div className="bg-purple-900 w-12 h-12 rounded-full flex items-center justify-center mb-4">
                <svg
                  className="h-6 w-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                Packaging & Labels
              </h3>
              <p className="text-gray-600">
                Custom packaging solutions and product labels that enhance your
                brand presentation and customer experience.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Marketing Prints for Businesses Section */}
      <section className="py-20 bg-[#f5fbff]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="mb-10">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Top Picks Our Clients Love
            </h2>
          </div>

          {/* Category Filter Buttons */}
          <div className="overflow-x-auto mb-12">
            <div className="flex gap-3 min-w-max px-4 justify-center lg:justify-center">
              {allCategories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveCategory(category)}
                  className={`px-6 py-2 rounded-full text-sm font-medium transition-colors whitespace-nowrap flex-shrink-0 ${
                    activeCategory === category
                      ? 'bg-orange-500 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-orange-100'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
            {displayedServices.map((service, index) => (
              <ProductCardHome
                key={service.id}
                productName={service.name}
                imageUrl={service.image}
                price={`$${service.price}`}
                route={`/service/${service.id}`}
              />
            ))}
          </div>
        </div>
      </section>

      {/* Quality & Service Guarantee Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Image */}
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75"
                alt="Quality Printing Services"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <svg
                      className="h-6 w-6 text-purple-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      Quality Guaranteed
                    </p>
                    <p className="text-sm text-gray-600">
                      100% satisfaction promise
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Content */}
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Choose Our Printing Services?
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                We're committed to delivering exceptional quality and service
                that exceeds your expectations. From premium materials to fast
                turnaround times, we make printing easy and reliable.
              </p>

              <div className="space-y-4 mb-8">
                <div className="flex items-start space-x-3">
                  <div className="bg-orange-500 p-1 rounded-full mt-1">
                    <svg
                      className="h-4 w-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Premium Quality Materials
                    </h3>
                    <p className="text-gray-600">
                      We use only the finest papers and inks to ensure your
                      prints look professional and last longer
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-orange-500 p-1 rounded-full mt-1">
                    <svg
                      className="h-4 w-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Fast Turnaround Times
                    </h3>
                    <p className="text-gray-600">
                      Most orders completed within 24-48 hours, with rush
                      options available for urgent needs
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-orange-500 p-1 rounded-full mt-1">
                    <svg
                      className="h-4 w-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Expert Customer Support
                    </h3>
                    <p className="text-gray-600">
                      Our experienced team is here to help you every step of the
                      way, from design to delivery
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/services"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  View All Services
                </Link>
                <Link
                  to="/about"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  Learn More About Us
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Services */}
      <section className="py-20 bg-[#f5fbff]">
        <div className="text-center mb-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Featured Services
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Discover our most popular printing services designed to make
                your business stand out.
              </p>
            </div>
          </div>
        </div>

        <div className="relative">
          {/* Navigation Arrows */}
          <button
            onClick={handlePreviousFeatured}
            disabled={currentIndexFeatured === 0}
            className={`absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
              currentIndexFeatured === 0
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-orange-50 hover:shadow-xl'
            }`}
          >
            <ChevronLeft className="h-6 w-6 text-gray-600" />
          </button>

          <button
            onClick={handleNextFeatured}
            disabled={
              currentIndexFeatured >=
              activeServices.length - getVisibleCountFeatured()
            }
            className={`absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
              currentIndexFeatured >=
              activeServices.length - getVisibleCountFeatured()
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:bg-orange-50 hover:shadow-xl'
            }`}
          >
            <ChevronRight className="h-6 w-6 text-gray-600" />
          </button>

          {/* Slider Container */}
          <div className="overflow-hidden px-16">
            <div
              className="flex transition-transform duration-300 ease-in-out gap-6"
              style={{
                transform: `translateX(-${
                  currentIndexFeatured * (100 / getVisibleCountFeatured())
                }%)`,
              }}
            >
              {activeServices.map((service) => (
                <div
                  key={service.id}
                  className="flex-shrink-0"
                  style={{
                    width: `calc(${100 / getVisibleCountFeatured()}% - ${
                      ((getVisibleCountFeatured() - 1) * 24) /
                      getVisibleCountFeatured()
                    }px)`,
                  }}
                >
                  <ProductCardHome
                    productName={service.name}
                    imageUrl={service.image}
                    price={`$${service.price}`}
                    route={`/service/${service.id}`}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Design Services Section */}
      <section className="py-20 bg-[#a7bed3]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Professional Design Services
              </h2>
              <p className="text-lg text-gray-600 mb-6">
                Don't have a design? No problem! Our talented design team
                creates stunning, professional artwork that perfectly represents
                your brand and captures your vision.
              </p>

              <div className="space-y-4 mb-8">
                <div className="flex items-start space-x-3">
                  <div className="bg-orange-500 p-1 rounded-full mt-1">
                    <svg
                      className="h-4 w-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Custom Logo Design
                    </h3>
                    <p className="text-gray-600">
                      Create a memorable brand identity that stands out from the
                      competition
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-orange-500 p-1 rounded-full mt-1">
                    <svg
                      className="h-4 w-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Marketing Materials Design
                    </h3>
                    <p className="text-gray-600">
                      Professional layouts for brochures, flyers, and
                      promotional materials
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="bg-orange-500 p-1 rounded-full mt-1">
                    <svg
                      className="h-4 w-4 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      Fast Turnaround
                    </h3>
                    <p className="text-gray-600">
                      Get your custom designs within 24-48 hours with unlimited
                      revisions
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/design-services"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  Explore Design Services
                </Link>
                <Link
                  to="/contact"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  Need a hand?
                </Link>
              </div>
            </div>

            {/* Right Image */}
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75"
                alt="Professional Design Services"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <svg
                      className="h-6 w-6 text-purple-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"
                      />
                    </svg>
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      Expert Designers
                    </p>
                    <p className="text-sm text-gray-600">
                      Professional creative team
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
