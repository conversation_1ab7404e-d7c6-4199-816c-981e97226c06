// Service Repository - Following Repository Pattern and SOLID principles
import { PrismaClient, Service, ServiceCategory, Prisma } from '@prisma/client';
import { BaseRepository } from '@/repositories/base/base.repository';
import { CreateServiceData, UpdateServiceData, ServiceFilters } from '@/types/services';

// Service-specific repository interface (ISP)
export interface IServiceRepository {
  findById(id: string): Promise<Service | null>;
  findByIdWithDetails(id: string): Promise<ServiceWithDetails | null>;
  findAll(options?: ServiceQueryOptions): Promise<Service[]>;
  findByCategory(categoryId: string, options?: ServiceQueryOptions): Promise<Service[]>;
  findByProvider(providerId: string, options?: ServiceQueryOptions): Promise<Service[]>;
  findFeatured(options?: ServiceQueryOptions): Promise<Service[]>;
  search(query: string, options?: ServiceQueryOptions): Promise<Service[]>;
  create(serviceData: CreateServiceData): Promise<Service>;
  update(id: string, serviceData: UpdateServiceData): Promise<Service>;
  delete(id: string): Promise<void>;
  activate(id: string): Promise<Service>;
  deactivate(id: string): Promise<Service>;
  setFeatured(id: string, featured: boolean): Promise<Service>;
  updateRating(id: string, rating: number, reviewCount: number): Promise<Service>;
  getServiceStats(): Promise<ServiceStats>;
  findServicesInPriceRange(minPrice: number, maxPrice: number, options?: ServiceQueryOptions): Promise<Service[]>;
  findNearby(latitude: number, longitude: number, radiusKm: number, options?: ServiceQueryOptions): Promise<Service[]>;
}

// Types for service operations
export type ServiceWithDetails = Service & {
  category: ServiceCategory;
  provider: {
    id: string;
    businessName: string;
    rating?: number;
    isVerified: boolean;
  };
  user: {
    id: string;
    name: string;
    email: string;
  };
  _count: {
    orders: number;
    reviews: number;
  };
};

export interface ServiceQueryOptions {
  skip?: number;
  take?: number;
  orderBy?: Prisma.ServiceOrderByWithRelationInput;
  include?: Prisma.ServiceInclude;
  where?: Prisma.ServiceWhereInput;
}

export interface ServiceStats {
  total: number;
  active: number;
  featured: number;
  byCategory: Record<string, number>;
  byPriceType: Record<string, number>;
  averagePrice: number;
  totalOrders: number;
  averageRating: number;
}

// Service repository implementation (SRP)
export class ServiceRepository extends BaseRepository<Service> implements IServiceRepository {
  constructor(prisma: PrismaClient) {
    super(prisma, 'service');
  }

  async findByIdWithDetails(id: string): Promise<ServiceWithDetails | null> {
    try {
      return await this.prisma.service.findUnique({
        where: { id },
        include: {
          category: true,
          provider: {
            select: {
              id: true,
              businessName: true,
              rating: true,
              isVerified: true,
            },
          },
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          _count: {
            select: {
              orders: true,
              reviews: true,
            },
          },
        },
      }) as ServiceWithDetails | null;
    } catch (error) {
      throw new Error(`Failed to find service with details: ${error}`);
    }
  }

  async findByCategory(categoryId: string, options: ServiceQueryOptions = {}): Promise<Service[]> {
    try {
      const { skip, take, orderBy = { createdAt: 'desc' }, where = {} } = options;
      
      return await this.prisma.service.findMany({
        where: {
          categoryId,
          isActive: true,
          ...where,
        },
        skip,
        take,
        orderBy,
        include: options.include,
      });
    } catch (error) {
      throw new Error(`Failed to find services by category: ${error}`);
    }
  }

  async findByProvider(providerId: string, options: ServiceQueryOptions = {}): Promise<Service[]> {
    try {
      const { skip, take, orderBy = { createdAt: 'desc' }, where = {} } = options;
      
      return await this.prisma.service.findMany({
        where: {
          providerId,
          ...where,
        },
        skip,
        take,
        orderBy,
        include: options.include,
      });
    } catch (error) {
      throw new Error(`Failed to find services by provider: ${error}`);
    }
  }

  async findFeatured(options: ServiceQueryOptions = {}): Promise<Service[]> {
    try {
      const { skip, take, orderBy = { createdAt: 'desc' }, where = {} } = options;
      
      return await this.prisma.service.findMany({
        where: {
          isFeatured: true,
          isActive: true,
          ...where,
        },
        skip,
        take,
        orderBy,
        include: options.include,
      });
    } catch (error) {
      throw new Error(`Failed to find featured services: ${error}`);
    }
  }

  async search(query: string, options: ServiceQueryOptions = {}): Promise<Service[]> {
    try {
      const { skip, take, orderBy = { _relevance: { fields: ['name', 'description'], search: query, sort: 'desc' } }, where = {} } = options;
      
      return await this.prisma.service.findMany({
        where: {
          isActive: true,
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { description: { contains: query, mode: 'insensitive' } },
            { tags: { has: query } },
            { category: { name: { contains: query, mode: 'insensitive' } } },
          ],
          ...where,
        },
        skip,
        take,
        orderBy,
        include: {
          category: true,
          provider: {
            select: {
              businessName: true,
              rating: true,
              isVerified: true,
            },
          },
          ...options.include,
        },
      });
    } catch (error) {
      throw new Error(`Failed to search services: ${error}`);
    }
  }

  async create(serviceData: CreateServiceData): Promise<Service> {
    try {
      return await this.prisma.service.create({
        data: serviceData,
      });
    } catch (error) {
      throw new Error(`Failed to create service: ${error}`);
    }
  }

  async update(id: string, serviceData: UpdateServiceData): Promise<Service> {
    try {
      return await this.prisma.service.update({
        where: { id },
        data: serviceData,
      });
    } catch (error) {
      throw new Error(`Failed to update service: ${error}`);
    }
  }

  async activate(id: string): Promise<Service> {
    try {
      return await this.prisma.service.update({
        where: { id },
        data: { isActive: true },
      });
    } catch (error) {
      throw new Error(`Failed to activate service: ${error}`);
    }
  }

  async deactivate(id: string): Promise<Service> {
    try {
      return await this.prisma.service.update({
        where: { id },
        data: { isActive: false },
      });
    } catch (error) {
      throw new Error(`Failed to deactivate service: ${error}`);
    }
  }

  async setFeatured(id: string, featured: boolean): Promise<Service> {
    try {
      return await this.prisma.service.update({
        where: { id },
        data: { isFeatured: featured },
      });
    } catch (error) {
      throw new Error(`Failed to set service featured status: ${error}`);
    }
  }

  async updateRating(id: string, rating: number, reviewCount: number): Promise<Service> {
    try {
      return await this.prisma.service.update({
        where: { id },
        data: {
          rating,
          totalReviews: reviewCount,
        },
      });
    } catch (error) {
      throw new Error(`Failed to update service rating: ${error}`);
    }
  }

  async getServiceStats(): Promise<ServiceStats> {
    try {
      const [
        total,
        active,
        featured,
        byCategory,
        byPriceType,
        priceStats,
        orderStats,
        ratingStats,
      ] = await Promise.all([
        this.prisma.service.count(),
        this.prisma.service.count({ where: { isActive: true } }),
        this.prisma.service.count({ where: { isFeatured: true } }),
        this.prisma.service.groupBy({
          by: ['categoryId'],
          _count: { _all: true },
        }),
        this.prisma.service.groupBy({
          by: ['priceType'],
          _count: { _all: true },
        }),
        this.prisma.service.aggregate({
          _avg: { price: true },
        }),
        this.prisma.order.count(),
        this.prisma.service.aggregate({
          _avg: { rating: true },
        }),
      ]);

      const byCategoryObj = byCategory.reduce((acc, item) => {
        acc[item.categoryId] = item._count._all;
        return acc;
      }, {} as Record<string, number>);

      const byPriceTypeObj = byPriceType.reduce((acc, item) => {
        acc[item.priceType] = item._count._all;
        return acc;
      }, {} as Record<string, number>);

      return {
        total,
        active,
        featured,
        byCategory: byCategoryObj,
        byPriceType: byPriceTypeObj,
        averagePrice: priceStats._avg.price || 0,
        totalOrders: orderStats,
        averageRating: ratingStats._avg.rating || 0,
      };
    } catch (error) {
      throw new Error(`Failed to get service stats: ${error}`);
    }
  }

  async findServicesInPriceRange(
    minPrice: number,
    maxPrice: number,
    options: ServiceQueryOptions = {}
  ): Promise<Service[]> {
    try {
      const { skip, take, orderBy = { price: 'asc' }, where = {} } = options;
      
      return await this.prisma.service.findMany({
        where: {
          isActive: true,
          price: {
            gte: minPrice,
            lte: maxPrice,
          },
          ...where,
        },
        skip,
        take,
        orderBy,
        include: options.include,
      });
    } catch (error) {
      throw new Error(`Failed to find services in price range: ${error}`);
    }
  }

  async findNearby(
    latitude: number,
    longitude: number,
    radiusKm: number,
    options: ServiceQueryOptions = {}
  ): Promise<Service[]> {
    try {
      // This would require implementing geospatial queries
      // For now, return services with location containing nearby cities
      const { skip, take, orderBy = { createdAt: 'desc' }, where = {} } = options;
      
      return await this.prisma.service.findMany({
        where: {
          isActive: true,
          location: {
            not: null,
          },
          ...where,
        },
        skip,
        take,
        orderBy,
        include: options.include,
      });
    } catch (error) {
      throw new Error(`Failed to find nearby services: ${error}`);
    }
  }
}