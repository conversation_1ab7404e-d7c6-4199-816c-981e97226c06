import {Request, Response, NextFunction} from 'express';
import {IAuthService} from '@/services/interfaces/auth.service';
import {UserRole} from '@/types/common.types';
import {UnauthorizedError, ForbiddenError} from '@/types/common.types';

export class AuthMiddleware {
	constructor(private authService: IAuthService) {}

	authenticate = async (
		req: Request,
		res: Response,
		next: NextFunction
	): Promise<void> => {
		try {
			const authHeader = req.headers.authorization;

			if (!authHeader || !authHeader.startsWith('Bearer ')) {
				throw new UnauthorizedError('Access token is required');
			}

			const token = authHeader.substring(7); // Remove 'Bearer ' prefix

			// Verify access token
			const payload = await this.authService.verifyAccessToken(token);

			// Attach user to request
			(req as any).user = payload;

			next();
		} catch (error) {
			if (error instanceof UnauthorizedError) {
				res.status(401).json({
					success: false,
					message: error.message,
				});
			} else {
				res.status(401).json({
					success: false,
					message: 'Invalid access token',
				});
			}
		}
	};

	requireRole = (roles: UserRole | UserRole[]) => {
		return (req: Request, res: Response, next: NextFunction): void => {
			try {
				const user = (req as any).user;

				if (!user) {
					throw new UnauthorizedError('User not authenticated');
				}

				const allowedRoles = Array.isArray(roles) ? roles : [roles];

				if (!allowedRoles.includes(user.role)) {
					throw new ForbiddenError('Insufficient permissions');
				}

				next();
			} catch (error) {
				if (
					error instanceof UnauthorizedError ||
					error instanceof ForbiddenError
				) {
					res.status(error.statusCode).json({
						success: false,
						message: error.message,
					});
				} else {
					res.status(500).json({
						success: false,
						message: 'Internal server error',
					});
				}
			}
		};
	};

	requireCustomer = this.requireRole(UserRole.CUSTOMER);
	requireProvider = this.requireRole(UserRole.PROVIDER);
	requireAdmin = this.requireRole(UserRole.ADMIN);
	requireProviderOrAdmin = this.requireRole([
		UserRole.PROVIDER,
		UserRole.ADMIN,
	]);
	requireAnyAuthenticated = this.requireRole([
		UserRole.CUSTOMER,
		UserRole.PROVIDER,
		UserRole.ADMIN,
	]);

	// Optional authentication - doesn't fail if no token provided
	optionalAuth = async (
		req: Request,
		res: Response,
		next: NextFunction
	): Promise<void> => {
		try {
			const authHeader = req.headers.authorization;

			if (authHeader && authHeader.startsWith('Bearer ')) {
				const token = authHeader.substring(7);
				const payload = await this.authService.verifyAccessToken(token);
				(req as any).user = payload;
			}

			next();
		} catch (error) {
			// Continue without authentication
			next();
		}
	};
}
