// Provider Types - Following established patterns and SOLID principles
import {UserRole} from '@prisma/client';

// Base Provider Interface (LSP - Liskov Substitution Principle)
export interface BaseProvider {
	id: string;
	userId: string;
	businessName: string;
	description?: string;
	website?: string;
	businessPhone?: string;
	businessAddress?: string;
	businessCity?: string;
	businessState?: string;
	businessZip?: string;
	licenseNumber?: string;
	insuranceNumber?: string;
	isVerified: boolean;
	verifiedAt?: Date;
	rating?: number;
	totalReviews: number;
	totalOrders: number;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

// Provider Profile Interface (ISP - Interface Segregation Principle)
export interface ProviderProfile extends BaseProvider {
	// Additional provider-specific fields
	specialties: string[];
	certifications: string[];
	yearsOfExperience: number;
	serviceAreas: string[];
	availability: ProviderAvailability;
	paymentMethods: string[];
	responseTime: number; // in hours
}

// Provider Availability Interface (SRP - Single Responsibility)
export interface ProviderAvailability {
	monday: TimeSlot[];
	tuesday: TimeSlot[];
	wednesday: TimeSlot[];
	thursday: TimeSlot[];
	friday: TimeSlot[];
	saturday: TimeSlot[];
	sunday: TimeSlot[];
	timezone: string;
}

// Time Slot Interface (SRP)
export interface TimeSlot {
	startTime: string; // HH:MM format
	endTime: string; // HH:MM format
	isAvailable: boolean;
}

// Data Transfer Objects (DTOs) for API operations
export interface CreateProviderRequest {
	businessName: string;
	description?: string;
	website?: string;
	businessPhone?: string;
	businessAddress?: string;
	businessCity?: string;
	businessState?: string;
	businessZip?: string;
	licenseNumber?: string;
	insuranceNumber?: string;
	specialties?: string[];
	certifications?: string[];
	yearsOfExperience?: number;
	serviceAreas?: string[];
	availability?: ProviderAvailability;
	paymentMethods?: string[];
	responseTime?: number;
}

export interface UpdateProviderRequest {
	businessName?: string;
	description?: string;
	website?: string;
	businessPhone?: string;
	businessAddress?: string;
	businessCity?: string;
	businessState?: string;
	businessZip?: string;
	licenseNumber?: string;
	insuranceNumber?: string;
	specialties?: string[];
	certifications?: string[];
	yearsOfExperience?: number;
	serviceAreas?: string[];
	availability?: ProviderAvailability;
	paymentMethods?: string[];
	responseTime?: number;
	isActive?: boolean;
}

// Provider Verification Request (SRP)
export interface ProviderVerificationRequest {
	licenseNumber: string;
	insuranceNumber?: string;
	businessDocuments: string[]; // File URLs
	identityDocuments: string[]; // File URLs
	additionalNotes?: string;
}

// Provider Search and Filter Types (OCP - Open/Closed Principle)
export interface ProviderFilters {
	specialties?: string[];
	serviceAreas?: string[];
	minRating?: number;
	maxRating?: number;
	isVerified?: boolean;
	isActive?: boolean;
	minExperience?: number;
	maxExperience?: number;
	availability?: {
		dayOfWeek?: number;
		timeSlot?: TimeSlot;
	};
	paymentMethods?: string[];
	maxResponseTime?: number;
	sortBy?: ProviderSortField;
	sortOrder?: 'asc' | 'desc';
}

export interface ProviderSearchRequest {
	query?: string;
	filters?: ProviderFilters;
	pagination?: {
		page: number;
		limit: number;
	};
}

// Provider Sort Options (OCP)
export type ProviderSortField =
	| 'businessName'
	| 'rating'
	| 'totalOrders'
	| 'responseTime'
	| 'yearsOfExperience'
	| 'createdAt';

// Business Logic Types (SRP)
export interface ProviderBusinessRules {
	maxSpecialties: number;
	maxCertifications: number;
	maxServiceAreas: number;
	minBusinessNameLength: number;
	maxBusinessNameLength: number;
	minDescriptionLength: number;
	maxDescriptionLength: number;
	requiredFields: string[];
	verificationRequired: boolean;
	maxResponseTime: number;
	minResponseTime: number;
}

// Validation Error Types (SRP)
export interface ProviderValidationError {
	field: string;
	message: string;
	code: string;
}

// Provider Metrics Interface (SRP)
export interface ProviderMetrics {
	totalServices: number;
	activeServices: number;
	totalOrders: number;
	completedOrders: number;
	cancelledOrders: number;
	averageRating: number;
	totalReviews: number;
	responseRate: number;
	averageResponseTime: number;
	completionRate: number;
	revenue: {
		total: number;
		thisMonth: number;
		lastMonth: number;
	};
	topServices: Array<{
		serviceId: string;
		serviceName: string;
		orderCount: number;
		revenue: number;
	}>;
}

// Provider Statistics Interface (SRP)
export interface ProviderStatistics {
	totalProviders: number;
	verifiedProviders: number;
	activeProviders: number;
	averageRating: number;
	topSpecialties: Array<{
		specialty: string;
		providerCount: number;
	}>;
	topServiceAreas: Array<{
		area: string;
		providerCount: number;
	}>;
	verificationRate: number;
	averageResponseTime: number;
}

// Event Types for business logic (SRP)
export interface ProviderCreatedEvent {
	providerId: string;
	userId: string;
	businessName: string;
	timestamp: Date;
}

export interface ProviderUpdatedEvent {
	providerId: string;
	userId: string;
	changes: Partial<UpdateProviderRequest>;
	timestamp: Date;
}

export interface ProviderVerifiedEvent {
	providerId: string;
	verifiedBy: string;
	verificationNotes?: string;
	timestamp: Date;
}

export interface ProviderDeactivatedEvent {
	providerId: string;
	deactivatedBy: string;
	reason?: string;
	timestamp: Date;
}

// Utility Types (DRY - Don't Repeat Yourself)
export type ProviderStatus =
	| 'active'
	| 'inactive'
	| 'pending'
	| 'suspended'
	| 'verified'
	| 'unverified';

export interface ProviderStatusUpdate {
	status: ProviderStatus;
	reason?: string;
	updatedBy: string;
	timestamp: Date;
}

// Extended Types for specific use cases (OCP)
export interface ProviderWithMetrics extends ProviderProfile {
	metrics: ProviderMetrics;
	recentOrders: Array<{
		orderId: string;
		serviceName: string;
		status: string;
		amount: number;
		createdAt: Date;
	}>;
	recentReviews: Array<{
		reviewId: string;
		rating: number;
		comment?: string;
		createdAt: Date;
	}>;
}

export interface ProviderSearchResult {
	providers: ProviderWithMetrics[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
	hasNext: boolean;
	hasPrev: boolean;
}
