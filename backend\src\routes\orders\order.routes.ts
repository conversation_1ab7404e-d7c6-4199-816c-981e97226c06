// Order Routes - Following REST principles and proper middleware organization
import {Router} from 'express';
import {OrderController} from '@/controllers/orders';
import {OrderManagementService} from '@/services/orders';
import {OrderRepository} from '@/repositories/orders';
import {Valida<PERSON><PERSON>hains} from '@/middleware/validation';
import {handleValidation} from '@/middleware/validation/validator';
import {
	authenticate,
	adminOnly,
	providerOrAdmin,
	optionalAuth,
} from '@/middleware/auth';
import {validatePagination} from '@/middleware/validation';
import {prisma} from '@/config/database';

const router = Router();

// Initialize dependencies (DIP - Dependency Inversion Principle)
const orderRepository = new OrderRepository(prisma);
const orderService = new OrderManagementService(
	orderRepository,
	// These would be injected from a DI container in a real implementation
	{} as any, // IFileUploadService
	{} as any // IEmailService
);
const orderController = new OrderController(orderService);

// GET /api/v1/orders - Search orders (Admin/Provider only)
router.get(
	'/',
	authenticate,
	providerOrAdmin,
	validatePagination,
	orderController.searchOrders.bind(orderController)
);

// GET /api/v1/orders/recent - Get recent orders (Admin only)
router.get(
	'/recent',
	authenticate,
	adminOnly,
	orderController.getRecentOrders.bind(orderController)
);

// GET /api/v1/orders/overdue - Get overdue orders (Admin/Provider only)
router.get(
	'/overdue',
	authenticate,
	providerOrAdmin,
	orderController.getOverdueOrders.bind(orderController)
);

// GET /api/v1/orders/stats - Get order statistics (Admin only)
router.get(
	'/stats',
	authenticate,
	adminOnly,
	orderController.getOrderStatistics.bind(orderController)
);

// GET /api/v1/orders/metrics - Get order metrics (Admin/Provider only)
router.get(
	'/metrics',
	authenticate,
	providerOrAdmin,
	orderController.getOrderMetrics.bind(orderController)
);

// GET /api/v1/orders/me - Get current user's orders (Authenticated users)
router.get(
	'/me',
	authenticate,
	orderController.getMyOrders.bind(orderController)
);

// GET /api/v1/orders/number/:orderNumber - Get order by order number (Public)
router.get(
	'/number/:orderNumber',
	optionalAuth,
	orderController.getOrderByOrderNumber.bind(orderController)
);

// GET /api/v1/orders/:id - Get order by ID (Owner/Provider/Admin only)
router.get(
	'/:id',
	authenticate,
	orderController.getOrderById.bind(orderController)
);

// GET /api/v1/orders/user/:userId - Get orders by user ID (Admin/Provider only)
router.get(
	'/user/:userId',
	authenticate,
	providerOrAdmin,
	orderController.getOrdersByUserId.bind(orderController)
);

// GET /api/v1/orders/provider/:providerId - Get orders by provider ID (Admin/Provider only)
router.get(
	'/provider/:providerId',
	authenticate,
	providerOrAdmin,
	orderController.getOrdersByProviderId.bind(orderController)
);

// POST /api/v1/orders - Create new order (Authenticated users)
router.post(
	'/',
	authenticate,
	ValidationChains.createOrder(),
	handleValidation,
	orderController.createOrder.bind(orderController)
);

// PUT /api/v1/orders/:id - Update order (Owner/Provider/Admin only)
router.put(
	'/:id',
	authenticate,
	ValidationChains.updateOrder(),
	handleValidation,
	orderController.updateOrder.bind(orderController)
);

// DELETE /api/v1/orders/:id - Delete order (Owner/Admin only)
router.delete(
	'/:id',
	authenticate,
	orderController.deleteOrder.bind(orderController)
);

// PUT /api/v1/orders/:id/status - Update order status (Provider/Admin only)
router.put(
	'/:id/status',
	authenticate,
	providerOrAdmin,
	ValidationChains.updateOrderStatus(),
	handleValidation,
	orderController.updateOrderStatus.bind(orderController)
);

// PUT /api/v1/orders/:id/payment - Update payment status (Admin only)
router.put(
	'/:id/payment',
	authenticate,
	adminOnly,
	ValidationChains.updatePaymentStatus(),
	handleValidation,
	orderController.updatePaymentStatus.bind(orderController)
);

// POST /api/v1/orders/:id/action - Process order action (Provider/Admin/User)
router.post(
	'/:id/action',
	authenticate,
	ValidationChains.processOrderAction(),
	handleValidation,
	orderController.processOrderAction.bind(orderController)
);

export default router;
