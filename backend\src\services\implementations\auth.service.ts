import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import {v4 as uuidv4} from 'uuid';
import {IAuthService} from '../interfaces/auth.service';
import {IAuthRepository} from '@/repositories/interfaces/auth.repository';
import {IUserRepository} from '@/repositories/interfaces/user.repository';
import {
	LoginRequest,
	RegisterRequest,
	GoogleAuthRequest,
	RefreshTokenRequest,
	ChangePasswordRequest,
	LoginResponse,
	AuthTokens,
	AuthUser,
	JwtPayload,
} from '@/types/auth.types';
import {UserRole} from '@/types/common.types';
import {
	ValidationError,
	UnauthorizedError,
	ConflictError,
	NotFoundError,
} from '@/types/common.types';

export class AuthService implements IAuthService {
	constructor(
		private authRepository: IAuthRepository,
		private userRepository: IUserRepository,
		private jwtSecret: string,
		private jwtRefreshSecret: string,
		private jwtExpiresIn: string = '15m',
		private jwtRefreshExpiresIn: string = '7d'
	) {}

	async registerUser(userData: RegisterRequest): Promise<AuthUser> {
		// Check if user already exists
		const existingUser = await this.userRepository.findUserByEmail(
			userData.email
		);
		if (existingUser) {
			throw new ConflictError('User with this email already exists');
		}

		// Validate password strength
		await this.validatePasswordStrength(userData.password);

		// Hash password
		const hashedPassword = await this.hashPassword(userData.password);

		// Create user
		const user = await this.userRepository.createUser({
			...userData,
			password: hashedPassword,
		});

		return this.mapToAuthUser(user);
	}

	async loginUser(
		credentials: LoginRequest,
		ipAddress?: string,
		userAgent?: string
	): Promise<LoginResponse> {
		// Check rate limiting
		const rateLimitKey = `login:${credentials.email}`;
		if (!(await this.checkRateLimit(rateLimitKey, 'login'))) {
			throw new UnauthorizedError(
				'Too many login attempts. Please try again later.'
			);
		}

		// Find user with password
		const user = await this.authRepository.findUserWithPassword(
			credentials.email
		);
		if (!user) {
			throw new UnauthorizedError('Invalid email or password');
		}

		// Verify password
		const isValidPassword = await this.comparePasswords(
			credentials.password,
			user.password
		);
		if (!isValidPassword) {
			// Record failed login attempt
			await this.authRepository.recordLoginAttempt(
				user.id,
				ipAddress,
				userAgent
			);
			throw new UnauthorizedError('Invalid email or password');
		}

		// Check if user is active
		if (!user.isActive) {
			throw new UnauthorizedError('Account is deactivated');
		}

		// Clear failed login attempts
		await this.authRepository.clearFailedLoginAttempts(user.id);

		// Generate tokens
		const tokens = await this.generateTokens(user.id, user.email, user.role);

		// Create session
		await this.createSession(
			user.id,
			tokens.refreshToken,
			ipAddress,
			userAgent
		);

		return {
			user: this.mapToAuthUser(user),
			tokens,
		};
	}

	async loginWithGoogle(
		token: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<LoginResponse> {
		// TODO: Implement Google OAuth verification
		// For now, throw an error indicating this needs to be implemented
		throw new Error('Google OAuth login not yet implemented');
	}

	async logoutUser(refreshToken: string): Promise<void> {
		// Invalidate refresh token
		await this.invalidateRefreshToken(refreshToken);
	}

	async refreshToken(refreshToken: string): Promise<AuthTokens> {
		// Verify refresh token
		const payload = await this.verifyRefreshToken(refreshToken);

		// Check if session is valid
		const isValidSession = await this.validateSession(refreshToken);
		if (!isValidSession) {
			throw new UnauthorizedError('Invalid refresh token');
		}

		// Get user
		const user = await this.userRepository.findUserById(payload.userId);
		if (!user || !user.isActive) {
			throw new UnauthorizedError('User not found or inactive');
		}

		// Generate new tokens
		const tokens = await this.generateTokens(user.id, user.email, user.role);

		// Update session with new refresh token
		await this.authRepository.updateSession(
			(await this.authRepository.findSessionByRefreshToken(refreshToken))!.id,
			{
				refreshToken: tokens.refreshToken,
				expiresAt: new Date(Date.now() + this.getRefreshTokenExpiryMs()),
			}
		);

		return tokens;
	}

	async changePassword(
		userId: string,
		passwordData: ChangePasswordRequest
	): Promise<void> {
		// Get user with password
		const user = await this.authRepository.findUserWithPassword(userId);
		if (!user) {
			throw new NotFoundError('User not found');
		}

		// Verify current password
		const isValidPassword = await this.comparePasswords(
			passwordData.currentPassword,
			user.password
		);
		if (!isValidPassword) {
			throw new UnauthorizedError('Current password is incorrect');
		}

		// Validate new password strength
		await this.validatePasswordStrength(passwordData.newPassword);

		// Hash new password
		const hashedPassword = await this.hashPassword(passwordData.newPassword);

		// Update password
		await this.authRepository.updateUserPassword(userId, hashedPassword);

		// Revoke all sessions to force re-login
		await this.revokeAllUserSessions(userId);
	}

	async generateTokens(
		userId: string,
		email: string,
		role: string
	): Promise<AuthTokens> {
		const accessToken = jwt.sign({userId, email, role}, this.jwtSecret, {
			expiresIn: this.jwtExpiresIn,
		});

		const refreshToken = jwt.sign(
			{userId, email, role, type: 'refresh'},
			this.jwtRefreshSecret,
			{expiresIn: this.jwtRefreshExpiresIn}
		);

		return {
			accessToken,
			refreshToken,
			expiresIn: this.getAccessTokenExpiryMs(),
		};
	}

	async verifyAccessToken(token: string): Promise<JwtPayload> {
		try {
			const payload = jwt.verify(token, this.jwtSecret) as JwtPayload;
			return payload;
		} catch (error) {
			throw new UnauthorizedError('Invalid access token');
		}
	}

	async verifyRefreshToken(token: string): Promise<JwtPayload> {
		try {
			const payload = jwt.verify(token, this.jwtRefreshSecret) as JwtPayload;
			if (payload.type !== 'refresh') {
				throw new UnauthorizedError('Invalid token type');
			}
			return payload;
		} catch (error) {
			throw new UnauthorizedError('Invalid refresh token');
		}
	}

	async invalidateRefreshToken(token: string): Promise<void> {
		await this.authRepository.invalidateRefreshToken(token);
	}

	async createSession(
		userId: string,
		refreshToken: string,
		ipAddress?: string,
		userAgent?: string
	): Promise<void> {
		await this.authRepository.createSession({
			userId,
			refreshToken,
			expiresAt: new Date(Date.now() + this.getRefreshTokenExpiryMs()),
			ipAddress,
			userAgent,
			isActive: true,
		});
	}

	async validateSession(refreshToken: string): Promise<boolean> {
		return await this.authRepository.isRefreshTokenValid(refreshToken);
	}

	async revokeAllUserSessions(userId: string): Promise<void> {
		await this.authRepository.deleteAllUserSessions(userId);
	}

	async hashPassword(password: string): Promise<string> {
		const saltRounds = 12;
		return await bcrypt.hash(password, saltRounds);
	}

	async comparePasswords(
		password: string,
		hashedPassword: string
	): Promise<boolean> {
		return await bcrypt.compare(password, hashedPassword);
	}

	async validatePasswordStrength(password: string): Promise<boolean> {
		// Minimum 8 characters, at least one uppercase letter, one lowercase letter, one number
		const passwordRegex =
			/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/;

		if (!passwordRegex.test(password)) {
			throw new ValidationError(
				'Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, and one number'
			);
		}

		return true;
	}

	async checkRateLimit(identifier: string, action: string): Promise<boolean> {
		// TODO: Implement rate limiting with Redis
		// For now, return true (no rate limiting)
		return true;
	}

	// Helper methods
	private mapToAuthUser(user: any): AuthUser {
		return {
			id: user.id,
			email: user.email,
			name: user.name,
			role: user.role,
			isActive: user.isActive,
			isVerified: user.isVerified,
		};
	}

	private getAccessTokenExpiryMs(): number {
		const match = this.jwtExpiresIn.match(/^(\d+)([smhd])$/);
		if (!match) return 15 * 60 * 1000; // Default 15 minutes

		const [, value, unit] = match;
		const numValue = parseInt(value);

		switch (unit) {
			case 's':
				return numValue * 1000;
			case 'm':
				return numValue * 60 * 1000;
			case 'h':
				return numValue * 60 * 60 * 1000;
			case 'd':
				return numValue * 24 * 60 * 60 * 1000;
			default:
				return 15 * 60 * 1000;
		}
	}

	private getRefreshTokenExpiryMs(): number {
		const match = this.jwtRefreshExpiresIn.match(/^(\d+)([smhd])$/);
		if (!match) return 7 * 24 * 60 * 60 * 1000; // Default 7 days

		const [, value, unit] = match;
		const numValue = parseInt(value);

		switch (unit) {
			case 's':
				return numValue * 1000;
			case 'm':
				return numValue * 60 * 1000;
			case 'h':
				return numValue * 60 * 60 * 1000;
			case 'd':
				return numValue * 24 * 60 * 60 * 1000;
			default:
				return 7 * 24 * 60 * 60 * 1000;
		}
	}
}
