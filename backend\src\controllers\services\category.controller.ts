// Category Controller - Following SRP and REST principles
import { Request, Response } from 'express';
import { CategoryManagementService } from '@/services/services/category-management.service';
import { CategoryRepository } from '@/repositories/services';
import { ResponseFormatter } from '@/utils/formatting';
import { HTTP_STATUS } from '@/constants';
import { prisma } from '@/config/database';
import { CreateCategoryData, UpdateCategoryData } from '@/types/services';

// Category Controller (SRP - handles HTTP requests for categories)
export class CategoryController {
  private categoryManagementService: CategoryManagementService;

  constructor() {
    // Dependency injection setup
    const categoryRepository = new CategoryRepository(prisma);
    this.categoryManagementService = new CategoryManagementService(categoryRepository);
  }

  // GET /api/v1/categories - Get all categories
  async getCategories(req: Request, res: Response): Promise<void> {
    try {
      const includeInactive = req.user?.role === 'ADMIN' && req.query.includeInactive === 'true';

      const result = await this.categoryManagementService.getAllCategories(includeInactive);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Categories retrieved successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Failed to get categories',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get categories',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // GET /api/v1/categories/:id - Get category by ID
  async getCategoryById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;

      const result = await this.categoryManagementService.getCategoryById(id);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Category retrieved successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Category not found' ? HTTP_STATUS.NOT_FOUND : HTTP_STATUS.BAD_REQUEST;
        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to get category',
            result.error || 'Unknown error',
            statusCode,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get category',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // POST /api/v1/categories - Create new category (Admin only)
  async createCategory(req: Request, res: Response): Promise<void> {
    try {
      const userRole = req.user?.role;

      if (!userRole) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const categoryData: CreateCategoryData = req.body;

      const result = await this.categoryManagementService.createCategory(categoryData, userRole);

      if (result.success) {
        res.status(HTTP_STATUS.CREATED).json(
          ResponseFormatter.success(
            'Category created successfully',
            result.data,
            HTTP_STATUS.CREATED
          )
        );
      } else {
        const statusCode = result.error === 'Only administrators can create categories' ? 
                          HTTP_STATUS.FORBIDDEN : HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to create category',
            result.error || 'Unknown error',
            statusCode,
            req.path,
            result.validationErrors
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to create category',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // PUT /api/v1/categories/:id - Update category (Admin only)
  async updateCategory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role;

      if (!userRole) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const updateData: UpdateCategoryData = req.body;

      const result = await this.categoryManagementService.updateCategory(id, updateData, userRole);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Category updated successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Category not found' ? HTTP_STATUS.NOT_FOUND :
                          result.error === 'Only administrators can update categories' ? HTTP_STATUS.FORBIDDEN :
                          HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to update category',
            result.error || 'Unknown error',
            statusCode,
            req.path,
            result.validationErrors
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to update category',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // DELETE /api/v1/categories/:id - Delete category (Admin only)
  async deleteCategory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role;

      if (!userRole) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const result = await this.categoryManagementService.deleteCategory(id, userRole);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Category deleted successfully',
            null,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Category not found' ? HTTP_STATUS.NOT_FOUND :
                          result.error === 'Only administrators can delete categories' ? HTTP_STATUS.FORBIDDEN :
                          result.error === 'Cannot delete category with active services' ? HTTP_STATUS.CONFLICT :
                          HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to delete category',
            result.error || 'Unknown error',
            statusCode,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to delete category',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // PUT /api/v1/categories/:id/activate - Activate category (Admin only)
  async activateCategory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role;

      if (!userRole) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const result = await this.categoryManagementService.activateCategory(id, userRole);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Category activated successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Category not found' ? HTTP_STATUS.NOT_FOUND :
                          result.error === 'Only administrators can activate categories' ? HTTP_STATUS.FORBIDDEN :
                          HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to activate category',
            result.error || 'Unknown error',
            statusCode,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to activate category',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // PUT /api/v1/categories/:id/deactivate - Deactivate category (Admin only)
  async deactivateCategory(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userRole = req.user?.role;

      if (!userRole) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const result = await this.categoryManagementService.deactivateCategory(id, userRole);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Category deactivated successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Category not found' ? HTTP_STATUS.NOT_FOUND :
                          result.error === 'Only administrators can deactivate categories' ? HTTP_STATUS.FORBIDDEN :
                          HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to deactivate category',
            result.error || 'Unknown error',
            statusCode,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to deactivate category',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // PUT /api/v1/categories/reorder - Reorder categories (Admin only)
  async reorderCategories(req: Request, res: Response): Promise<void> {
    try {
      const userRole = req.user?.role;
      const { categoryOrders } = req.body;

      if (!userRole) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      if (!Array.isArray(categoryOrders)) {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Invalid request',
            'categoryOrders must be an array',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
        return;
      }

      const result = await this.categoryManagementService.reorderCategories(categoryOrders, userRole);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Categories reordered successfully',
            null,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Only administrators can reorder categories' ? HTTP_STATUS.FORBIDDEN :
                          HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to reorder categories',
            result.error || 'Unknown error',
            statusCode,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to reorder categories',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // GET /api/v1/categories/popular - Get most popular categories
  async getMostPopularCategories(req: Request, res: Response): Promise<void> {
    try {
      const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

      const result = await this.categoryManagementService.getMostPopularCategories(limit);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Popular categories retrieved successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Failed to get popular categories',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get popular categories',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }
}