// Authentication Service - Following SRP and DIP principles
import { User, Session } from '@prisma/client';
import { 
  AuthResponse, 
  LoginRequest, 
  RegisterRequest,
  TokenPayload,
  CreateUserData,
  UpdateUserData,
  CreateSessionData 
} from '@/types/auth';
import { BusinessLogicResult } from '@/types/common';
import { PasswordService, TokenService, RandomTokenService } from '@/utils/crypto';
import { IUserRepository, ISessionRepository } from '@/repositories';

// Email service interface (DIP - Dependency Inversion Principle)
export interface IEmailService {
  sendVerificationEmail(email: string, token: string): Promise<void>;
  sendPasswordResetEmail(email: string, token: string): Promise<void>;
  sendWelcomeEmail(email: string, name: string): Promise<void>;
}

// Main Authentication Service (SRP - Single Responsibility)
export class AuthService {
  constructor(
    private userRepository: IUserRepository,
    private sessionRepository: ISessionRepository,
    private emailService: IEmailService
  ) {}

  // User registration (SRP)
  async register(request: RegisterRequest): Promise<BusinessLogicResult<AuthResponse>> {
    try {
      // Check if user already exists
      const existingUser = await this.userRepository.findByEmail(request.email);
      if (existingUser) {
        return {
          success: false,
          error: 'Email already registered',
        };
      }

      // Hash password
      const hashedPassword = await PasswordService.hash(request.password);

      // Generate email verification token
      const verificationToken = RandomTokenService.generateSecureToken();

      // Create user data
      const userData: CreateUserData = {
        email: request.email,
        name: request.name,
        role: request.role || 'USER',
        password: hashedPassword,
        emailToken: verificationToken,
        isEmailVerified: false,
        isActive: true,
      };

      const user = await this.userRepository.createUser(userData);

      // Send verification email
      await this.emailService.sendVerificationEmail(user.email, verificationToken);

      // Generate tokens
      const tokenPayload: TokenPayload = {
        userId: user.id,
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
      };

      const accessToken = TokenService.generateAccessToken(tokenPayload);
      const refreshToken = TokenService.generateRefreshToken(user.id);

      // Create session
      const sessionData: CreateSessionData = {
        userId: user.id,
        accessToken,
        refreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        isActive: true,
        userAgent: undefined,
        ipAddress: undefined,
      };

      await this.sessionRepository.createSession(sessionData);

      const authResponse: AuthResponse = {
        user: this.sanitizeUser(user),
        token: accessToken,
        refreshToken,
        expiresIn: 24 * 60 * 60, // 24 hours in seconds
      };

      return {
        success: true,
        data: authResponse,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Registration failed',
      };
    }
  }

  // User login (SRP)
  async login(request: LoginRequest): Promise<BusinessLogicResult<AuthResponse>> {
    try {
      // Find user by email
      const user = await this.userRepository.findByEmail(request.email);
      if (!user) {
        return {
          success: false,
          error: 'Invalid credentials',
        };
      }

      // Check if user is active
      if (!user.isActive) {
        return {
          success: false,
          error: 'Account is deactivated',
        };
      }

      // Verify password
      const isPasswordValid = await PasswordService.compare(request.password, user.password);
      if (!isPasswordValid) {
        return {
          success: false,
          error: 'Invalid credentials',
        };
      }

      // Update last login
      await this.userRepository.updateUser(user.id, {
        lastLogin: new Date(),
      });

      // Generate tokens
      const tokenPayload: TokenPayload = {
        userId: user.id,
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
      };

      const accessToken = TokenService.generateAccessToken(tokenPayload);
      const refreshToken = TokenService.generateRefreshToken(user.id);

      // Create new session (invalidate old ones)
      await this.sessionRepository.deactivateAllUserSessions(user.id);
      
      const sessionData: CreateSessionData = {
        userId: user.id,
        accessToken,
        refreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
        isActive: true,
        userAgent: undefined,
        ipAddress: undefined,
      };

      await this.sessionRepository.createSession(sessionData);

      const authResponse: AuthResponse = {
        user: this.sanitizeUser(user),
        token: accessToken,
        refreshToken,
        expiresIn: 24 * 60 * 60, // 24 hours in seconds
      };

      return {
        success: true,
        data: authResponse,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed',
      };
    }
  }

  // Token refresh (SRP)
  async refreshToken(refreshToken: string): Promise<BusinessLogicResult<AuthResponse>> {
    try {
      // Verify refresh token
      const decoded = TokenService.verifyRefreshToken(refreshToken);

      // Find session
      const session = await this.sessionRepository.findByRefreshToken(refreshToken);
      if (!session || !session.isActive || new Date() > session.expiresAt) {
        return {
          success: false,
          error: 'Invalid or expired refresh token',
        };
      }

      // Find user
      const user = await this.userRepository.findById(decoded.userId);
      if (!user || !user.isActive) {
        return {
          success: false,
          error: 'User not found or inactive',
        };
      }

      // Generate new tokens
      const tokenPayload: TokenPayload = {
        userId: user.id,
        email: user.email,
        role: user.role,
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours
      };

      const newAccessToken = TokenService.generateAccessToken(tokenPayload);
      const newRefreshToken = TokenService.generateRefreshToken(user.id);

      // Update session
      await this.sessionRepository.updateSession(session.id, {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
      });

      const authResponse: AuthResponse = {
        user: this.sanitizeUser(user),
        token: newAccessToken,
        refreshToken: newRefreshToken,
        expiresIn: 24 * 60 * 60, // 24 hours in seconds
      };

      return {
        success: true,
        data: authResponse,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Token refresh failed',
      };
    }
  }

  // Logout (SRP)
  async logout(userId: string): Promise<BusinessLogicResult<void>> {
    try {
      await this.sessionRepository.deactivateAllUserSessions(userId);
      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Logout failed',
      };
    }
  }

  // Email verification (SRP)
  async verifyEmail(token: string): Promise<BusinessLogicResult<void>> {
    try {
      // Find user by verification token
      const user = await this.userRepository.findByEmailToken(token);

      if (!user) {
        return {
          success: false,
          error: 'Invalid verification token',
        };
      }

      // Update user as verified
      await this.userRepository.verifyEmail(user.id);

      // Send welcome email
      await this.emailService.sendWelcomeEmail(user.email, user.name);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Email verification failed',
      };
    }
  }

  // Password reset request (SRP)
  async requestPasswordReset(email: string): Promise<BusinessLogicResult<void>> {
    try {
      const user = await this.userRepository.findByEmail(email);
      if (!user) {
        // Don't reveal if email exists or not
        return {
          success: true,
        };
      }

      // Generate reset token
      const resetToken = RandomTokenService.generateSecureToken();
      const resetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      // Update user with reset token
      await this.userRepository.setResetToken(user.id, resetToken, resetExpires);

      // Send reset email
      await this.emailService.sendPasswordResetEmail(user.email, resetToken);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Password reset request failed',
      };
    }
  }

  // Password reset completion (SRP)
  async resetPassword(token: string, newPassword: string): Promise<BusinessLogicResult<void>> {
    try {
      // Find user by reset token
      const user = await this.userRepository.findByResetToken(token);
      if (!user) {
        return {
          success: false,
          error: 'Invalid or expired reset token',
        };
      }

      // Hash new password
      const hashedPassword = await PasswordService.hash(newPassword);

      // Update user password and clear reset token
      await this.userRepository.updateUser(user.id, {
        password: hashedPassword,
      });

      await this.userRepository.clearResetToken(user.id);

      // Invalidate all user sessions
      await this.sessionRepository.deactivateAllUserSessions(user.id);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Password reset failed',
      };
    }
  }

  // Helper method to sanitize user data (SRP)
  private sanitizeUser(user: User): Omit<User, 'password'> {
    const { password, ...sanitizedUser } = user;
    return sanitizedUser;
  }
}