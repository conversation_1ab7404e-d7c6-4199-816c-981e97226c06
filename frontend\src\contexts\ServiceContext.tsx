import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface Service {
  id: string;
  name: string;
  description: string;
  category: string;
  price: number;
  image: string;
  features: string[];
  isActive: boolean;
  formFields: FormField[];
}

export interface FormField {
  id: string;
  name: string;
  label: string;
  type: 'select' | 'radio' | 'checkbox' | 'number' | 'text';
  required: boolean;
  options?: FormFieldOption[];
  defaultValue?: string;
  priceModifier?: number;
}

export interface FormFieldOption {
  id: string;
  label: string;
  value: string;
  priceModifier: number;
}

export interface GalleryImage {
  id: string;
  url: string;
  category: string;
  title: string;
  description: string;
  createdAt: string;
}

interface ServiceContextType {
  services: Service[];
  addService: (service: Omit<Service, 'id'>) => void;
  updateService: (id: string, service: Partial<Service>) => void;
  deleteService: (id: string) => void;
  getServicesByCategory: (category: string) => Service[];
  addFormField: (serviceId: string, field: Omit<FormField, 'id'>) => void;
  updateFormField: (serviceId: string, fieldId: string, field: Partial<FormField>) => void;
  deleteFormField: (serviceId: string, fieldId: string) => void;
  galleryImages: GalleryImage[];
  addGalleryImage: (image: Omit<GalleryImage, 'id' | 'createdAt'>) => void;
  updateGalleryImage: (id: string, image: Partial<GalleryImage>) => void;
  deleteGalleryImage: (id: string) => void;
  getGalleryImagesByCategory: (category: string) => GalleryImage[];
}

const ServiceContext = createContext<ServiceContextType | undefined>(undefined);

export const useServices = () => {
  const context = useContext(ServiceContext);
  if (context === undefined) {
    throw new Error('useServices must be used within a ServiceProvider');
  }
  return context;
};

interface ServiceProviderProps {
  children: ReactNode;
}

export const ServiceProvider: React.FC<ServiceProviderProps> = ({ children }) => {
  const initialServices = [
    {
      id: '1',
      name: 'Business Cards',
      description: 'Professional business cards with premium finishes',
      category: 'Business Cards',
      price: 29.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      features: ['Premium cardstock', 'Multiple finishes', 'Fast delivery'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'size',
          label: 'Card Size',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: 'Standard (3.5" x 2")', value: 'standard', priceModifier: 0 },
            { id: '2', label: 'Mini (3.3" x 2.1")', value: 'mini', priceModifier: -5 },
            { id: '3', label: 'Square (2.5" x 2.5")', value: 'square', priceModifier: 5 }
          ],
          defaultValue: 'standard'
        },
        {
          id: '6',
          name: 'shape',
          label: 'Card Shape',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: 'Standard Rectangle', value: 'rectangle', priceModifier: 0 },
            { id: '2', label: 'Rounded Corner', value: 'rounded', priceModifier: 3 },
            { id: '3', label: 'Square', value: 'square', priceModifier: 5 },
            { id: '4', label: 'Circle', value: 'circle', priceModifier: 8 },
            { id: '5', label: 'Oval', value: 'oval', priceModifier: 6 },
            { id: '6', label: 'Leaf', value: 'leaf', priceModifier: 10 }
          ],
          defaultValue: 'rectangle'
        },
        {
          id: '2',
          name: 'paper',
          label: 'Paper Type',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: 'Standard Matte', value: 'matte', priceModifier: 0 },
            { id: '2', label: 'Glossy', value: 'glossy', priceModifier: 10 },
            { id: '3', label: 'Premium Linen', value: 'linen', priceModifier: 15 },
            { id: '4', label: 'Recycled', value: 'recycled', priceModifier: 5 }
          ],
          defaultValue: 'matte'
        },
        {
          id: '3',
          name: 'sides',
          label: 'Printing Sides',
          type: 'radio',
          required: true,
          options: [
            { id: '1', label: 'One-sided', value: 'one', priceModifier: 0 },
            { id: '2', label: 'Two-sided', value: 'two', priceModifier: 8 }
          ],
          defaultValue: 'one'
        },
        {
          id: '4',
          name: 'quantity',
          label: 'Quantity',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: '100 cards', value: '100', priceModifier: 0 },
            { id: '2', label: '250 cards', value: '250', priceModifier: 15 },
            { id: '3', label: '500 cards', value: '500', priceModifier: 25 },
            { id: '4', label: '1000 cards', value: '1000', priceModifier: 40 }
          ],
          defaultValue: '100'
        },
        {
          id: '5',
          name: 'coating',
          label: 'Special Coating',
          type: 'checkbox',
          required: false,
          options: [
            { id: '1', label: 'UV Coating (+$12)', value: 'uv', priceModifier: 12 },
            { id: '2', label: 'Spot UV (+$18)', value: 'spot_uv', priceModifier: 18 },
            { id: '3', label: 'Embossing (+$25)', value: 'emboss', priceModifier: 25 }
          ]
        }
      ]
    },
    {
      id: '2',
      name: 'Flyers',
      description: 'Eye-catching flyers for events and promotions',
      category: 'Marketing Materials',
      price: 19.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      features: ['Full color printing', 'Multiple sizes', 'Bulk discounts'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'size',
          label: 'Flyer Size',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: '8.5" x 11"', value: '8.5x11', priceModifier: 0 },
            { id: '2', label: '5.5" x 8.5"', value: '5.5x8.5', priceModifier: -5 },
            { id: '3', label: '11" x 17"', value: '11x17', priceModifier: 15 }
          ],
          defaultValue: '8.5x11'
        },
        {
          id: '2',
          name: 'paper',
          label: 'Paper Weight',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: '70lb Text', value: '70lb', priceModifier: 0 },
            { id: '2', label: '100lb Text', value: '100lb', priceModifier: 8 },
            { id: '3', label: '80lb Cover', value: '80lb_cover', priceModifier: 12 }
          ],
          defaultValue: '70lb'
        },
        {
          id: '3',
          name: 'sides',
          label: 'Printing Sides',
          type: 'radio',
          required: true,
          options: [
            { id: '1', label: 'One-sided', value: 'one', priceModifier: 0 },
            { id: '2', label: 'Two-sided', value: 'two', priceModifier: 10 }
          ],
          defaultValue: 'one'
        }
      ]
    },
    {
      id: '3',
      name: 'Brochures',
      description: 'Professional tri-fold and bi-fold brochures',
      category: 'Marketing Materials',
      price: 39.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75',
      features: ['Glossy finish', 'Folding included', 'Custom design'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'fold',
          label: 'Fold Type',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: 'Tri-fold', value: 'tri', priceModifier: 0 },
            { id: '2', label: 'Bi-fold', value: 'bi', priceModifier: -5 },
            { id: '3', label: 'Z-fold', value: 'z', priceModifier: 8 }
          ],
          defaultValue: 'tri'
        }
      ]
    },
    {
      id: '4',
      name: 'Posters',
      description: 'Large format posters for advertising and decoration',
      category: 'Signs & Banners',
      price: 49.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75',
      features: ['Multiple sizes', 'Weather resistant', 'High quality print'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'size',
          label: 'Poster Size',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: '18" x 24"', value: '18x24', priceModifier: 0 },
            { id: '2', label: '24" x 36"', value: '24x36', priceModifier: 20 },
            { id: '3', label: '36" x 48"', value: '36x48', priceModifier: 45 }
          ],
          defaultValue: '18x24'
        }
      ]
    },
    {
      id: '5',
      name: 'Banners',
      description: 'Vinyl banners for outdoor and indoor use',
      category: 'Signs & Banners',
      price: 79.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      features: ['Vinyl material', 'Weather proof', 'Grommets included'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'size',
          label: 'Banner Size',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: '2\' x 4\'', value: '2x4', priceModifier: 0 },
            { id: '2', label: '3\' x 6\'', value: '3x6', priceModifier: 30 },
            { id: '3', label: '4\' x 8\'', value: '4x8', priceModifier: 60 }
          ],
          defaultValue: '2x4'
        }
      ]
    },
    {
      id: '6',
      name: 'Wedding Invitations',
      description: 'Elegant wedding invitations with custom designs',
      category: 'Invitations & Stationery',
      price: 89.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      features: ['Premium paper', 'Custom design', 'RSVP cards included'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'style',
          label: 'Invitation Style',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: 'Classic', value: 'classic', priceModifier: 0 },
            { id: '2', label: 'Modern', value: 'modern', priceModifier: 15 },
            { id: '3', label: 'Rustic', value: 'rustic', priceModifier: 20 }
          ],
          defaultValue: 'classic'
        }
      ]
    },
    {
      id: '7',
      name: 'Custom Stickers',
      description: 'High-quality vinyl stickers for any purpose',
      category: 'Stickers & Labels',
      price: 24.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      features: ['Waterproof vinyl', 'Custom shapes', 'Bulk pricing'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'material',
          label: 'Material Type',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: 'Vinyl (Waterproof)', value: 'vinyl', priceModifier: 0 },
            { id: '2', label: 'Paper (Indoor)', value: 'paper', priceModifier: -5 },
            { id: '3', label: 'Clear Vinyl', value: 'clear', priceModifier: 8 }
          ],
          defaultValue: 'vinyl'
        }
      ]
    },
    {
      id: '8',
      name: 'Custom T-Shirts',
      description: 'High-quality custom printed t-shirts',
      category: 'Apparel',
      price: 19.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      features: ['100% cotton', 'Multiple colors', 'Screen printing'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'size',
          label: 'T-Shirt Size',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: 'Small', value: 'S', priceModifier: 0 },
            { id: '2', label: 'Medium', value: 'M', priceModifier: 0 },
            { id: '3', label: 'Large', value: 'L', priceModifier: 0 },
            { id: '4', label: 'X-Large', value: 'XL', priceModifier: 2 },
            { id: '5', label: 'XX-Large', value: 'XXL', priceModifier: 4 }
          ],
          defaultValue: 'M'
        }
      ]
    },
    {
      id: '9',
      name: 'Photo Canvas',
      description: 'Beautiful canvas prints for home decoration',
      category: 'Gifts & Décor',
      price: 59.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      features: ['Gallery wrap', 'Fade resistant', 'Ready to hang'],
      isActive: true,
      formFields: [
        {
          id: '1',
          name: 'size',
          label: 'Canvas Size',
          type: 'select',
          required: true,
          options: [
            { id: '1', label: '8" x 10"', value: '8x10', priceModifier: 0 },
            { id: '2', label: '11" x 14"', value: '11x14', priceModifier: 20 },
            { id: '3', label: '16" x 20"', value: '16x20', priceModifier: 40 }
          ],
          defaultValue: '8x10'
        }
      ]
    },
    // Apparel Services
    {
      id: '10',
      name: 'Polo Shirts',
      description: 'Professional polo shirts with custom embroidery or printing',
      category: 'Apparel',
      price: 24.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      features: ['100% cotton', 'Custom embroidery', 'Multiple colors'],
      isActive: true,
      formFields: []
    },
    {
      id: '11',
      name: 'Jackets',
      description: 'Custom jackets for corporate and promotional use',
      category: 'Apparel',
      price: 49.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75',
      features: ['Weather resistant', 'Custom embroidery', 'Professional quality'],
      isActive: true,
      formFields: []
    },
    {
      id: '12',
      name: 'Sweatshirts & Hoodies',
      description: 'Comfortable sweatshirts and hoodies with custom designs',
      category: 'Apparel',
      price: 34.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75',
      features: ['Soft fleece lining', 'Custom printing', 'Unisex sizing'],
      isActive: true,
      formFields: []
    },
    {
      id: '13',
      name: 'Dress Shirts',
      description: 'Professional dress shirts with custom embroidery',
      category: 'Apparel',
      price: 39.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      features: ['Wrinkle resistant', 'Professional fit', 'Custom embroidery'],
      isActive: true,
      formFields: []
    },
    {
      id: '14',
      name: 'Baseball Caps',
      description: 'Classic baseball caps with custom embroidery or printing',
      category: 'Apparel',
      price: 16.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      features: ['Adjustable strap', 'Custom embroidery', 'Multiple colors'],
      isActive: true,
      formFields: []
    },
    {
      id: '15',
      name: 'Trucker Caps',
      description: 'Mesh-back trucker caps with custom designs',
      category: 'Apparel',
      price: 18.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      features: ['Mesh back', 'Snapback closure', 'Custom printing'],
      isActive: true,
      formFields: []
    },
    {
      id: '16',
      name: 'Performance Caps',
      description: 'Athletic performance caps with moisture-wicking technology',
      category: 'Apparel',
      price: 22.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      features: ['Moisture-wicking', 'UV protection', 'Custom embroidery'],
      isActive: true,
      formFields: []
    },
    {
      id: '17',
      name: 'Beanies',
      description: 'Warm knit beanies with custom embroidery',
      category: 'Apparel',
      price: 14.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      features: ['Warm knit material', 'Custom embroidery', 'One size fits all'],
      isActive: true,
      formFields: []
    },
    {
      id: '18',
      name: 'Visors',
      description: 'Sun visors with custom embroidery or printing',
      category: 'Apparel',
      price: 12.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      features: ['UV protection', 'Adjustable strap', 'Custom printing'],
      isActive: true,
      formFields: []
    },
    // Bags Category
    {
      id: '19',
      name: 'Bags',
      description: 'Custom bags for promotional and everyday use',
      category: 'Bags',
      price: 29.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75',
      features: ['Durable materials', 'Custom printing', 'Multiple styles'],
      isActive: true,
      formFields: []
    },
    {
      id: '20',
      name: 'Totes',
      description: 'Eco-friendly tote bags with custom designs',
      category: 'Bags',
      price: 19.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75',
      features: ['Eco-friendly materials', 'Large capacity', 'Custom printing'],
      isActive: true,
      formFields: []
    },
    // Signs & Banners Services
    {
      id: '21',
      name: 'Yard Signs',
      description: 'Weather-resistant yard signs for outdoor advertising',
      category: 'Signs & Banners',
      price: 29.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      features: ['Weather resistant', 'Stakes included', 'Full color printing'],
      isActive: true,
      formFields: []
    },
    {
      id: '22',
      name: 'Rigid Signs & Boards',
      description: 'Durable rigid signs for indoor and outdoor use',
      category: 'Signs & Banners',
      price: 69.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      features: ['Rigid construction', 'Weather proof', 'Multiple sizes'],
      isActive: true,
      formFields: []
    },
    {
      id: '23',
      name: 'Car Signs',
      description: 'Magnetic and adhesive car signs for mobile advertising',
      category: 'Signs & Banners',
      price: 39.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      features: ['Magnetic backing', 'Weather resistant', 'Easy application'],
      isActive: true,
      formFields: []
    },
    {
      id: '24',
      name: 'Door & Desk Signs',
      description: 'Professional door and desk signs for offices',
      category: 'Signs & Banners',
      price: 24.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      features: ['Professional appearance', 'Multiple materials', 'Custom text'],
      isActive: true,
      formFields: []
    },
    // Marketing Materials Services
    {
      id: '25',
      name: 'Tickets & Vouchers',
      description: 'Custom tickets and vouchers for events and promotions',
      category: 'Marketing Materials',
      price: 34.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      features: ['Perforated edges', 'Sequential numbering', 'Custom design'],
      isActive: true,
      formFields: []
    },
    {
      id: '26',
      name: 'Menus',
      description: 'Professional restaurant menus with custom layouts',
      category: 'Marketing Materials',
      price: 44.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      features: ['Laminated finish', 'Custom design', 'Multiple sizes'],
      isActive: true,
      formFields: []
    },
    {
      id: '27',
      name: 'Tabletop',
      description: 'Table tents and tabletop displays for restaurants and events',
      category: 'Marketing Materials',
      price: 19.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75',
      features: ['Self-standing', 'Double-sided printing', 'Durable cardstock'],
      isActive: true,
      formFields: []
    },
    {
      id: '28',
      name: 'Folders & Booklets',
      description: 'Professional folders and booklets for presentations',
      category: 'Marketing Materials',
      price: 54.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75',
      features: ['Professional binding', 'Custom design', 'Multiple page counts'],
      isActive: true,
      formFields: []
    },
    {
      id: '29',
      name: 'Posters & Boards',
      description: 'Large format posters and presentation boards',
      category: 'Marketing Materials',
      price: 64.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      features: ['Large format printing', 'Multiple materials', 'High resolution'],
      isActive: true,
      formFields: []
    },
    {
      id: '30',
      name: 'Magnets',
      description: 'Custom magnets for promotional and advertising use',
      category: 'Marketing Materials',
      price: 29.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      features: ['Strong magnetic backing', 'Weather resistant', 'Custom shapes'],
      isActive: true,
      formFields: []
    },
    {
      id: '31',
      name: 'Door Hangers',
      description: 'Eye-catching door hangers for direct marketing',
      category: 'Marketing Materials',
      price: 24.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      features: ['Die-cut handle', 'Full color printing', 'Targeted marketing'],
      isActive: true,
      formFields: []
    },
    {
      id: '32',
      name: 'Rack Cards',
      description: 'Compact rack cards for information displays',
      category: 'Marketing Materials',
      price: 22.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      features: ['Standard rack size', 'High-quality cardstock', 'Full color printing'],
      isActive: true,
      formFields: []
    }
  ];

  // Add Design Services
  const designServices = [
    {
      id: '33',
      name: 'Logo Design',
      description: 'Professional logo design that represents your brand identity',
      category: 'Design Services',
      price: 199.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      features: ['Custom design', 'Multiple concepts', 'Vector files included'],
      isActive: true,
      formFields: []
    },
    {
      id: '34',
      name: 'Business Card Design',
      description: 'Custom business card design that makes a lasting impression',
      category: 'Design Services',
      price: 89.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      features: ['Professional design', 'Print-ready files', '3 revisions included'],
      isActive: true,
      formFields: []
    },
    {
      id: '35',
      name: 'Brochure Design',
      description: 'Eye-catching brochure design for marketing materials',
      category: 'Design Services',
      price: 149.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75',
      features: ['Custom layout', 'Professional graphics', 'Print specifications'],
      isActive: true,
      formFields: []
    },
    {
      id: '36',
      name: 'Flyer Design',
      description: 'Compelling flyer design for events and promotions',
      category: 'Design Services',
      price: 79.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75',
      features: ['Creative design', 'High-impact visuals', 'Multiple formats'],
      isActive: true,
      formFields: []
    },
    {
      id: '37',
      name: 'Banner Design',
      description: 'Professional banner design for advertising and events',
      category: 'Design Services',
      price: 129.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      features: ['Large format design', 'Brand consistency', 'Print-ready artwork'],
      isActive: true,
      formFields: []
    },
    {
      id: '38',
      name: 'Poster Design',
      description: 'Creative poster design for events and advertising',
      category: 'Design Services',
      price: 99.99,
      image: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      features: ['Artistic design', 'Custom illustrations', 'Various sizes'],
      isActive: true,
      formFields: []
    }
  ];

  const [services, setServices] = useState<Service[]>([
    ...initialServices,
    ...designServices
  ]);

  const [galleryImages, setGalleryImages] = useState<GalleryImage[]>([
    {
      id: '1',
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      category: 'Business Cards',
      title: 'Modern Business Card Design',
      description: 'Clean and professional business card with minimalist design',
      createdAt: '2025-01-15T10:00:00Z'
    },
    {
      id: '2',
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      category: 'Business Cards',
      title: 'Creative Business Cards',
      description: 'Vibrant and eye-catching business card design with bold colors',
      createdAt: '2025-01-15T11:00:00Z'
    },
    {
      id: '3',
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75',
      category: 'Marketing Materials',
      title: 'Professional Brochure',
      description: 'Tri-fold brochure with elegant layout and premium finish',
      createdAt: '2025-01-15T12:00:00Z'
    },
    {
      id: '4',
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75',
      category: 'Invitations & Stationery',
      title: 'Wedding Invitation Suite',
      description: 'Elegant wedding invitations with gold foil accents',
      createdAt: '2025-01-15T13:00:00Z'
    },
    {
      id: '5',
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75',
      category: 'Signs & Banners',
      title: 'Grand Opening Banner',
      description: 'Large format vinyl banner with vibrant graphics',
      createdAt: '2025-01-15T14:00:00Z'
    },
    {
      id: '6',
      url: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75',
      category: 'Stickers & Labels',
      title: 'Custom Vinyl Stickers',
      description: 'Waterproof vinyl stickers with custom die-cut shapes',
      createdAt: '2025-01-15T15:00:00Z'
    }
  ]);

  const addService = (service: Omit<Service, 'id'>) => {
    const newService = {
      ...service,
      id: Date.now().toString(),
      formFields: []
    };
    setServices(prev => [...prev, newService]);
  };

  const updateService = (id: string, updatedService: Partial<Service>) => {
    setServices(prev =>
      prev.map(service =>
        service.id === id ? { ...service, ...updatedService } : service
      )
    );
  };

  const deleteService = (id: string) => {
    setServices(prev => prev.filter(service => service.id !== id));
  };

  const getServicesByCategory = (category: string) => {
    return services.filter(service => service.category === category && service.isActive);
  };

  const addFormField = (serviceId: string, field: Omit<FormField, 'id'>) => {
    const newField = {
      ...field,
      id: Date.now().toString()
    };
    setServices(prev =>
      prev.map(service =>
        service.id === serviceId
          ? { ...service, formFields: [...service.formFields, newField] }
          : service
      )
    );
  };

  const updateFormField = (serviceId: string, fieldId: string, updatedField: Partial<FormField>) => {
    setServices(prev =>
      prev.map(service =>
        service.id === serviceId
          ? {
              ...service,
              formFields: service.formFields.map(field =>
                field.id === fieldId ? { ...field, ...updatedField } : field
              )
            }
          : service
      )
    );
  };

  const deleteFormField = (serviceId: string, fieldId: string) => {
    setServices(prev =>
      prev.map(service =>
        service.id === serviceId
          ? {
              ...service,
              formFields: service.formFields.filter(field => field.id !== fieldId)
            }
          : service
      )
    );
  };

  const addGalleryImage = (image: Omit<GalleryImage, 'id' | 'createdAt'>) => {
    const newImage: GalleryImage = {
      ...image,
      id: Date.now().toString(),
      createdAt: new Date().toISOString()
    };
    setGalleryImages(prev => [...prev, newImage]);
  };

  const updateGalleryImage = (id: string, updatedImage: Partial<GalleryImage>) => {
    setGalleryImages(prev =>
      prev.map(image =>
        image.id === id ? { ...image, ...updatedImage } : image
      )
    );
  };

  const deleteGalleryImage = (id: string) => {
    setGalleryImages(prev => prev.filter(image => image.id !== id));
  };

  const getGalleryImagesByCategory = (category: string) => {
    return galleryImages.filter(image => image.category === category);
  };

  const value = {
    services,
    addService,
    updateService,
    deleteService,
    getServicesByCategory,
    addFormField,
    updateFormField,
    deleteFormField,
    galleryImages,
    addGalleryImage,
    updateGalleryImage,
    deleteGalleryImage,
    getGalleryImagesByCategory
  };

  return (
    <ServiceContext.Provider value={value}>
      {children}
    </ServiceContext.Provider>
  );
};