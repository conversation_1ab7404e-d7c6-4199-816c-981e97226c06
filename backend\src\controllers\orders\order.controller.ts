// Order Controller - Following established patterns and SOLID principles
import {Request, Response} from 'express';
import {OrderManagementService} from '@/services/orders';
import {ResponseFormatter} from '@/utils/formatting';
import {
	CreateOrderRequest,
	UpdateOrderRequest,
	OrderSearchRequest,
	OrderStatusUpdateRequest,
	PaymentUpdateRequest,
	OrderActionRequest,
} from '@/types/orders';

// Order Controller (SRP - Single Responsibility Principle)
export class OrderController {
	constructor(private orderService: OrderManagementService) {}

	// Create a new order (SRP)
	async createOrder(req: Request, res: Response): Promise<void> {
		try {
			const request: CreateOrderRequest = req.body;
			const userId = req.user?.id;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.orderService.createOrder(request, userId);

			if (result.success) {
				res
					.status(201)
					.json(
						ResponseFormatter.success('Order created successfully', result.data)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get order by ID (SRP)
	async getOrderById(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const result = await this.orderService.getOrderById(id);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Order retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(404).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get order by order number (SRP)
	async getOrderByOrderNumber(req: Request, res: Response): Promise<void> {
		try {
			const {orderNumber} = req.params;
			const result = await this.orderService.getOrderByOrderNumber(orderNumber);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Order retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(404).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get orders by user ID (SRP)
	async getOrdersByUserId(req: Request, res: Response): Promise<void> {
		try {
			const {userId} = req.params;
			const result = await this.orderService.getOrdersByUserId(userId);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Orders retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get current user's orders (SRP)
	async getMyOrders(req: Request, res: Response): Promise<void> {
		try {
			const userId = req.user?.id;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.orderService.getOrdersByUserId(userId);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Orders retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get orders by provider ID (SRP)
	async getOrdersByProviderId(req: Request, res: Response): Promise<void> {
		try {
			const {providerId} = req.params;
			const result = await this.orderService.getOrdersByProviderId(providerId);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Orders retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Update order (SRP)
	async updateOrder(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const request: UpdateOrderRequest = req.body;
			const userId = req.user?.id;
			const userRole = req.user?.role;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.orderService.updateOrder(
				id,
				request,
				userId,
				userRole
			);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success('Order updated successfully', result.data)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Delete order (SRP)
	async deleteOrder(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const userId = req.user?.id;
			const userRole = req.user?.role;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.orderService.deleteOrder(id, userId, userRole);

			if (result.success) {
				res
					.status(200)
					.json(ResponseFormatter.success('Order deleted successfully'));
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Search orders (SRP)
	async searchOrders(req: Request, res: Response): Promise<void> {
		try {
			const searchParams: OrderSearchRequest = {
				query: req.query.query as string,
				filters: {
					status: req.query.status
						? ((req.query.status as string).split(',') as any)
						: undefined,
					paymentStatus: req.query.paymentStatus
						? ((req.query.paymentStatus as string).split(',') as any)
						: undefined,
					serviceId: req.query.serviceId as string,
					providerId: req.query.providerId as string,
					userId: req.query.userId as string,
					minAmount: req.query.minAmount
						? parseFloat(req.query.minAmount as string)
						: undefined,
					maxAmount: req.query.maxAmount
						? parseFloat(req.query.maxAmount as string)
						: undefined,
					scheduledDateFrom: req.query.scheduledDateFrom
						? new Date(req.query.scheduledDateFrom as string)
						: undefined,
					scheduledDateTo: req.query.scheduledDateTo
						? new Date(req.query.scheduledDateTo as string)
						: undefined,
					createdAtFrom: req.query.createdAtFrom
						? new Date(req.query.createdAtFrom as string)
						: undefined,
					createdAtTo: req.query.createdAtTo
						? new Date(req.query.createdAtTo as string)
						: undefined,
					completedDateFrom: req.query.completedDateFrom
						? new Date(req.query.completedDateFrom as string)
						: undefined,
					completedDateTo: req.query.completedDateTo
						? new Date(req.query.completedDateTo as string)
						: undefined,
					sortBy: req.query.sortBy as any,
					sortOrder: req.query.sortOrder as 'asc' | 'desc',
				},
				pagination: {
					page: req.query.page ? parseInt(req.query.page as string) : 1,
					limit: req.query.limit ? parseInt(req.query.limit as string) : 10,
				},
			};

			const result = await this.orderService.searchOrders(searchParams);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Orders retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Update order status (SRP)
	async updateOrderStatus(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const request: OrderStatusUpdateRequest = req.body;
			const userId = req.user?.id;
			const userRole = req.user?.role;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.orderService.updateOrderStatus(
				id,
				request,
				userId,
				userRole
			);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Order status updated successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Update payment status (SRP)
	async updatePaymentStatus(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const request: PaymentUpdateRequest = req.body;
			const userId = req.user?.id;
			const userRole = req.user?.role;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.orderService.updatePaymentStatus(
				id,
				request,
				userId,
				userRole
			);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Payment status updated successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Process order action (SRP)
	async processOrderAction(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const request: OrderActionRequest = req.body;
			const userId = req.user?.id;
			const userRole = req.user?.role;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.orderService.processOrderAction(
				id,
				request,
				userId,
				userRole
			);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Order action processed successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get order metrics (SRP)
	async getOrderMetrics(req: Request, res: Response): Promise<void> {
		try {
			const userId = req.query.userId as string;
			const providerId = req.query.providerId as string;
			const result = await this.orderService.getOrderMetrics(
				userId,
				providerId
			);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Order metrics retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get order statistics (SRP)
	async getOrderStatistics(req: Request, res: Response): Promise<void> {
		try {
			const result = await this.orderService.getOrderStatistics();

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Order statistics retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get recent orders (SRP)
	async getRecentOrders(req: Request, res: Response): Promise<void> {
		try {
			const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
			const result = await this.orderService.getRecentOrders(limit);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Recent orders retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get overdue orders (SRP)
	async getOverdueOrders(req: Request, res: Response): Promise<void> {
		try {
			const result = await this.orderService.getOverdueOrders();

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Overdue orders retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}
}
