// Authentication middleware - Following SRP and DIP principles
import { Request, Response, NextFunction } from 'express';
import { TokenService } from '@/utils/crypto';
import { ResponseFormatter } from '@/utils/formatting';
import { HTTP_STATUS, ERROR_MESSAGES } from '@/constants';
import { TokenPayload } from '@/types/auth';
import { SessionRepository } from '@/repositories';
import { prisma } from '@/config/database';

// Extend Express Request type
declare global {
  namespace Express {
    interface Request {
      user?: TokenPayload;
      sessionId?: string;
    }
  }
}

// Create session repository instance
const sessionRepository = new SessionRepository(prisma);

// Authentication middleware with session validation (SRP)
export const authenticate = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const token = TokenService.extractTokenFromHeader(req.headers.authorization);
    
    if (!token) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseFormatter.error(
          ERROR_MESSAGES.ACCESS_DENIED,
          'No token provided',
          HTTP_STATUS.UNAUTHORIZED,
          req.path
        )
      );
      return;
    }
    
    // Verify token first
    const decoded = TokenService.verifyAccessToken(token);
    
    // Validate session in database
    const session = await sessionRepository.findByAccessToken(token);
    if (!session || !session.isActive || !session.user?.isActive) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseFormatter.error(
          ERROR_MESSAGES.TOKEN_INVALID,
          'Invalid or expired session',
          HTTP_STATUS.UNAUTHORIZED,
          req.path
        )
      );
      return;
    }
    
    // Check if user email is verified for protected routes
    if (!session.user.isEmailVerified && req.path !== '/verify-email') {
      res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseFormatter.error(
          ERROR_MESSAGES.ACCESS_DENIED,
          'Email verification required',
          HTTP_STATUS.FORBIDDEN,
          req.path
        )
      );
      return;
    }
    
    req.user = {
      userId: session.user.id,
      email: session.user.email,
      name: session.user.name,
      role: session.user.role,
      iat: decoded.iat,
      exp: decoded.exp,
    };
    req.sessionId = session.id;
    
    next();
  } catch (error) {
    res.status(HTTP_STATUS.UNAUTHORIZED).json(
      ResponseFormatter.error(
        ERROR_MESSAGES.TOKEN_INVALID,
        'Invalid or expired token',
        HTTP_STATUS.UNAUTHORIZED,
        req.path
      )
    );
  }
};

// Authorization middleware factory (Open/Closed principle)
export const authorize = (roles: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseFormatter.error(
          ERROR_MESSAGES.ACCESS_DENIED,
          'Authentication required',
          HTTP_STATUS.UNAUTHORIZED,
          req.path
        )
      );
      return;
    }
    
    if (roles.length > 0 && !roles.includes(req.user.role)) {
      res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseFormatter.error(
          ERROR_MESSAGES.ACCESS_DENIED,
          'Insufficient permissions',
          HTTP_STATUS.FORBIDDEN,
          req.path
        )
      );
      return;
    }
    
    next();
  };
};

// Optional authentication middleware (for routes that can work with or without auth)
export const optionalAuth = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const token = TokenService.extractTokenFromHeader(req.headers.authorization);
    
    if (token) {
      const decoded = TokenService.verifyAccessToken(token);
      req.user = decoded;
    }
    
    next();
  } catch (error) {
    // If token is invalid, continue without authentication
    next();
  }
};

// Admin-only middleware
export const adminOnly = authorize(['ADMIN']);

// Provider-only middleware
export const providerOnly = authorize(['PROVIDER']);

// Provider or admin middleware
export const providerOrAdmin = authorize(['PROVIDER', 'ADMIN']);

// User authentication middleware (any authenticated user)
export const userOnly = authorize(['USER', 'PROVIDER', 'ADMIN']);

// Resource owner middleware factory (for checking if user owns the resource)
export const resourceOwner = (getUserIdFromResource: (req: Request) => string) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(HTTP_STATUS.UNAUTHORIZED).json(
        ResponseFormatter.error(
          ERROR_MESSAGES.ACCESS_DENIED,
          'Authentication required',
          HTTP_STATUS.UNAUTHORIZED,
          req.path
        )
      );
      return;
    }
    
    const resourceUserId = getUserIdFromResource(req);
    
    // Admin can access any resource, others can only access their own
    if (req.user.role === 'ADMIN' || req.user.userId === resourceUserId) {
      next();
    } else {
      res.status(HTTP_STATUS.FORBIDDEN).json(
        ResponseFormatter.error(
          ERROR_MESSAGES.ACCESS_DENIED,
          'You can only access your own resources',
          HTTP_STATUS.FORBIDDEN,
          req.path
        )
      );
    }
  };
};

// Rate limiting by user
export const rateLimitByUser = (maxRequests: number = 100, windowMs: number = 15 * 60 * 1000) => {
  const userRequestCounts = new Map<string, { count: number; resetTime: number }>();
  
  return (req: Request, res: Response, next: NextFunction): void => {
    const userId = req.user?.userId || req.ip;
    const now = Date.now();
    
    const userRecord = userRequestCounts.get(userId);
    
    if (!userRecord || now > userRecord.resetTime) {
      userRequestCounts.set(userId, {
        count: 1,
        resetTime: now + windowMs,
      });
      next();
      return;
    }
    
    if (userRecord.count >= maxRequests) {
      res.status(429).json(
        ResponseFormatter.error(
          'Too many requests',
          'Rate limit exceeded',
          429,
          req.path
        )
      );
      return;
    }
    
    userRecord.count++;
    next();
  };
};