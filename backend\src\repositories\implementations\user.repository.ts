import {PrismaClient} from '@prisma/client';
import {IUserRepository} from '../interfaces/user.repository';
import {User, UserAddress, UserProfile, UserStats} from '@/types/user.types';
import {PaginationParams, PaginatedResponse} from '@/types/common.types';
import {NotFoundError} from '@/types/common.types';

export class UserRepository implements IUserRepository {
	constructor(private prisma: PrismaClient) {}

	async createUser(userData: Partial<User>): Promise<User> {
		const user = await this.prisma.user.create({
			data: {
				email: userData.email!,
				name: userData.name!,
				password: userData.password,
				role: userData.role!,
				phone: userData.phone,
				avatar: userData.avatar,
				isActive: userData.isActive ?? true,
				isVerified: userData.isVerified ?? false,
			},
		});

		return this.mapToUser(user);
	}

	async findUserById(id: string): Promise<User | null> {
		const user = await this.prisma.user.findUnique({
			where: {id},
		});

		return user ? this.mapToUser(user) : null;
	}

	async findUserByEmail(email: string): Promise<User | null> {
		const user = await this.prisma.user.findUnique({
			where: {email},
		});

		return user ? this.mapToUser(user) : null;
	}

	async updateUser(id: string, userData: Partial<User>): Promise<User> {
		const user = await this.prisma.user.update({
			where: {id},
			data: {
				name: userData.name,
				phone: userData.phone,
				avatar: userData.avatar,
				isActive: userData.isActive,
				isVerified: userData.isVerified,
			},
		});

		return this.mapToUser(user);
	}

	async deleteUser(id: string): Promise<void> {
		await this.prisma.user.delete({
			where: {id},
		});
	}

	async listUsers(params: PaginationParams): Promise<PaginatedResponse<User>> {
		const {page, limit, sortBy = 'createdAt', sortOrder = 'desc'} = params;
		const skip = (page - 1) * limit;

		const [users, total] = await Promise.all([
			this.prisma.user.findMany({
				skip,
				take: limit,
				orderBy: {[sortBy]: sortOrder},
			}),
			this.prisma.user.count(),
		]);

		return {
			success: true,
			data: users.map((user) => this.mapToUser(user)),
			pagination: {
				page,
				limit,
				total,
				totalPages: Math.ceil(total / limit),
				hasNext: page * limit < total,
				hasPrev: page > 1,
			},
		};
	}

	async getUserProfile(id: string): Promise<UserProfile | null> {
		const user = await this.prisma.user.findUnique({
			where: {id},
			include: {
				addresses: {
					orderBy: {isDefault: 'desc'},
				},
			},
		});

		if (!user) return null;

		const defaultAddress = user.addresses.find((addr) => addr.isDefault);
		const addresses = user.addresses.map((addr) => this.mapToUserAddress(addr));

		return {
			...this.mapToUser(user),
			addresses,
			defaultAddress: defaultAddress
				? this.mapToUserAddress(defaultAddress)
				: undefined,
		};
	}

	async getUserStats(id: string): Promise<UserStats | null> {
		const user = await this.prisma.user.findUnique({
			where: {id},
			include: {
				orders: {
					select: {
						status: true,
						totalAmount: true,
						createdAt: true,
					},
				},
			},
		});

		if (!user) return null;

		const totalOrders = user.orders.length;
		const completedOrders = user.orders.filter(
			(order) => order.status === 'COMPLETED'
		).length;
		const pendingOrders = user.orders.filter(
			(order) => order.status === 'PENDING'
		).length;
		const totalSpent = user.orders
			.filter((order) => order.status === 'COMPLETED')
			.reduce((sum, order) => sum + Number(order.totalAmount), 0);

		return {
			totalOrders,
			completedOrders,
			pendingOrders,
			totalSpent,
			memberSince: user.createdAt,
		};
	}

	async createAddress(
		userId: string,
		addressData: Partial<UserAddress>
	): Promise<UserAddress> {
		const address = await this.prisma.userAddress.create({
			data: {
				userId,
				type: addressData.type!,
				street: addressData.street!,
				city: addressData.city!,
				state: addressData.state!,
				zipCode: addressData.zipCode!,
				country: addressData.country ?? 'US',
				isDefault: addressData.isDefault ?? false,
			},
		});

		return this.mapToUserAddress(address);
	}

	async findAddressById(id: string): Promise<UserAddress | null> {
		const address = await this.prisma.userAddress.findUnique({
			where: {id},
		});

		return address ? this.mapToUserAddress(address) : null;
	}

	async findAddressesByUserId(userId: string): Promise<UserAddress[]> {
		const addresses = await this.prisma.userAddress.findMany({
			where: {userId},
			orderBy: {isDefault: 'desc'},
		});

		return addresses.map((addr) => this.mapToUserAddress(addr));
	}

	async updateAddress(
		id: string,
		addressData: Partial<UserAddress>
	): Promise<UserAddress> {
		const address = await this.prisma.userAddress.update({
			where: {id},
			data: {
				type: addressData.type,
				street: addressData.street,
				city: addressData.city,
				state: addressData.state,
				zipCode: addressData.zipCode,
				country: addressData.country,
				isDefault: addressData.isDefault,
			},
		});

		return this.mapToUserAddress(address);
	}

	async deleteAddress(id: string): Promise<void> {
		await this.prisma.userAddress.delete({
			where: {id},
		});
	}

	async setDefaultAddress(userId: string, addressId: string): Promise<void> {
		await this.prisma.$transaction([
			// Remove default from all user addresses
			this.prisma.userAddress.updateMany({
				where: {userId},
				data: {isDefault: false},
			}),
			// Set the specified address as default
			this.prisma.userAddress.update({
				where: {id: addressId},
				data: {isDefault: true},
			}),
		]);
	}

	async searchUsers(
		query: string,
		params: PaginationParams
	): Promise<PaginatedResponse<User>> {
		const {page, limit, sortBy = 'createdAt', sortOrder = 'desc'} = params;
		const skip = (page - 1) * limit;

		const [users, total] = await Promise.all([
			this.prisma.user.findMany({
				where: {
					OR: [
						{name: {contains: query, mode: 'insensitive'}},
						{email: {contains: query, mode: 'insensitive'}},
					],
				},
				skip,
				take: limit,
				orderBy: {[sortBy]: sortOrder},
			}),
			this.prisma.user.count({
				where: {
					OR: [
						{name: {contains: query, mode: 'insensitive'}},
						{email: {contains: query, mode: 'insensitive'}},
					],
				},
			}),
		]);

		return {
			success: true,
			data: users.map((user) => this.mapToUser(user)),
			pagination: {
				page,
				limit,
				total,
				totalPages: Math.ceil(total / limit),
				hasNext: page * limit < total,
				hasPrev: page > 1,
			},
		};
	}

	async verifyUserEmail(id: string): Promise<User> {
		const user = await this.prisma.user.update({
			where: {id},
			data: {
				isVerified: true,
				emailVerified: new Date(),
			},
		});

		return this.mapToUser(user);
	}

	async updateUserVerification(id: string, isVerified: boolean): Promise<User> {
		const user = await this.prisma.user.update({
			where: {id},
			data: {
				isVerified,
				emailVerified: isVerified ? new Date() : null,
			},
		});

		return this.mapToUser(user);
	}

	// Helper methods for mapping Prisma models to domain types
	private mapToUser(prismaUser: any): User {
		return {
			id: prismaUser.id,
			email: prismaUser.email,
			name: prismaUser.name,
			role: prismaUser.role,
			isActive: prismaUser.isActive,
			isVerified: prismaUser.isVerified,
			emailVerified: prismaUser.emailVerified,
			avatar: prismaUser.avatar,
			phone: prismaUser.phone,
			createdAt: prismaUser.createdAt,
			updatedAt: prismaUser.updatedAt,
		};
	}

	private mapToUserAddress(prismaAddress: any): UserAddress {
		return {
			id: prismaAddress.id,
			userId: prismaAddress.userId,
			type: prismaAddress.type,
			street: prismaAddress.street,
			city: prismaAddress.city,
			state: prismaAddress.state,
			zipCode: prismaAddress.zipCode,
			country: prismaAddress.country,
			isDefault: prismaAddress.isDefault,
			createdAt: prismaAddress.createdAt,
			updatedAt: prismaAddress.updatedAt,
		};
	}
}
