# Provider Management & Order Management API Layers

This document provides a comprehensive overview of the newly implemented
Provider Management and Order Management API layers, following SOLID principles
and established architectural patterns.

## Overview

The PrintWeditt backend now includes two comprehensive API layers:

1. **Provider Management API** - Handles provider profiles, verification, and
   business operations
2. **Order Management API** - Manages order lifecycle, status updates, and
   payment processing

Both layers follow the established architecture patterns and SOLID principles
outlined in the `ARCHITECTURE.md` file.

## Architecture Principles Applied

### SOLID Principles

- **Single Responsibility Principle (SRP)**: Each class has one clear purpose
- **Open/Closed Principle (OCP)**: Extensible without modification
- **Liskov Substitution Principle (LSP)**: Interfaces are substitutable
- **Interface Segregation Principle (ISP)**: Focused, specific interfaces
- **Dependency Inversion Principle (DIP)**: Depend on abstractions, not
  concretions

### Additional Principles

- **DRY (Don't Repeat Yourself)**: Reusable components and utilities
- **Separation of Concerns**: Clear boundaries between layers
- **Scalable Folder Structure**: Easy to extend and maintain

## Provider Management API

### Overview

The Provider Management API handles all aspects of provider business operations,
including profile management, verification, and business metrics.

### Key Features

- **Provider Profile Management**: Create, read, update, delete provider
  profiles
- **Verification System**: Submit and process verification requests
- **Business Metrics**: Track performance, ratings, and statistics
- **Search & Filtering**: Advanced search with multiple filter options
- **Admin Controls**: Provider verification and deactivation

### API Endpoints

#### Public Endpoints

```
GET    /api/v1/providers                    # Search providers
GET    /api/v1/providers/verified           # Get verified providers
GET    /api/v1/providers/top-rated          # Get top rated providers
GET    /api/v1/providers/:id                # Get provider by ID
GET    /api/v1/providers/user/:userId       # Get provider by user ID
```

#### Authenticated Endpoints

```
GET    /api/v1/providers/me                 # Get current user's provider profile
POST   /api/v1/providers                    # Create provider profile
PUT    /api/v1/providers/:id                # Update provider profile
DELETE /api/v1/providers/:id                # Delete provider profile
POST   /api/v1/providers/:id/verification-request  # Submit verification request
```

#### Admin Only Endpoints

```
GET    /api/v1/providers/stats              # Get provider statistics
POST   /api/v1/providers/:id/verify         # Verify provider
POST   /api/v1/providers/:id/deactivate     # Deactivate provider
```

### Data Models

#### Provider Profile

```typescript
interface ProviderProfile {
	id: string;
	userId: string;
	businessName: string;
	description?: string;
	website?: string;
	businessPhone?: string;
	businessAddress?: string;
	businessCity?: string;
	businessState?: string;
	businessZip?: string;
	licenseNumber?: string;
	insuranceNumber?: string;
	isVerified: boolean;
	verifiedAt?: Date;
	rating?: number;
	totalReviews: number;
	totalOrders: number;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}
```

#### Provider Metrics

```typescript
interface ProviderMetrics {
	totalServices: number;
	activeServices: number;
	totalOrders: number;
	completedOrders: number;
	cancelledOrders: number;
	averageRating: number;
	totalReviews: number;
	responseRate: number;
	averageResponseTime: number;
	completionRate: number;
	revenue: {
		total: number;
		thisMonth: number;
		lastMonth: number;
	};
	topServices: Array<{
		serviceId: string;
		serviceName: string;
		orderCount: number;
		revenue: number;
	}>;
}
```

### Business Rules

- Maximum 10 specialties per provider
- Maximum 20 certifications per provider
- Maximum 15 service areas per provider
- Business name must be 2-100 characters
- Description must be 10-1000 characters
- Response time must be 1-72 hours

## Order Management API

### Overview

The Order Management API handles the complete order lifecycle, from creation to
completion, including status management, payment processing, and order tracking.

### Key Features

- **Order Lifecycle Management**: Complete order workflow from creation to
  completion
- **Status Management**: Track order status with validation rules
- **Payment Processing**: Handle payment status updates
- **File Management**: Upload and manage order-related files
- **Advanced Search**: Comprehensive search with multiple filters
- **Metrics & Analytics**: Order statistics and performance metrics
- **Action Processing**: Confirm, start, complete, cancel, and refund orders

### API Endpoints

#### Public Endpoints

```
GET    /api/v1/orders/number/:orderNumber   # Get order by order number
```

#### Authenticated Endpoints

```
GET    /api/v1/orders/me                    # Get current user's orders
POST   /api/v1/orders                       # Create new order
GET    /api/v1/orders/:id                   # Get order by ID
PUT    /api/v1/orders/:id                   # Update order
DELETE /api/v1/orders/:id                   # Delete order
POST   /api/v1/orders/:id/action            # Process order action
```

#### Provider/Admin Endpoints

```
GET    /api/v1/orders                       # Search orders
GET    /api/v1/orders/overdue               # Get overdue orders
GET    /api/v1/orders/metrics               # Get order metrics
GET    /api/v1/orders/user/:userId          # Get orders by user ID
GET    /api/v1/orders/provider/:providerId  # Get orders by provider ID
PUT    /api/v1/orders/:id/status            # Update order status
```

#### Admin Only Endpoints

```
GET    /api/v1/orders/recent                # Get recent orders
GET    /api/v1/orders/stats                 # Get order statistics
PUT    /api/v1/orders/:id/payment           # Update payment status
```

### Data Models

#### Order with Relations

```typescript
interface OrderWithRelations {
	id: string;
	orderNumber: string;
	serviceId: string;
	userId: string;
	providerId: string;
	status: OrderStatus;
	totalAmount: number;
	paymentStatus: PaymentStatus;
	paymentMethod?: string;
	customerNotes?: string;
	providerNotes?: string;
	scheduledDate?: Date;
	completedDate?: Date;
	cancelledDate?: Date;
	cancellationReason?: string;
	files: string[];
	createdAt: Date;
	updatedAt: Date;
	service: {
		id: string;
		name: string;
		description: string;
		price: number;
		priceType: ServicePriceType;
		images: string[];
		provider: {
			id: string;
			businessName: string;
			rating?: number;
		};
	};
	user: {
		id: string;
		name: string;
		email: string;
		profileImage?: string;
	};
	provider: {
		id: string;
		businessName: string;
		rating?: number;
		totalReviews: number;
	};
	review?: {
		id: string;
		rating: number;
		comment?: string;
		createdAt: Date;
	};
}
```

#### Order Metrics

```typescript
interface OrderMetrics {
	totalOrders: number;
	pendingOrders: number;
	confirmedOrders: number;
	inProgressOrders: number;
	completedOrders: number;
	cancelledOrders: number;
	refundedOrders: number;
	totalRevenue: number;
	averageOrderValue: number;
	completionRate: number;
	cancellationRate: number;
	averageProcessingTime: number;
	topServices: Array<{
		serviceId: string;
		serviceName: string;
		orderCount: number;
		revenue: number;
	}>;
	monthlyStats: Array<{
		month: string;
		orders: number;
		revenue: number;
	}>;
}
```

### Order Status Flow

```
PENDING → CONFIRMED → IN_PROGRESS → COMPLETED
    ↓         ↓           ↓           ↓
  CANCELLED  CANCELLED  CANCELLED   REFUNDED
```

### Business Rules

- Maximum 10 files per order
- Maximum file size: 10MB
- Allowed file types: JPEG, PNG, GIF, PDF, TXT
- Scheduled date must be 1 hour to 30 days in advance
- Auto-cancel after 72 hours of inactivity
- Refund policy: Full refund within 24 hours, partial within 72 hours, no refund
  after 7 days

## File Structure

```
backend/src/
├── types/
│   ├── providers/
│   │   ├── index.ts
│   │   └── provider.types.ts
│   └── orders/
│       ├── index.ts
│       └── order.types.ts
├── repositories/
│   ├── providers/
│   │   ├── index.ts
│   │   └── provider.repository.ts
│   └── orders/
│       ├── index.ts
│       └── order.repository.ts
├── services/
│   ├── providers/
│   │   ├── index.ts
│   │   └── provider-management.service.ts
│   └── orders/
│       ├── index.ts
│       └── order-management.service.ts
├── controllers/
│   ├── providers/
│   │   ├── index.ts
│   │   └── provider.controller.ts
│   └── orders/
│       ├── index.ts
│       └── order.controller.ts
└── routes/
    ├── providers/
    │   ├── index.ts
    │   └── provider.routes.ts
    └── orders/
        ├── index.ts
        └── order.routes.ts
```

## Integration Points

### Database Integration

- Uses existing Prisma schema with `ProviderProfile` and `Order` models
- Maintains referential integrity with foreign key relationships
- Supports complex queries with eager loading of related data

### Authentication & Authorization

- Integrates with existing auth middleware
- Role-based access control (User, Provider, Admin)
- Permission validation for sensitive operations

### Email Notifications

- Order confirmation emails
- Status update notifications
- Provider verification emails
- Payment status updates

### File Upload

- Integrates with existing file upload service
- Supports multiple file types
- Organized folder structure for different entities

## Error Handling

Both API layers implement comprehensive error handling:

- **Validation Errors**: Business rule violations and input validation
- **Authorization Errors**: Permission and access control violations
- **Not Found Errors**: Resource not found scenarios
- **Business Logic Errors**: Domain-specific rule violations

## Testing Strategy

### Unit Tests

- Service layer business logic
- Repository data access methods
- Controller request/response handling

### Integration Tests

- API endpoint testing
- Database integration
- Authentication flow

### E2E Tests

- Complete user workflows
- Order lifecycle scenarios
- Provider verification process

## Performance Considerations

### Database Optimization

- Indexed queries for common search patterns
- Efficient pagination implementation
- Optimized joins for related data

### Caching Strategy

- Provider profile caching
- Order statistics caching
- Search result caching

### Scalability

- Horizontal scaling support
- Database connection pooling
- Async processing for notifications

## Security Features

### Input Validation

- Comprehensive request validation
- SQL injection prevention
- XSS protection

### Access Control

- Role-based permissions
- Resource ownership validation
- Admin-only operations

### Data Protection

- Sensitive data encryption
- Audit logging
- GDPR compliance considerations

## Monitoring & Observability

### Logging

- Structured logging for all operations
- Error tracking and alerting
- Performance metrics collection

### Metrics

- API response times
- Error rates
- Business metrics tracking

### Health Checks

- Database connectivity
- External service dependencies
- Overall system health

## Future Enhancements

### Planned Features

- Real-time notifications (WebSocket)
- Advanced analytics dashboard
- Bulk operations support
- API rate limiting
- GraphQL support

### Scalability Improvements

- Microservices architecture
- Event-driven architecture
- CQRS pattern implementation
- Distributed caching

## Conclusion

The Provider Management and Order Management API layers provide a solid
foundation for the PrintWeditt platform's business operations. By following
established architectural patterns and SOLID principles, these layers are:

- **Maintainable**: Clear separation of concerns and single responsibilities
- **Testable**: Dependency injection enables easy unit testing
- **Scalable**: Modular design allows for easy scaling
- **Secure**: Comprehensive validation and authorization
- **Extensible**: Open/closed principle allows for future enhancements

These API layers integrate seamlessly with the existing codebase while providing
the functionality needed for a robust service marketplace platform.
