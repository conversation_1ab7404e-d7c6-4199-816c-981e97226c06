import {
	Service,
	ServiceCategory,
	ServiceFormField,
	ServiceFieldOption,
	ServiceWithCategory,
	ServiceWithFormFields,
} from '@/types/service.types';
import {PaginationParams, PaginatedResponse} from '@/types/common.types';

export interface IServiceRepository {
	// Service category operations
	createCategory(
		categoryData: Partial<ServiceCategory>
	): Promise<ServiceCategory>;
	findCategoryById(id: string): Promise<ServiceCategory | null>;
	findCategoryByRoute(route: string): Promise<ServiceCategory | null>;
	updateCategory(
		id: string,
		categoryData: Partial<ServiceCategory>
	): Promise<ServiceCategory>;
	deleteCategory(id: string): Promise<void>;
	listCategories(): Promise<ServiceCategory[]>;

	// Service operations
	createService(serviceData: Partial<Service>): Promise<Service>;
	findServiceById(id: string): Promise<Service | null>;
	findServiceWithCategory(id: string): Promise<ServiceWithCategory | null>;
	findServiceWithFormFields(id: string): Promise<ServiceWithFormFields | null>;
	updateService(id: string, serviceData: Partial<Service>): Promise<Service>;
	deleteService(id: string): Promise<void>;
	listServices(params: PaginationParams): Promise<PaginatedResponse<Service>>;
	listServicesByCategory(
		categoryId: string,
		params: PaginationParams
	): Promise<PaginatedResponse<Service>>;
	listActiveServices(): Promise<Service[]>;

	// Form field operations
	createFormField(
		serviceId: string,
		fieldData: Partial<ServiceFormField>
	): Promise<ServiceFormField>;
	findFormFieldById(id: string): Promise<ServiceFormField | null>;
	findFormFieldsByServiceId(serviceId: string): Promise<ServiceFormField[]>;
	updateFormField(
		id: string,
		fieldData: Partial<ServiceFormField>
	): Promise<ServiceFormField>;
	deleteFormField(id: string): Promise<void>;

	// Field option operations
	createFieldOption(
		fieldId: string,
		optionData: Partial<ServiceFieldOption>
	): Promise<ServiceFieldOption>;
	findFieldOptionById(id: string): Promise<ServiceFieldOption | null>;
	findFieldOptionsByFieldId(fieldId: string): Promise<ServiceFieldOption[]>;
	updateFieldOption(
		id: string,
		optionData: Partial<ServiceFieldOption>
	): Promise<ServiceFieldOption>;
	deleteFieldOption(id: string): Promise<void>;

	// Search operations
	searchServices(
		query: string,
		params: PaginationParams
	): Promise<PaginatedResponse<Service>>;
	searchServicesByCategory(
		categoryId: string,
		query: string,
		params: PaginationParams
	): Promise<PaginatedResponse<Service>>;
}
