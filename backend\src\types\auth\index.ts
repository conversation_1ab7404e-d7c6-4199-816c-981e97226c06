// Authentication Types - Enhanced from frontend auth types
import { BaseEntity } from '@/types/common';

export interface User extends BaseEntity {
  email: string;
  name: string;
  role: 'user' | 'admin' | 'provider';
  password: string; // Hashed password
  isEmailVerified: boolean;
  emailToken?: string;
  resetToken?: string;
  resetTokenExpiry?: Date;
  lastLogin?: Date;
  isActive: boolean;
  profileImage?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  profile?: UserProfile;
}

export interface UserProfile extends BaseEntity {
  userId: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  address?: Address;
  avatar?: string;
  preferences?: UserPreferences;
}

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
}

export interface UserPreferences {
  notifications: {
    email: boolean;
    sms: boolean;
    marketing: boolean;
  };
  theme: 'light' | 'dark';
  language: string;
}

// Authentication Request/Response Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  role?: 'user' | 'provider';
}

export interface AuthResponse {
  user: Omit<User, 'password'>;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface TokenPayload {
  id: string;
  userId: string;
  email: string;
  role: 'user' | 'admin' | 'provider';
  iat: number;
  exp: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordUpdateRequest {
  token: string;
  newPassword: string;
}

export interface EmailVerificationRequest {
  token: string;
}

// Additional auth types for controllers
export interface UpdateUserData {
  name?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
  profileImage?: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface VerifyEmailRequest {
  token: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// Google OAuth Types
export interface GoogleAuthResponse {
  email: string;
  name: string;
  picture?: string;
  emailVerified: boolean;
}

// Session Types
export interface UserSession extends BaseEntity {
  userId: string;
  refreshToken: string;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
}