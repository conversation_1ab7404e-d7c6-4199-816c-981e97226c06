// Authentication Types - Enhanced from frontend auth types
import { BaseEntity } from '@/types/common';

export interface User extends BaseEntity {
  email: string;
  name: string;
  role: 'user' | 'admin' | 'provider';
  password: string; // Hashed password
  emailVerified: boolean;
  emailVerificationToken?: string;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  lastLogin?: Date;
  isActive: boolean;
  profile?: UserProfile;
}

export interface UserProfile extends BaseEntity {
  userId: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  address?: Address;
  avatar?: string;
  preferences?: UserPreferences;
}

export interface Address {
  street?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  country?: string;
}

export interface UserPreferences {
  notifications: {
    email: boolean;
    sms: boolean;
    marketing: boolean;
  };
  theme: 'light' | 'dark';
  language: string;
}

// Authentication Request/Response Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
  role?: 'user' | 'provider';
}

export interface AuthResponse {
  user: Omit<User, 'password'>;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface TokenPayload {
  userId: string;
  email: string;
  role: 'user' | 'admin' | 'provider';
  iat: number;
  exp: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordUpdateRequest {
  token: string;
  newPassword: string;
}

export interface EmailVerificationRequest {
  token: string;
}

// Google OAuth Types
export interface GoogleAuthResponse {
  email: string;
  name: string;
  picture?: string;
  emailVerified: boolean;
}

// Session Types
export interface UserSession extends BaseEntity {
  userId: string;
  refreshToken: string;
  expiresAt: Date;
  ipAddress?: string;
  userAgent?: string;
  isActive: boolean;
}