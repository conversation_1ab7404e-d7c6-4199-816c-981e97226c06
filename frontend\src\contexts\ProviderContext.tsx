import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface Provider {
  id: string;
  businessName: string;
  logo: string;
  contactName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  coordinates: { lat: number; lng: number };
  website?: string;
  description: string;
  serviceAreas: string[]; // zip codes they serve
  googleReviewsId?: string;
  averageRating: number;
  reviewCount: number;
  operatingHours: OperatingHours;
  isVerified: boolean;
  isActive: boolean;
  joinedDate: string;
  services: ProviderService[];
  totalServicesOffered: number;
}

export interface ProviderService {
  serviceId: string;
  isActive: boolean;
  agreedToPrice: boolean;
  agreedDate: string;
  notes?: string;
}

export interface OperatingHours {
  monday: { open: string; close: string; closed: boolean };
  tuesday: { open: string; close: string; closed: boolean };
  wednesday: { open: string; close: string; closed: boolean };
  thursday: { open: string; close: string; closed: boolean };
  friday: { open: string; close: string; closed: boolean };
  saturday: { open: string; close: string; closed: boolean };
  sunday: { open: string; close: string; closed: boolean };
}

export interface CustomerProfile {
  id: string;
  name: string;
  email: string;
  phone?: string;
  addresses: CustomerAddress[];
  defaultAddressId?: string;
  orderHistory: string[]; // order IDs
  preferences: CustomerPreferences;
  joinedDate: string;
}

export interface CustomerAddress {
  id: string;
  label: string; // "Home", "Office", etc.
  street: string;
  city: string;
  state: string;
  zipCode: string;
  coordinates: { lat: number; lng: number };
  isDefault: boolean;
}

export interface CustomerPreferences {
  preferredProviders: string[];
  communicationMethod: 'email' | 'phone' | 'both';
  notifications: {
    orderUpdates: boolean;
    promotions: boolean;
    newProviders: boolean;
  };
}

interface ProviderContextType {
  providers: Provider[];
  customerProfile: CustomerProfile | null;
  addProvider: (provider: Omit<Provider, 'id' | 'joinedDate' | 'totalServicesOffered'>) => void;
  updateProvider: (id: string, provider: Partial<Provider>) => void;
  deleteProvider: (id: string) => void;
  updateProviderService: (providerId: string, serviceId: string, updates: Partial<ProviderService>) => void;
  getProvidersByLocation: (zipCode: string, radius?: number) => Provider[];
  getProvidersByService: (serviceId: string) => Provider[];
  updateCustomerProfile: (profile: Partial<CustomerProfile>) => void;
  addCustomerAddress: (address: Omit<CustomerAddress, 'id'>) => void;
  updateCustomerAddress: (addressId: string, address: Partial<CustomerAddress>) => void;
  deleteCustomerAddress: (addressId: string) => void;
}

const ProviderContext = createContext<ProviderContextType | undefined>(undefined);

export const useProviders = () => {
  const context = useContext(ProviderContext);
  if (context === undefined) {
    throw new Error('useProviders must be used within a ProviderProvider');
  }
  return context;
};

interface ProviderProviderProps {
  children: ReactNode;
}

export const ProviderProvider: React.FC<ProviderProviderProps> = ({ children }) => {
  const [providers, setProviders] = useState<Provider[]>([
    {
      id: '1',
      businessName: 'QuickPrint Solutions',
      logo: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      contactName: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Print Street',
      city: 'New York',
      state: 'NY',
      zipCode: '10001',
      coordinates: { lat: 40.7128, lng: -74.0060 },
      website: 'https://quickprint.com',
      description: 'Full-service commercial printing with 15+ years of experience. Specializing in business cards, marketing materials, and large format printing.',
      serviceAreas: ['10001', '10002', '10003', '10004', '10005'],
      averageRating: 4.8,
      reviewCount: 127,
      operatingHours: {
        monday: { open: '08:00', close: '18:00', closed: false },
        tuesday: { open: '08:00', close: '18:00', closed: false },
        wednesday: { open: '08:00', close: '18:00', closed: false },
        thursday: { open: '08:00', close: '18:00', closed: false },
        friday: { open: '08:00', close: '18:00', closed: false },
        saturday: { open: '09:00', close: '15:00', closed: false },
        sunday: { open: '00:00', close: '00:00', closed: true }
      },
      isVerified: true,
      isActive: true,
      joinedDate: '2024-01-15',
      services: [
        { serviceId: '1', isActive: true, agreedToPrice: true, agreedDate: '2024-01-15' },
        { serviceId: '2', isActive: true, agreedToPrice: true, agreedDate: '2024-01-15' },
        { serviceId: '3', isActive: true, agreedToPrice: true, agreedDate: '2024-01-15' },
        { serviceId: '4', isActive: true, agreedToPrice: true, agreedDate: '2024-01-15' },
        { serviceId: '5', isActive: true, agreedToPrice: true, agreedDate: '2024-01-15' }
      ],
      totalServicesOffered: 5
    },
    {
      id: '2',
      businessName: 'Premium Print Co',
      logo: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      contactName: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      address: '456 Design Avenue',
      city: 'New York',
      state: 'NY',
      zipCode: '10002',
      coordinates: { lat: 40.7200, lng: -74.0100 },
      description: 'High-end printing services focusing on premium materials and finishes. Perfect for luxury brands and special occasions.',
      serviceAreas: ['10001', '10002', '10003', '10010', '10011'],
      averageRating: 4.9,
      reviewCount: 89,
      operatingHours: {
        monday: { open: '09:00', close: '17:00', closed: false },
        tuesday: { open: '09:00', close: '17:00', closed: false },
        wednesday: { open: '09:00', close: '17:00', closed: false },
        thursday: { open: '09:00', close: '17:00', closed: false },
        friday: { open: '09:00', close: '17:00', closed: false },
        saturday: { open: '00:00', close: '00:00', closed: true },
        sunday: { open: '00:00', close: '00:00', closed: true }
      },
      isVerified: true,
      isActive: true,
      joinedDate: '2024-02-01',
      services: [
        { serviceId: '1', isActive: true, agreedToPrice: true, agreedDate: '2024-02-01' },
        { serviceId: '6', isActive: true, agreedToPrice: true, agreedDate: '2024-02-01' },
        { serviceId: '9', isActive: true, agreedToPrice: true, agreedDate: '2024-02-01' }
      ],
      totalServicesOffered: 3
    }
  ]);

  const [customerProfile, setCustomerProfile] = useState<CustomerProfile | null>({
    id: 'customer-1',
    name: 'Demo Customer',
    email: '<EMAIL>',
    phone: '+****************',
    addresses: [
      {
        id: 'addr-1',
        label: 'Home',
        street: '789 Customer Lane',
        city: 'New York',
        state: 'NY',
        zipCode: '10001',
        coordinates: { lat: 40.7150, lng: -74.0080 },
        isDefault: true
      }
    ],
    defaultAddressId: 'addr-1',
    orderHistory: [],
    preferences: {
      preferredProviders: [],
      communicationMethod: 'email',
      notifications: {
        orderUpdates: true,
        promotions: false,
        newProviders: true
      }
    },
    joinedDate: '2024-12-01'
  });

  const addProvider = (provider: Omit<Provider, 'id' | 'joinedDate' | 'totalServicesOffered'>) => {
    const newProvider: Provider = {
      ...provider,
      id: Date.now().toString(),
      joinedDate: new Date().toISOString().split('T')[0],
      totalServicesOffered: provider.services.filter(s => s.isActive).length
    };
    setProviders(prev => [...prev, newProvider]);
  };

  const updateProvider = (id: string, updatedProvider: Partial<Provider>) => {
    setProviders(prev =>
      prev.map(provider => {
        if (provider.id === id) {
          const updated = { ...provider, ...updatedProvider };
          if (updatedProvider.services) {
            updated.totalServicesOffered = updatedProvider.services.filter(s => s.isActive).length;
          }
          return updated;
        }
        return provider;
      })
    );
  };

  const deleteProvider = (id: string) => {
    setProviders(prev => prev.filter(provider => provider.id !== id));
  };

  const updateProviderService = (providerId: string, serviceId: string, updates: Partial<ProviderService>) => {
    setProviders(prev =>
      prev.map(provider => {
        if (provider.id === providerId) {
          const updatedServices = provider.services.map(service =>
            service.serviceId === serviceId ? { ...service, ...updates } : service
          );
          
          // If service doesn't exist, add it
          if (!provider.services.find(s => s.serviceId === serviceId)) {
            updatedServices.push({
              serviceId,
              isActive: false,
              agreedToPrice: false,
              agreedDate: new Date().toISOString().split('T')[0],
              ...updates
            });
          }

          return {
            ...provider,
            services: updatedServices,
            totalServicesOffered: updatedServices.filter(s => s.isActive).length
          };
        }
        return provider;
      })
    );
  };

  const getProvidersByLocation = (zipCode: string, radius: number = 10) => {
    return providers.filter(provider => 
      provider.isActive && 
      provider.isVerified && 
      provider.serviceAreas.includes(zipCode)
    );
  };

  const getProvidersByService = (serviceId: string) => {
    return providers.filter(provider =>
      provider.isActive &&
      provider.isVerified &&
      provider.services.some(service => 
        service.serviceId === serviceId && 
        service.isActive && 
        service.agreedToPrice
      )
    );
  };

  const updateCustomerProfile = (profile: Partial<CustomerProfile>) => {
    setCustomerProfile(prev => prev ? { ...prev, ...profile } : null);
  };

  const addCustomerAddress = (address: Omit<CustomerAddress, 'id'>) => {
    if (!customerProfile) return;
    
    const newAddress: CustomerAddress = {
      ...address,
      id: Date.now().toString()
    };

    setCustomerProfile(prev => prev ? {
      ...prev,
      addresses: [...prev.addresses, newAddress]
    } : null);
  };

  const updateCustomerAddress = (addressId: string, address: Partial<CustomerAddress>) => {
    if (!customerProfile) return;

    setCustomerProfile(prev => prev ? {
      ...prev,
      addresses: prev.addresses.map(addr =>
        addr.id === addressId ? { ...addr, ...address } : addr
      )
    } : null);
  };

  const deleteCustomerAddress = (addressId: string) => {
    if (!customerProfile) return;

    setCustomerProfile(prev => prev ? {
      ...prev,
      addresses: prev.addresses.filter(addr => addr.id !== addressId)
    } : null);
  };

  const value = {
    providers,
    customerProfile,
    addProvider,
    updateProvider,
    deleteProvider,
    updateProviderService,
    getProvidersByLocation,
    getProvidersByService,
    updateCustomerProfile,
    addCustomerAddress,
    updateCustomerAddress,
    deleteCustomerAddress
  };

  return (
    <ProviderContext.Provider value={value}>
      {children}
    </ProviderContext.Provider>
  );
};