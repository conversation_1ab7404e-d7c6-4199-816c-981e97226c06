// Email Service - Following SRP and DIP principles
import nodemailer from 'nodemailer';
import { IEmailService } from '@/services/auth/auth.service';

// Email configuration interface (ISP)
interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  auth: {
    user: string;
    pass: string;
  };
}

// Email template interface (ISP)
interface EmailTemplate {
  subject: string;
  html: string;
  text?: string;
}

// Email service implementation (SRP)
export class EmailService implements IEmailService {
  private transporter: nodemailer.Transporter;
  private fromEmail: string;
  private fromName: string;

  constructor() {
    this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
    this.fromName = process.env.FROM_NAME || 'PrintWeditt';
    
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });
  }

  // Send verification email (SRP)
  async sendVerificationEmail(email: string, token: string): Promise<void> {
    const verificationUrl = `${process.env.FRONTEND_URL}/verify-email?token=${token}`;
    
    const template = this.getVerificationEmailTemplate(verificationUrl);
    
    await this.sendEmail(email, template);
  }

  // Send password reset email (SRP)
  async sendPasswordResetEmail(email: string, token: string): Promise<void> {
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password?token=${token}`;
    
    const template = this.getPasswordResetTemplate(resetUrl);
    
    await this.sendEmail(email, template);
  }

  // Send welcome email (SRP)
  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    const template = this.getWelcomeEmailTemplate(name);
    
    await this.sendEmail(email, template);
  }

  // Send order notification email (SRP)
  async sendOrderNotificationEmail(
    email: string, 
    orderData: {
      orderNumber: string;
      serviceName: string;
      providerName: string;
      status: string;
    }
  ): Promise<void> {
    const template = this.getOrderNotificationTemplate(orderData);
    
    await this.sendEmail(email, template);
  }

  // Send provider application email (SRP)
  async sendProviderApplicationEmail(email: string, name: string): Promise<void> {
    const template = this.getProviderApplicationTemplate(name);
    
    await this.sendEmail(email, template);
  }

  // Generic email sending method (DRY principle)
  private async sendEmail(to: string, template: EmailTemplate): Promise<void> {
    try {
      const mailOptions = {
        from: `"${this.fromName}" <${this.fromEmail}>`,
        to,
        subject: template.subject,
        html: template.html,
        text: template.text,
      };

      const info = await this.transporter.sendMail(mailOptions);
      console.log('Email sent:', info.messageId);
    } catch (error) {
      console.error('Email sending failed:', error);
      throw new Error('Failed to send email');
    }
  }

  // Email templates (SRP for each template)
  private getVerificationEmailTemplate(verificationUrl: string): EmailTemplate {
    return {
      subject: 'Verify Your Email - PrintWeditt',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Verify Your Email</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ff6b35;">PrintWeditt</h1>
          </div>
          
          <h2>Welcome to PrintWeditt!</h2>
          
          <p>Thank you for joining our printing services platform. To complete your registration, please verify your email address by clicking the button below:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" 
               style="background-color: #ff6b35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
              Verify Email Address
            </a>
          </div>
          
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${verificationUrl}</p>
          
          <p style="margin-top: 30px; font-size: 14px; color: #666;">
            This verification link will expire in 24 hours. If you didn't create an account with PrintWeditt, please ignore this email.
          </p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          
          <p style="font-size: 12px; color: #999; text-align: center;">
            © ${new Date().getFullYear()} PrintWeditt. All rights reserved.
          </p>
        </body>
        </html>
      `,
      text: `Welcome to PrintWeditt! Please verify your email by visiting: ${verificationUrl}`,
    };
  }

  private getPasswordResetTemplate(resetUrl: string): EmailTemplate {
    return {
      subject: 'Reset Your Password - PrintWeditt',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ff6b35;">PrintWeditt</h1>
          </div>
          
          <h2>Password Reset Request</h2>
          
          <p>We received a request to reset your password. Click the button below to create a new password:</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${resetUrl}" 
               style="background-color: #ff6b35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
              Reset Password
            </a>
          </div>
          
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all; color: #666;">${resetUrl}</p>
          
          <p style="margin-top: 30px; font-size: 14px; color: #666;">
            This reset link will expire in 1 hour. If you didn't request this password reset, please ignore this email.
          </p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          
          <p style="font-size: 12px; color: #999; text-align: center;">
            © ${new Date().getFullYear()} PrintWeditt. All rights reserved.
          </p>
        </body>
        </html>
      `,
      text: `Reset your password by visiting: ${resetUrl}`,
    };
  }

  private getWelcomeEmailTemplate(name: string): EmailTemplate {
    return {
      subject: 'Welcome to PrintWeditt!',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to PrintWeditt</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ff6b35;">PrintWeditt</h1>
          </div>
          
          <h2>Welcome, ${name}!</h2>
          
          <p>Your email has been verified successfully. You're now ready to explore our printing services platform!</p>
          
          <h3>What you can do:</h3>
          <ul>
            <li>Browse thousands of printing services</li>
            <li>Connect with local printing providers</li>
            <li>Get custom quotes for your projects</li>
            <li>Track your orders from start to finish</li>
          </ul>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/services" 
               style="background-color: #ff6b35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
              Explore Services
            </a>
          </div>
          
          <p>If you have any questions, our support team is here to help!</p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          
          <p style="font-size: 12px; color: #999; text-align: center;">
            © ${new Date().getFullYear()} PrintWeditt. All rights reserved.
          </p>
        </body>
        </html>
      `,
      text: `Welcome to PrintWeditt, ${name}! Your email has been verified. Visit ${process.env.FRONTEND_URL}/services to get started.`,
    };
  }

  private getOrderNotificationTemplate(orderData: {
    orderNumber: string;
    serviceName: string;
    providerName: string;
    status: string;
  }): EmailTemplate {
    const statusMessages: Record<string, string> = {
      'pending': 'Your order has been received and is awaiting provider confirmation.',
      'confirmed': 'Your order has been confirmed by the provider.',
      'in_progress': 'Your order is currently being processed.',
      'completed': 'Your order has been completed!',
      'cancelled': 'Your order has been cancelled.',
    };

    return {
      subject: `Order Update - ${orderData.orderNumber}`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Order Update</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ff6b35;">PrintWeditt</h1>
          </div>
          
          <h2>Order Update</h2>
          
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="margin-top: 0;">Order #${orderData.orderNumber}</h3>
            <p><strong>Service:</strong> ${orderData.serviceName}</p>
            <p><strong>Provider:</strong> ${orderData.providerName}</p>
            <p><strong>Status:</strong> <span style="color: #ff6b35;">${orderData.status.replace('_', ' ').toUpperCase()}</span></p>
          </div>
          
          <p>${statusMessages[orderData.status] || 'Your order status has been updated.'}</p>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/dashboard" 
               style="background-color: #ff6b35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
              View Order Details
            </a>
          </div>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          
          <p style="font-size: 12px; color: #999; text-align: center;">
            © ${new Date().getFullYear()} PrintWeditt. All rights reserved.
          </p>
        </body>
        </html>
      `,
      text: `Order #${orderData.orderNumber} - ${orderData.serviceName} by ${orderData.providerName} is now ${orderData.status}`,
    };
  }

  private getProviderApplicationTemplate(name: string): EmailTemplate {
    return {
      subject: 'Provider Application Received - PrintWeditt',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Provider Application</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #ff6b35;">PrintWeditt</h1>
          </div>
          
          <h2>Thank you for your application, ${name}!</h2>
          
          <p>We've received your application to become a printing service provider on PrintWeditt. Our team will review your information and get back to you within 2-3 business days.</p>
          
          <h3>What happens next:</h3>
          <ol>
            <li>Our team reviews your application</li>
            <li>We may contact you for additional information</li>
            <li>Once approved, you'll receive access to your provider dashboard</li>
            <li>You can start listing your services and connecting with customers</li>
          </ol>
          
          <div style="text-align: center; margin: 30px 0;">
            <a href="${process.env.FRONTEND_URL}/provider-dashboard" 
               style="background-color: #ff6b35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
              Provider Dashboard
            </a>
          </div>
          
          <p>If you have any questions about your application, please don't hesitate to contact our support team.</p>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          
          <p style="font-size: 12px; color: #999; text-align: center;">
            © ${new Date().getFullYear()} PrintWeditt. All rights reserved.
          </p>
        </body>
        </html>
      `,
      text: `Thank you for your provider application, ${name}! We'll review it within 2-3 business days.`,
    };
  }
}