import {Request, Response, NextFunction} from 'express';
import {
	AppError,
	ValidationError,
	NotFoundError,
	UnauthorizedError,
	ForbiddenError,
	ConflictError,
} from '@/types/common.types';

export class ErrorMiddleware {
	static handleError(
		error: any,
		req: Request,
		res: Response,
		next: NextFunction
	): void {
		console.error('Error occurred:', {
			error: error.message,
			stack: error.stack,
			url: req.url,
			method: req.method,
			ip: req.ip,
			userAgent: req.get('User-Agent'),
		});

		// Handle Zod validation errors
		if (error.name === 'ZodError') {
			res.status(400).json({
				success: false,
				message: 'Validation error',
				errors: error.errors.map(
					(err: any) => `${err.path.join('.')}: ${err.message}`
				),
			});
			return;
		}

		// Handle Prisma errors
		if (error.code === 'P2002') {
			res.status(409).json({
				success: false,
				message: 'Resource already exists',
			});
			return;
		}

		if (error.code === 'P2025') {
			res.status(404).json({
				success: false,
				message: 'Resource not found',
			});
			return;
		}

		// Handle custom application errors
		if (error instanceof AppError) {
			res.status(error.statusCode).json({
				success: false,
				message: error.message,
			});
			return;
		}

		// Handle JWT errors
		if (error.name === 'JsonWebTokenError') {
			res.status(401).json({
				success: false,
				message: 'Invalid token',
			});
			return;
		}

		if (error.name === 'TokenExpiredError') {
			res.status(401).json({
				success: false,
				message: 'Token expired',
			});
			return;
		}

		// Handle other known errors
		if (error.name === 'CastError') {
			res.status(400).json({
				success: false,
				message: 'Invalid ID format',
			});
			return;
		}

		if (error.name === 'ValidationError') {
			res.status(400).json({
				success: false,
				message: 'Validation error',
				errors: Object.values(error.errors).map((err: any) => err.message),
			});
			return;
		}

		// Default error response
		res.status(500).json({
			success: false,
			message:
				process.env.NODE_ENV === 'production'
					? 'Internal server error'
					: error.message,
		});
	}

	static notFound(req: Request, res: Response, next: NextFunction): void {
		res.status(404).json({
			success: false,
			message: `Route ${req.originalUrl} not found`,
		});
	}

	static asyncHandler(fn: Function) {
		return (req: Request, res: Response, next: NextFunction) => {
			Promise.resolve(fn(req, res, next)).catch(next);
		};
	}
}
