// Session Repository - Following SRP and ISP principles
import { PrismaClient, Session } from '@prisma/client';
import { BaseRepository } from '@/repositories/base/base.repository';
import { CreateSessionData, UpdateSessionData } from '@/types/auth';

// Session-specific repository interface (ISP)
export interface ISessionRepository {
  findByAccessToken(token: string): Promise<Session | null>;
  findByRefreshToken(token: string): Promise<Session | null>;
  findByUserId(userId: string): Promise<Session[]>;
  createSession(sessionData: CreateSessionData): Promise<Session>;
  updateSession(id: string, sessionData: UpdateSessionData): Promise<Session>;
  deactivateSession(id: string): Promise<Session>;
  deactivateAllUserSessions(userId: string): Promise<void>;
  deactivateUserSessionsExcept(userId: string, currentSessionId: string): Promise<void>;
  deleteExpiredSessions(): Promise<number>;
  getActiveSessionsCount(userId: string): Promise<number>;
  findUserActiveSessions(userId: string): Promise<Session[]>;
}

// Session repository implementation (SRP)
export class SessionRepository extends BaseRepository<Session> implements ISessionRepository {
  constructor(prisma: PrismaClient) {
    super(prisma, 'session');
  }

  async findByAccessToken(token: string): Promise<Session | null> {
    try {
      return await this.prisma.session.findUnique({
        where: { 
          accessToken: token,
          isActive: true,
          expiresAt: {
            gt: new Date(), // Token must not be expired
          },
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
              isActive: true,
              isEmailVerified: true,
            },
          },
        },
      });
    } catch (error) {
      throw new Error(`Failed to find session by access token: ${error}`);
    }
  }

  async findByRefreshToken(token: string): Promise<Session | null> {
    try {
      return await this.prisma.session.findUnique({
        where: { 
          refreshToken: token,
          isActive: true,
        },
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              role: true,
              isActive: true,
              isEmailVerified: true,
            },
          },
        },
      });
    } catch (error) {
      throw new Error(`Failed to find session by refresh token: ${error}`);
    }
  }

  async findByUserId(userId: string): Promise<Session[]> {
    try {
      return await this.prisma.session.findMany({
        where: { 
          userId,
          isActive: true,
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find sessions by user ID: ${error}`);
    }
  }

  async createSession(sessionData: CreateSessionData): Promise<Session> {
    try {
      return await this.prisma.session.create({
        data: sessionData,
      });
    } catch (error) {
      throw new Error(`Failed to create session: ${error}`);
    }
  }

  async updateSession(id: string, sessionData: UpdateSessionData): Promise<Session> {
    try {
      return await this.prisma.session.update({
        where: { id },
        data: sessionData,
      });
    } catch (error) {
      throw new Error(`Failed to update session: ${error}`);
    }
  }

  async deactivateSession(id: string): Promise<Session> {
    try {
      return await this.prisma.session.update({
        where: { id },
        data: {
          isActive: false,
        },
      });
    } catch (error) {
      throw new Error(`Failed to deactivate session: ${error}`);
    }
  }

  async deactivateAllUserSessions(userId: string): Promise<void> {
    try {
      await this.prisma.session.updateMany({
        where: { userId },
        data: {
          isActive: false,
        },
      });
    } catch (error) {
      throw new Error(`Failed to deactivate all user sessions: ${error}`);
    }
  }

  async deactivateUserSessionsExcept(userId: string, currentSessionId: string): Promise<void> {
    try {
      await this.prisma.session.updateMany({
        where: { 
          userId,
          id: {
            not: currentSessionId,
          },
        },
        data: {
          isActive: false,
        },
      });
    } catch (error) {
      throw new Error(`Failed to deactivate user sessions except current: ${error}`);
    }
  }

  async deleteExpiredSessions(): Promise<number> {
    try {
      const result = await this.prisma.session.deleteMany({
        where: {
          OR: [
            {
              expiresAt: {
                lt: new Date(),
              },
            },
            {
              isActive: false,
              createdAt: {
                lt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days old
              },
            },
          ],
        },
      });
      return result.count;
    } catch (error) {
      throw new Error(`Failed to delete expired sessions: ${error}`);
    }
  }

  async getActiveSessionsCount(userId: string): Promise<number> {
    try {
      return await this.prisma.session.count({
        where: {
          userId,
          isActive: true,
          expiresAt: {
            gt: new Date(),
          },
        },
      });
    } catch (error) {
      throw new Error(`Failed to get active sessions count: ${error}`);
    }
  }

  async findUserActiveSessions(userId: string): Promise<Session[]> {
    try {
      return await this.prisma.session.findMany({
        where: {
          userId,
          isActive: true,
          expiresAt: {
            gt: new Date(),
          },
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find user active sessions: ${error}`);
    }
  }
}