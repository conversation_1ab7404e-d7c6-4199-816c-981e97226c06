// Barrel exports for constants
export * from './routes';
export * from './categories';

// Application constants
export const APP_CONFIG = {
  NAME: 'PrintWeditt',
  VERSION: '1.0.0',
  DESCRIPTION: 'Professional printing services platform',
  
  // Authentication
  JWT_EXPIRES_IN: '24h',
  REFRESH_TOKEN_EXPIRES_IN: '7d',
  EMAIL_VERIFICATION_EXPIRES_IN: '24h',
  PASSWORD_RESET_EXPIRES_IN: '1h',
  
  // File uploads
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_IMAGE_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  ALLOWED_DOCUMENT_TYPES: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  
  // Pagination
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  
  // Rating system
  MIN_RATING: 1,
  MAX_RATING: 5,
  
  // Business rules
  MIN_PASSWORD_LENGTH: 8,
  MAX_DESCRIPTION_LENGTH: 2000,
  MAX_REVIEW_LENGTH: 1000,
  
  // Provider subscription limits
  SUBSCRIPTION_LIMITS: {
    free: {
      maxServices: 5,
      maxImages: 10,
      featuredServices: 0,
      portfolioItems: 5,
    },
    basic: {
      maxServices: 25,
      maxImages: 50,
      featuredServices: 3,
      portfolioItems: 20,
    },
    premium: {
      maxServices: 100,
      maxImages: 200,
      featuredServices: 10,
      portfolioItems: 50,
    },
    enterprise: {
      maxServices: -1, // unlimited
      maxImages: -1, // unlimited
      featuredServices: -1, // unlimited
      portfolioItems: -1, // unlimited
    },
  },
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  VALIDATION_ERROR: 422,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  // Authentication
  INVALID_CREDENTIALS: 'Invalid email or password',
  EMAIL_ALREADY_EXISTS: 'Email already registered',
  EMAIL_NOT_VERIFIED: 'Please verify your email address',
  TOKEN_EXPIRED: 'Token has expired',
  TOKEN_INVALID: 'Invalid token',
  ACCESS_DENIED: 'Access denied',
  
  // Validation
  REQUIRED_FIELD: 'This field is required',
  INVALID_EMAIL: 'Invalid email format',
  INVALID_PASSWORD: 'Password must be at least 8 characters',
  PASSWORDS_DONT_MATCH: 'Passwords do not match',
  
  // Resources
  USER_NOT_FOUND: 'User not found',
  SERVICE_NOT_FOUND: 'Service not found',
  PROVIDER_NOT_FOUND: 'Provider not found',
  ORDER_NOT_FOUND: 'Order not found',
  
  // File uploads
  FILE_TOO_LARGE: 'File size exceeds maximum limit',
  INVALID_FILE_TYPE: 'Invalid file type',
  UPLOAD_FAILED: 'File upload failed',
  
  // Generic
  INTERNAL_ERROR: 'Internal server error',
  INVALID_REQUEST: 'Invalid request',
  RESOURCE_NOT_FOUND: 'Resource not found',
} as const;