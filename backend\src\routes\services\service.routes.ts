// Service Routes - Following REST principles and proper middleware organization
import { Router } from 'express';
import { ServiceController } from '@/controllers/services/service.controller';
import { ValidationChains } from '@/middleware/validation';
import { handleValidation } from '@/middleware/validation/validator';
import { authenticate, adminOnly, providerOrAdmin, optionalAuth } from '@/middleware/auth';
import { validatePagination } from '@/middleware/validation';

const router = Router();

// Initialize controller
const serviceController = new ServiceController();

// GET /api/v1/services - Get all services with search and filters (Public)
router.get(
  '/',
  optionalAuth, // Optional auth to get personalized results
  validatePagination,
  serviceController.getServices.bind(serviceController)
);

// GET /api/v1/services/featured - Get featured services (Public)
router.get(
  '/featured',
  serviceController.getFeaturedServices.bind(serviceController)
);

// GET /api/v1/services/stats - Get service statistics (Admin only)
router.get(
  '/stats',
  authenticate,
  adminOnly,
  serviceController.getServiceStats.bind(serviceController)
);

// GET /api/v1/services/categories/:categoryId - Get services by category (Public)
router.get(
  '/categories/:categoryId',
  validatePagination,
  serviceController.getServicesByCategory.bind(serviceController)
);

// GET /api/v1/services/:id - Get service by ID (Public)
router.get(
  '/:id',
  optionalAuth, // Optional auth to get additional details if owner/admin
  serviceController.getServiceById.bind(serviceController)
);

// POST /api/v1/services - Create new service (Provider/Admin only)
router.post(
  '/',
  authenticate,
  providerOrAdmin,
  ValidationChains.createService(),
  handleValidation,
  serviceController.createService.bind(serviceController)
);

// PUT /api/v1/services/:id - Update service (Owner/Admin only)
router.put(
  '/:id',
  authenticate,
  ValidationChains.updateService(),
  handleValidation,
  serviceController.updateService.bind(serviceController)
);

// DELETE /api/v1/services/:id - Delete service (Owner/Admin only)
router.delete(
  '/:id',
  authenticate,
  serviceController.deleteService.bind(serviceController)
);

// PUT /api/v1/services/:id/featured - Set service featured status (Admin only)
router.put(
  '/:id/featured',
  authenticate,
  adminOnly,
  ValidationChains.setFeatured(),
  handleValidation,
  serviceController.setServiceFeatured.bind(serviceController)
);

export default router;