export { BaseModel, DomainEventPublisher, Result, ValueObject } from './base/BaseModel';
export * from './schemas/validation.schemas';
export { UserModel, Email, Password, UserProfile } from './auth/User.model';
export { SessionModel, SessionToken, SessionContext } from './auth/Session.model';
export { AdminProfileModel, AdminPermissions, AdminDepartment } from './auth/AdminProfile.model';
export { ServiceModel, ServicePrice, ServiceRating, ServiceMetadata } from './services/Service.model';
export { ProviderProfileModel, BusinessInfo, BusinessAddress, VerificationInfo, ProviderRating } from './providers/ProviderProfile.model';
export { OrderModel, OrderNumber, OrderAmount, OrderTimeline, OrderFiles } from './orders/Order.model';
export declare class ModelFactory {
    static createUser: any;
    static createSession: any;
    static createAdminProfile: any;
    static createService: any;
    static createProviderProfile: any;
    static createOrder: any;
    static fromPrismaUser: any;
    static fromPrismaSession: any;
    static fromPrismaAdminProfile: any;
    static fromPrismaService: any;
    static fromPrismaProviderProfile: any;
    static fromPrismaOrder: any;
}
export interface DomainEvent {
    eventType: string;
    aggregateId: string;
    data: Record<string, any>;
    timestamp: Date;
    version: number;
}
export type ModelResult<T> = Result<T, string>;
export interface ModelValidation {
    isValid: boolean;
    errors: string[];
}
export type { UserModel, SessionModel, AdminProfileModel, ServiceModel, ProviderProfileModel, OrderModel, };
//# sourceMappingURL=index.d.ts.map