# Backend Architecture Guide

## Overview

This document provides a comprehensive guide to the PrintWeditt backend architecture, demonstrating how SOLID principles and clean architecture patterns are implemented throughout the system.

## Architecture Layers

### 1. Presentation Layer (Controllers)

**Purpose**: Handle HTTP requests and responses, input validation, and route management.

**SOLID Principles Applied**:
- **SRP**: Each controller handles one domain (auth, services, providers)
- **OCP**: New endpoints can be added without modifying existing controllers
- **DIP**: Controllers depend on service interfaces, not implementations

```typescript
// Example: AuthController
class AuthController {
  constructor(private authService: IAuthService) {} // DIP
  
  async login(req: Request, res: Response) {
    // Single responsibility: handle HTTP login request
    const result = await this.authService.login(req.body);
    return res.json(ResponseFormatter.success('Login successful', result));
  }
}
```

### 2. Business Logic Layer (Services)

**Purpose**: Implement core business rules, orchestrate operations, and maintain data consistency.

**SOLID Principles Applied**:
- **SRP**: Each service handles one business domain
- **OCP**: Services can be extended with new features without modification
- **LSP**: All services implement consistent interfaces
- **ISP**: Services use focused repository interfaces
- **DIP**: Services depend on repository abstractions

```typescript
// Example: Service with proper dependency injection
class AuthService {
  constructor(
    private userRepository: IUserRepository,     // DIP
    private sessionRepository: ISessionRepository, // DIP
    private emailService: IEmailService          // DIP
  ) {}
  
  async register(request: RegisterRequest): Promise<BusinessLogicResult<AuthResponse>> {
    // SRP: Single responsibility for user registration logic
    // Business logic implementation here
  }
}
```

### 3. Data Access Layer (Repositories)

**Purpose**: Abstract database operations and provide a clean interface for data access.

**SOLID Principles Applied**:
- **SRP**: Each repository handles one entity type
- **OCP**: New query methods can be added without changing existing ones
- **LSP**: All repositories follow consistent CRUD interfaces
- **ISP**: Focused interfaces for specific operations

```typescript
// Example: Repository interface following ISP
interface IUserRepository {
  findByEmail(email: string): Promise<User | null>;
  findById(id: string): Promise<User | null>;
  create(userData: CreateUserData): Promise<User>;
  update(id: string, userData: UpdateUserData): Promise<User>;
  delete(id: string): Promise<void>;
}
```

### 4. Cross-Cutting Concerns (Middleware & Utilities)

**Purpose**: Handle authentication, validation, error handling, and common utilities.

**SOLID Principles Applied**:
- **SRP**: Each middleware has one specific purpose
- **OCP**: New middleware can be added without modifying existing ones
- **DIP**: Middleware depends on service abstractions

## Dependency Injection Pattern

### Interface Definitions

```typescript
// Define interfaces for all dependencies (DIP)
export interface IUserRepository {
  // User data operations
}

export interface IEmailService {
  // Email operations
}

export interface IFileUploadService {
  // File operations
}
```

### Service Implementation

```typescript
// Services depend on interfaces, not concrete implementations
export class AuthService {
  constructor(
    private userRepository: IUserRepository,
    private emailService: IEmailService
  ) {}
  
  // Business logic implementation
}
```

### Dependency Container (Future Implementation)

```typescript
// Example of how dependency injection would be configured
export class DIContainer {
  private static instance: DIContainer;
  private dependencies = new Map();
  
  register<T>(key: string, implementation: T): void {
    this.dependencies.set(key, implementation);
  }
  
  resolve<T>(key: string): T {
    return this.dependencies.get(key);
  }
}
```

## Error Handling Architecture

### Centralized Error Management

```typescript
// Custom error hierarchy following SRP
export class AppError extends Error {
  constructor(
    public message: string,
    public statusCode: number,
    public isOperational: boolean = true
  ) {
    super(message);
  }
}

// Specific error types (ISP)
export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 422);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string) {
    super(message, 401);
  }
}
```

### Global Error Handler

```typescript
// Centralized error handling (SRP)
export const globalErrorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Error logging
  ErrorLogger.log(error, req);
  
  // Handle different error types
  if (error instanceof AppError) {
    // Handle operational errors
  } else {
    // Handle programming errors
  }
};
```

## Validation Architecture

### Modular Validation Rules

```typescript
// Reusable validation rules (DRY principle)
export const ValidationRules = {
  email: () => body('email').isEmail().normalizeEmail(),
  password: () => body('password').isLength({ min: 8 }),
  name: () => body('name').trim().isLength({ min: 2, max: 100 }),
};

// Composite validation chains (OCP)
export const ValidationChains = {
  register: (): ValidationChain[] => [
    ValidationRules.name(),
    ValidationRules.email(),
    ValidationRules.password(),
  ],
  
  login: (): ValidationChain[] => [
    ValidationRules.email(),
    ValidationRules.password(),
  ],
};
```

## Security Architecture

### Authentication Middleware

```typescript
// Authentication middleware (SRP)
export const authenticate = (req: Request, res: Response, next: NextFunction): void => {
  try {
    const token = TokenService.extractTokenFromHeader(req.headers.authorization);
    const decoded = TokenService.verifyAccessToken(token);
    req.user = decoded;
    next();
  } catch (error) {
    // Handle authentication errors
  }
};

// Authorization middleware factory (OCP)
export const authorize = (roles: string[] = []) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user || !roles.includes(req.user.role)) {
      // Handle authorization errors
    }
    next();
  };
};
```

### Cryptographic Services

```typescript
// Separate cryptographic concerns (SRP)
export class PasswordService {
  static async hash(password: string): Promise<string> {
    return bcrypt.hash(password, 12);
  }
  
  static async compare(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash);
  }
}

export class TokenService {
  static generateAccessToken(payload: TokenPayload): string {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: '24h' });
  }
  
  static verifyAccessToken(token: string): TokenPayload {
    return jwt.verify(token, JWT_SECRET) as TokenPayload;
  }
}
```

## Type System Architecture

### Domain-Driven Types

```typescript
// Base entity interface (LSP)
export interface BaseEntity {
  id: string;
  createdAt: Date;
  updatedAt: Date;
}

// Domain-specific types extending base (LSP)
export interface User extends BaseEntity {
  email: string;
  name: string;
  role: 'user' | 'admin' | 'provider';
  // ... other user properties
}

export interface Service extends BaseEntity {
  name: string;
  description: string;
  category: string;
  // ... other service properties
}
```

### API Response Types

```typescript
// Consistent API response format (LSP)
export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
  timestamp: string;
}

// Specialized response types (ISP)
export interface PaginatedResult<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
```

## Business Logic Patterns

### Result Pattern

```typescript
// Consistent result handling (LSP)
export interface BusinessLogicResult<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  validationErrors?: Record<string, string>;
}

// Service method example
async createService(request: CreateServiceRequest): Promise<BusinessLogicResult<Service>> {
  try {
    // Business logic implementation
    return {
      success: true,
      data: createdService,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
}
```

### Repository Pattern

```typescript
// Abstract repository interface (DIP)
interface IRepository<T> {
  findById(id: string): Promise<T | null>;
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  update(id: string, data: Partial<T>): Promise<T>;
  delete(id: string): Promise<void>;
}

// Specific repository implementation
export class UserRepository implements IUserRepository {
  async findByEmail(email: string): Promise<User | null> {
    // Database implementation
  }
  
  async create(userData: CreateUserData): Promise<User> {
    // Database implementation
  }
}
```

## Configuration Management

### Environment-Based Configuration

```typescript
// Configuration service (SRP)
export class ConfigService {
  static get database() {
    return {
      url: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production',
    };
  }
  
  static get jwt() {
    return {
      secret: process.env.JWT_SECRET,
      expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    };
  }
  
  static get email() {
    return {
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    };
  }
}
```

## Testing Architecture

### Test Structure

```typescript
// Service testing with mocked dependencies (DIP)
describe('AuthService', () => {
  let authService: AuthService;
  let mockUserRepository: jest.Mocked<IUserRepository>;
  let mockEmailService: jest.Mocked<IEmailService>;
  
  beforeEach(() => {
    mockUserRepository = createMockUserRepository();
    mockEmailService = createMockEmailService();
    authService = new AuthService(mockUserRepository, mockEmailService);
  });
  
  it('should register a new user successfully', async () => {
    // Test implementation
  });
});
```

### Mock Factories

```typescript
// Mock factory following the same interfaces (LSP)
export const createMockUserRepository = (): jest.Mocked<IUserRepository> => ({
  findByEmail: jest.fn(),
  findById: jest.fn(),
  create: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
});
```

## Performance Considerations

### Caching Strategy

```typescript
// Cache service interface (DIP)
interface ICacheService {
  get<T>(key: string): Promise<T | null>;
  set<T>(key: string, value: T, ttl?: number): Promise<void>;
  delete(key: string): Promise<void>;
}

// Service with caching
export class ServiceWithCache {
  constructor(
    private repository: IRepository,
    private cacheService: ICacheService
  ) {}
  
  async findById(id: string): Promise<Entity | null> {
    // Try cache first
    const cached = await this.cacheService.get<Entity>(`entity:${id}`);
    if (cached) return cached;
    
    // Fallback to repository
    const entity = await this.repository.findById(id);
    if (entity) {
      await this.cacheService.set(`entity:${id}`, entity, 3600); // 1 hour TTL
    }
    
    return entity;
  }
}
```

## Monitoring and Observability

### Structured Logging

```typescript
// Logger service (SRP)
export class Logger {
  static info(message: string, meta?: Record<string, any>): void {
    console.log(JSON.stringify({
      level: 'info',
      message,
      timestamp: new Date().toISOString(),
      ...meta,
    }));
  }
  
  static error(message: string, error?: Error, meta?: Record<string, any>): void {
    console.error(JSON.stringify({
      level: 'error',
      message,
      error: error?.message,
      stack: error?.stack,
      timestamp: new Date().toISOString(),
      ...meta,
    }));
  }
}
```

### Health Checks

```typescript
// Health check service (SRP)
export class HealthCheckService {
  constructor(
    private databaseService: IDatabaseService,
    private cacheService: ICacheService
  ) {}
  
  async getHealthStatus(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkCache(),
      this.checkExternalServices(),
    ]);
    
    return {
      status: checks.every(check => check.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      checks: {
        database: checks[0],
        cache: checks[1],
        external: checks[2],
      },
      timestamp: new Date().toISOString(),
    };
  }
}
```

## Conclusion

This architecture demonstrates how SOLID principles and clean architecture patterns create a maintainable, scalable, and testable backend system. Each layer has clear responsibilities, dependencies flow inward through abstractions, and the system is designed for extension without modification.

The architecture provides:

1. **Maintainability**: Clear separation of concerns and single responsibilities
2. **Testability**: Dependency injection enables easy mocking and unit testing
3. **Scalability**: Modular design allows for easy scaling of individual components
4. **Reliability**: Comprehensive error handling and validation
5. **Security**: Multiple layers of security controls and validation
6. **Performance**: Optimized data access patterns and caching strategies

This foundation supports the growth and evolution of the PrintWeditt platform while maintaining high code quality and developer productivity.