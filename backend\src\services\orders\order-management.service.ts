// Order Management Service - Following SOLID principles and established patterns
import {
	CreateOrderRequest,
	UpdateOrderRequest,
	OrderSearchRequest,
	OrderSearchResult,
	OrderWithMetrics,
	OrderMetrics,
	OrderStatistics,
	OrderStatusUpdateRequest,
	PaymentUpdateRequest,
	OrderBusinessRules,
	OrderAction,
	OrderActionRequest,
} from '@/types/orders';
import {BusinessLogicResult} from '@/types/common';
import {IOrderRepository} from '@/repositories/orders';
import {IFileUploadService} from '@/services/services/service.service';
import {IEmailService} from '@/services/email/email.service';
import {
	NotFoundError,
	AuthorizationError,
	ValidationError,
} from '@/middleware/error-handling';
import {OrderStatus, PaymentStatus} from '@prisma/client';

// Order Management Service (SRP - Single Responsibility Principle)
export class OrderManagementService {
	private businessRules: OrderBusinessRules = {
		maxCustomerNotesLength: 1000,
		maxProviderNotesLength: 1000,
		maxCancellationReasonLength: 500,
		maxFilesPerOrder: 10,
		maxFileSize: 10 * 1024 * 1024, // 10MB
		allowedFileTypes: [
			'image/jpeg',
			'image/png',
			'image/gif',
			'application/pdf',
			'text/plain',
		],
		minScheduledDateAdvance: 1, // 1 hour
		maxScheduledDateAdvance: 30 * 24, // 30 days
		autoCancelAfterHours: 72, // 3 days
		refundPolicy: {
			fullRefundWithinHours: 24,
			partialRefundWithinHours: 72,
			noRefundAfterHours: 168, // 7 days
		},
	};

	constructor(
		private orderRepository: IOrderRepository,
		private fileUploadService: IFileUploadService,
		private emailService: IEmailService
	) {}

	// Create a new order (SRP)
	async createOrder(
		request: CreateOrderRequest,
		userId: string
	): Promise<BusinessLogicResult<OrderWithMetrics>> {
		try {
			// Validate business rules
			const validationResult = this.validateCreateOrderRequest(request);
			if (!validationResult.success) {
				return validationResult;
			}

			// Get service to validate and get provider ID
			const service = await this.getServiceWithProvider(request.serviceId);
			if (!service) {
				return {
					success: false,
					error: 'Service not found',
				};
			}

			// Validate scheduled date if provided
			if (request.scheduledDate) {
				const scheduledDateValidation = this.validateScheduledDate(
					request.scheduledDate
				);
				if (!scheduledDateValidation.success) {
					return scheduledDateValidation;
				}
			}

			// Upload files if provided
			let fileUrls: string[] = [];
			if (request.files && request.files.length > 0) {
				fileUrls = await this.fileUploadService.uploadMultipleImages(
					request.files,
					`orders/${userId}`
				);
			}

			// Create order
			const order = await this.orderRepository.create(
				{...request, files: request.files || []},
				userId,
				request.serviceId,
				service.providerId
			);

			// Increment order counts
			await Promise.all([
				this.orderRepository.incrementServiceOrderCount(request.serviceId),
				this.orderRepository.incrementProviderOrderCount(service.providerId),
			]);

			// Get order with metrics
			const orderWithMetrics = await this.getOrderWithMetrics(order.id);

			// Send notifications
			await Promise.all([
				this.emailService.sendOrderConfirmationEmail(userId, order.orderNumber),
				this.emailService.sendOrderNotificationEmail(
					service.providerId,
					order.orderNumber
				),
			]);

			return {
				success: true,
				data: orderWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error ? error.message : 'Failed to create order',
			};
		}
	}

	// Get order by ID (SRP)
	async getOrderById(
		id: string
	): Promise<BusinessLogicResult<OrderWithMetrics>> {
		try {
			const order = await this.orderRepository.findById(id);
			if (!order) {
				return {
					success: false,
					error: 'Order not found',
				};
			}

			const orderWithMetrics = await this.getOrderWithMetrics(order.id);

			return {
				success: true,
				data: orderWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Failed to get order',
			};
		}
	}

	// Get order by order number (SRP)
	async getOrderByOrderNumber(
		orderNumber: string
	): Promise<BusinessLogicResult<OrderWithMetrics>> {
		try {
			const order = await this.orderRepository.findByOrderNumber(orderNumber);
			if (!order) {
				return {
					success: false,
					error: 'Order not found',
				};
			}

			const orderWithMetrics = await this.getOrderWithMetrics(order.id);

			return {
				success: true,
				data: orderWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error: error instanceof Error ? error.message : 'Failed to get order',
			};
		}
	}

	// Get orders by user ID (SRP)
	async getOrdersByUserId(
		userId: string
	): Promise<BusinessLogicResult<OrderWithMetrics[]>> {
		try {
			const orders = await this.orderRepository.findByUserId(userId);

			const ordersWithMetrics = await Promise.all(
				orders.map((order) => this.getOrderWithMetrics(order.id))
			);

			return {
				success: true,
				data: ordersWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error ? error.message : 'Failed to get user orders',
			};
		}
	}

	// Get orders by provider ID (SRP)
	async getOrdersByProviderId(
		providerId: string
	): Promise<BusinessLogicResult<OrderWithMetrics[]>> {
		try {
			const orders = await this.orderRepository.findByProviderId(providerId);

			const ordersWithMetrics = await Promise.all(
				orders.map((order) => this.getOrderWithMetrics(order.id))
			);

			return {
				success: true,
				data: ordersWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get provider orders',
			};
		}
	}

	// Update order (SRP)
	async updateOrder(
		id: string,
		request: UpdateOrderRequest,
		userId: string,
		userRole: string
	): Promise<BusinessLogicResult<OrderWithMetrics>> {
		try {
			// Get existing order
			const existingOrder = await this.orderRepository.findById(id);
			if (!existingOrder) {
				return {
					success: false,
					error: 'Order not found',
				};
			}

			// Check permissions (only owner, provider, or admin can update)
			if (
				userRole !== 'admin' &&
				existingOrder.userId !== userId &&
				existingOrder.providerId !== userId
			) {
				return {
					success: false,
					error:
						'You can only update your own orders or orders assigned to you',
				};
			}

			// Validate business rules
			const validationResult = this.validateUpdateOrderRequest(request);
			if (!validationResult.success) {
				return validationResult;
			}

			// Validate scheduled date if being updated
			if (request.scheduledDate) {
				const scheduledDateValidation = this.validateScheduledDate(
					request.scheduledDate
				);
				if (!scheduledDateValidation.success) {
					return scheduledDateValidation;
				}
			}

			// Handle file uploads
			if (request.files && request.files.length > 0) {
				const fileUrls = await this.fileUploadService.uploadMultipleImages(
					request.files,
					`orders/${existingOrder.userId}`
				);
				request.files = request.files.map((file, index) => ({
					...file,
					filename: fileUrls[index],
				}));
			}

			// Update order
			const updatedOrder = await this.orderRepository.update(id, request);

			// Get order with metrics
			const orderWithMetrics = await this.getOrderWithMetrics(updatedOrder.id);

			return {
				success: true,
				data: orderWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error ? error.message : 'Failed to update order',
			};
		}
	}

	// Delete order (SRP)
	async deleteOrder(
		id: string,
		userId: string,
		userRole: string
	): Promise<BusinessLogicResult<void>> {
		try {
			// Get existing order
			const existingOrder = await this.orderRepository.findById(id);
			if (!existingOrder) {
				return {
					success: false,
					error: 'Order not found',
				};
			}

			// Check permissions (only owner or admin can delete)
			if (userRole !== 'admin' && existingOrder.userId !== userId) {
				return {
					success: false,
					error: 'You can only delete your own orders',
				};
			}

			// Check if order can be deleted (only pending orders)
			if (existingOrder.status !== 'PENDING') {
				return {
					success: false,
					error: 'Only pending orders can be deleted',
				};
			}

			// Delete order
			await this.orderRepository.delete(id);

			return {
				success: true,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error ? error.message : 'Failed to delete order',
			};
		}
	}

	// Search orders (SRP)
	async searchOrders(
		params: OrderSearchRequest
	): Promise<BusinessLogicResult<OrderSearchResult>> {
		try {
			const result = await this.orderRepository.search(params);

			return {
				success: true,
				data: result,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error ? error.message : 'Failed to search orders',
			};
		}
	}

	// Update order status (SRP)
	async updateOrderStatus(
		id: string,
		request: OrderStatusUpdateRequest,
		userId: string,
		userRole: string
	): Promise<BusinessLogicResult<OrderWithMetrics>> {
		try {
			// Get existing order
			const existingOrder = await this.orderRepository.findById(id);
			if (!existingOrder) {
				return {
					success: false,
					error: 'Order not found',
				};
			}

			// Check permissions (only provider or admin can update status)
			if (userRole !== 'admin' && existingOrder.providerId !== userId) {
				return {
					success: false,
					error: 'You can only update status of orders assigned to you',
				};
			}

			// Validate status transition
			const statusTransitionValidation = this.validateStatusTransition(
				existingOrder.status,
				request.status
			);
			if (!statusTransitionValidation.success) {
				return statusTransitionValidation;
			}

			// Update order status
			const updatedOrder = await this.orderRepository.updateStatus(
				id,
				request.status,
				request.notes
			);

			// Get order with metrics
			const orderWithMetrics = await this.getOrderWithMetrics(updatedOrder.id);

			// Send notifications based on status change
			await this.sendStatusChangeNotifications(updatedOrder, request.status);

			return {
				success: true,
				data: orderWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to update order status',
			};
		}
	}

	// Update payment status (SRP)
	async updatePaymentStatus(
		id: string,
		request: PaymentUpdateRequest,
		userId: string,
		userRole: string
	): Promise<BusinessLogicResult<OrderWithMetrics>> {
		try {
			// Get existing order
			const existingOrder = await this.orderRepository.findById(id);
			if (!existingOrder) {
				return {
					success: false,
					error: 'Order not found',
				};
			}

			// Check permissions (only admin can update payment status)
			if (userRole !== 'admin') {
				return {
					success: false,
					error: 'Only administrators can update payment status',
				};
			}

			// Update payment status
			const updatedOrder = await this.orderRepository.updatePaymentStatus(
				id,
				request.paymentStatus,
				request.paymentMethod
			);

			// Get order with metrics
			const orderWithMetrics = await this.getOrderWithMetrics(updatedOrder.id);

			// Send payment notification
			await this.emailService.sendPaymentStatusUpdateEmail(
				updatedOrder.userId,
				updatedOrder.orderNumber,
				request.paymentStatus
			);

			return {
				success: true,
				data: orderWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to update payment status',
			};
		}
	}

	// Process order action (SRP)
	async processOrderAction(
		id: string,
		request: OrderActionRequest,
		userId: string,
		userRole: string
	): Promise<BusinessLogicResult<OrderWithMetrics>> {
		try {
			// Get existing order
			const existingOrder = await this.orderRepository.findById(id);
			if (!existingOrder) {
				return {
					success: false,
					error: 'Order not found',
				};
			}

			// Check permissions based on action
			const permissionValidation = this.validateActionPermissions(
				request.action,
				existingOrder,
				userId,
				userRole
			);
			if (!permissionValidation.success) {
				return permissionValidation;
			}

			// Process action
			let newStatus: OrderStatus;
			let notes = request.notes;

			switch (request.action) {
				case 'confirm':
					newStatus = 'CONFIRMED';
					break;
				case 'start':
					newStatus = 'IN_PROGRESS';
					break;
				case 'complete':
					newStatus = 'COMPLETED';
					break;
				case 'cancel':
					newStatus = 'CANCELLED';
					notes = request.cancellationReason;
					break;
				case 'refund':
					newStatus = 'REFUNDED';
					break;
				default:
					return {
						success: false,
						error: 'Invalid action',
					};
			}

			// Update order status
			const updatedOrder = await this.orderRepository.updateStatus(
				id,
				newStatus,
				notes
			);

			// Get order with metrics
			const orderWithMetrics = await this.getOrderWithMetrics(updatedOrder.id);

			// Send notifications
			await this.sendStatusChangeNotifications(updatedOrder, newStatus);

			return {
				success: true,
				data: orderWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to process order action',
			};
		}
	}

	// Get order metrics (SRP)
	async getOrderMetrics(
		userId?: string,
		providerId?: string
	): Promise<BusinessLogicResult<OrderMetrics>> {
		try {
			const metrics = await this.orderRepository.getOrderMetrics(
				userId,
				providerId
			);

			return {
				success: true,
				data: metrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get order metrics',
			};
		}
	}

	// Get order statistics (SRP)
	async getOrderStatistics(): Promise<BusinessLogicResult<OrderStatistics>> {
		try {
			const statistics = await this.orderRepository.getOrderStatistics();

			return {
				success: true,
				data: statistics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get order statistics',
			};
		}
	}

	// Get recent orders (SRP)
	async getRecentOrders(
		limit: number = 10
	): Promise<BusinessLogicResult<OrderWithMetrics[]>> {
		try {
			const orders = await this.orderRepository.getRecentOrders(limit);

			const ordersWithMetrics = await Promise.all(
				orders.map((order) => this.getOrderWithMetrics(order.id))
			);

			return {
				success: true,
				data: ordersWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get recent orders',
			};
		}
	}

	// Get overdue orders (SRP)
	async getOverdueOrders(): Promise<BusinessLogicResult<OrderWithMetrics[]>> {
		try {
			const orders = await this.orderRepository.getOverdueOrders();

			const ordersWithMetrics = await Promise.all(
				orders.map((order) => this.getOrderWithMetrics(order.id))
			);

			return {
				success: true,
				data: ordersWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get overdue orders',
			};
		}
	}

	// Helper method to get order with metrics (SRP)
	private async getOrderWithMetrics(
		orderId: string
	): Promise<OrderWithMetrics> {
		const order = await this.orderRepository.findById(orderId);
		if (!order) {
			throw new NotFoundError('Order not found');
		}

		// Calculate metrics
		const now = new Date();
		const processingTime = order.completedDate
			? (order.completedDate.getTime() - order.createdAt.getTime()) /
			  (1000 * 60 * 60) // hours
			: (now.getTime() - order.createdAt.getTime()) / (1000 * 60 * 60);

		const isOverdue = order.scheduledDate ? now > order.scheduledDate : false;
		const daysUntilScheduled = order.scheduledDate
			? Math.ceil(
					(order.scheduledDate.getTime() - now.getTime()) /
						(1000 * 60 * 60 * 24)
			  )
			: 0;

		const canBeCancelled = ['PENDING', 'CONFIRMED'].includes(order.status);
		const canBeRefunded = ['COMPLETED', 'CANCELLED'].includes(order.status);
		const refundAmount = canBeRefunded ? order.totalAmount : 0;

		return {
			...order,
			metrics: {
				processingTime,
				isOverdue,
				daysUntilScheduled,
				canBeCancelled,
				canBeRefunded,
				refundAmount,
			},
			timeline: [], // Would be populated from actual timeline data
		};
	}

	// Helper method to get service with provider (SRP)
	private async getServiceWithProvider(serviceId: string): Promise<any> {
		// This would typically use a service repository
		// For now, we'll return a mock implementation
		return {
			id: serviceId,
			providerId: 'mock-provider-id',
		};
	}

	// Validation methods (SRP)
	private validateCreateOrderRequest(
		request: CreateOrderRequest
	): BusinessLogicResult<void> {
		if (!request.serviceId) {
			return {
				success: false,
				error: 'Service ID is required',
			};
		}

		if (
			request.customerNotes &&
			request.customerNotes.length > this.businessRules.maxCustomerNotesLength
		) {
			return {
				success: false,
				error: `Customer notes cannot exceed ${this.businessRules.maxCustomerNotesLength} characters`,
			};
		}

		if (
			request.files &&
			request.files.length > this.businessRules.maxFilesPerOrder
		) {
			return {
				success: false,
				error: `Cannot upload more than ${this.businessRules.maxFilesPerOrder} files`,
			};
		}

		return {success: true};
	}

	private validateUpdateOrderRequest(
		request: UpdateOrderRequest
	): BusinessLogicResult<void> {
		if (
			request.customerNotes &&
			request.customerNotes.length > this.businessRules.maxCustomerNotesLength
		) {
			return {
				success: false,
				error: `Customer notes cannot exceed ${this.businessRules.maxCustomerNotesLength} characters`,
			};
		}

		if (
			request.providerNotes &&
			request.providerNotes.length > this.businessRules.maxProviderNotesLength
		) {
			return {
				success: false,
				error: `Provider notes cannot exceed ${this.businessRules.maxProviderNotesLength} characters`,
			};
		}

		if (
			request.cancellationReason &&
			request.cancellationReason.length >
				this.businessRules.maxCancellationReasonLength
		) {
			return {
				success: false,
				error: `Cancellation reason cannot exceed ${this.businessRules.maxCancellationReasonLength} characters`,
			};
		}

		return {success: true};
	}

	private validateScheduledDate(
		scheduledDate: Date
	): BusinessLogicResult<void> {
		const now = new Date();
		const minDate = new Date(
			now.getTime() +
				this.businessRules.minScheduledDateAdvance * 60 * 60 * 1000
		);
		const maxDate = new Date(
			now.getTime() +
				this.businessRules.maxScheduledDateAdvance * 24 * 60 * 60 * 1000
		);

		if (scheduledDate < minDate) {
			return {
				success: false,
				error: `Scheduled date must be at least ${this.businessRules.minScheduledDateAdvance} hours in the future`,
			};
		}

		if (scheduledDate > maxDate) {
			return {
				success: false,
				error: `Scheduled date cannot be more than ${this.businessRules.maxScheduledDateAdvance} days in the future`,
			};
		}

		return {success: true};
	}

	private validateStatusTransition(
		currentStatus: OrderStatus,
		newStatus: OrderStatus
	): BusinessLogicResult<void> {
		const validTransitions: Record<OrderStatus, OrderStatus[]> = {
			PENDING: ['CONFIRMED', 'CANCELLED'],
			CONFIRMED: ['IN_PROGRESS', 'CANCELLED'],
			IN_PROGRESS: ['COMPLETED', 'CANCELLED'],
			COMPLETED: ['REFUNDED'],
			CANCELLED: [],
			REFUNDED: [],
		};

		if (!validTransitions[currentStatus].includes(newStatus)) {
			return {
				success: false,
				error: `Cannot transition from ${currentStatus} to ${newStatus}`,
			};
		}

		return {success: true};
	}

	private validateActionPermissions(
		action: OrderAction,
		order: any,
		userId: string,
		userRole: string
	): BusinessLogicResult<void> {
		const actionPermissions: Record<OrderAction, string[]> = {
			confirm: ['admin', 'provider'],
			start: ['admin', 'provider'],
			complete: ['admin', 'provider'],
			cancel: ['admin', 'provider', 'user'],
			refund: ['admin'],
		};

		const allowedRoles = actionPermissions[action];
		if (!allowedRoles.includes(userRole)) {
			return {
				success: false,
				error: `You do not have permission to perform this action`,
			};
		}

		// Additional checks for specific actions
		if (action === 'cancel' && userRole === 'user' && order.userId !== userId) {
			return {
				success: false,
				error: 'You can only cancel your own orders',
			};
		}

		if (
			['confirm', 'start', 'complete'].includes(action) &&
			order.providerId !== userId &&
			userRole !== 'admin'
		) {
			return {
				success: false,
				error: 'You can only perform this action on orders assigned to you',
			};
		}

		return {success: true};
	}

	// Helper method to send status change notifications (SRP)
	private async sendStatusChangeNotifications(
		order: any,
		newStatus: OrderStatus
	): Promise<void> {
		const statusMessages: Record<OrderStatus, string> = {
			PENDING: 'Order has been placed',
			CONFIRMED: 'Order has been confirmed',
			IN_PROGRESS: 'Work on your order has started',
			COMPLETED: 'Your order has been completed',
			CANCELLED: 'Order has been cancelled',
			REFUNDED: 'Order has been refunded',
		};

		const message = statusMessages[newStatus];
		if (message) {
			await Promise.all([
				this.emailService.sendOrderStatusUpdateEmail(
					order.userId,
					order.orderNumber,
					newStatus
				),
				this.emailService.sendOrderStatusUpdateEmail(
					order.providerId,
					order.orderNumber,
					newStatus
				),
			]);
		}
	}
}
