// Provider Controller - Following established patterns and SOLID principles
import {Request, Response} from 'express';
import {ProviderManagementService} from '@/services/providers';
import {ResponseFormatter} from '@/utils/formatting';
import {
	CreateProviderRequest,
	UpdateProviderRequest,
	ProviderSearchRequest,
	ProviderVerificationRequest,
} from '@/types/providers';

// Provider Controller (SRP - Single Responsibility Principle)
export class ProviderController {
	constructor(private providerService: ProviderManagementService) {}

	// Create provider profile (SRP)
	async createProvider(req: Request, res: Response): Promise<void> {
		try {
			const request: CreateProviderRequest = req.body;
			const userId = req.user?.id;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.providerService.createProvider(request, userId);

			if (result.success) {
				res
					.status(201)
					.json(
						ResponseFormatter.success(
							'Provider profile created successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get provider by ID (SRP)
	async getProviderById(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const result = await this.providerService.getProviderById(id);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Provider retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(404).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get provider by user ID (SRP)
	async getProviderByUserId(req: Request, res: Response): Promise<void> {
		try {
			const {userId} = req.params;
			const result = await this.providerService.getProviderByUserId(userId);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Provider profile retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(404).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get current user's provider profile (SRP)
	async getMyProviderProfile(req: Request, res: Response): Promise<void> {
		try {
			const userId = req.user?.id;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.providerService.getProviderByUserId(userId);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Provider profile retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(404).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Update provider profile (SRP)
	async updateProvider(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const request: UpdateProviderRequest = req.body;
			const userId = req.user?.id;
			const userRole = req.user?.role;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.providerService.updateProvider(
				id,
				request,
				userId,
				userRole
			);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Provider profile updated successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Delete provider profile (SRP)
	async deleteProvider(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const userId = req.user?.id;
			const userRole = req.user?.role;

			if (!userId) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.providerService.deleteProvider(
				id,
				userId,
				userRole
			);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success('Provider profile deleted successfully')
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Search providers (SRP)
	async searchProviders(req: Request, res: Response): Promise<void> {
		try {
			const searchParams: ProviderSearchRequest = {
				query: req.query.query as string,
				filters: {
					specialties: req.query.specialties
						? (req.query.specialties as string).split(',')
						: undefined,
					serviceAreas: req.query.serviceAreas
						? (req.query.serviceAreas as string).split(',')
						: undefined,
					minRating: req.query.minRating
						? parseFloat(req.query.minRating as string)
						: undefined,
					maxRating: req.query.maxRating
						? parseFloat(req.query.maxRating as string)
						: undefined,
					isVerified: req.query.isVerified
						? req.query.isVerified === 'true'
						: undefined,
					isActive: req.query.isActive
						? req.query.isActive === 'true'
						: undefined,
					minExperience: req.query.minExperience
						? parseInt(req.query.minExperience as string)
						: undefined,
					maxExperience: req.query.maxExperience
						? parseInt(req.query.maxExperience as string)
						: undefined,
					paymentMethods: req.query.paymentMethods
						? (req.query.paymentMethods as string).split(',')
						: undefined,
					maxResponseTime: req.query.maxResponseTime
						? parseInt(req.query.maxResponseTime as string)
						: undefined,
					sortBy: req.query.sortBy as any,
					sortOrder: req.query.sortOrder as 'asc' | 'desc',
				},
				pagination: {
					page: req.query.page ? parseInt(req.query.page as string) : 1,
					limit: req.query.limit ? parseInt(req.query.limit as string) : 10,
				},
			};

			const result = await this.providerService.searchProviders(searchParams);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Providers retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get verified providers (SRP)
	async getVerifiedProviders(req: Request, res: Response): Promise<void> {
		try {
			const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
			const result = await this.providerService.getVerifiedProviders(limit);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Verified providers retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get top rated providers (SRP)
	async getTopRatedProviders(req: Request, res: Response): Promise<void> {
		try {
			const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
			const result = await this.providerService.getTopRatedProviders(limit);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Top rated providers retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Get provider statistics (SRP)
	async getProviderStatistics(req: Request, res: Response): Promise<void> {
		try {
			const result = await this.providerService.getProviderStatistics();

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Provider statistics retrieved successfully',
							result.data
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Verify provider (Admin only) (SRP)
	async verifyProvider(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const {verificationNotes} = req.body;
			const verifiedBy = req.user?.id;

			if (!verifiedBy) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.providerService.verifyProvider(
				id,
				verifiedBy,
				verificationNotes
			);

			if (result.success) {
				res
					.status(200)
					.json(ResponseFormatter.success('Provider verified successfully'));
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Deactivate provider (Admin only) (SRP)
	async deactivateProvider(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const {reason} = req.body;
			const deactivatedBy = req.user?.id;

			if (!deactivatedBy) {
				res
					.status(401)
					.json(ResponseFormatter.error('Authentication required'));
				return;
			}

			const result = await this.providerService.deactivateProvider(
				id,
				deactivatedBy,
				reason
			);

			if (result.success) {
				res
					.status(200)
					.json(ResponseFormatter.success('Provider deactivated successfully'));
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}

	// Submit verification request (SRP)
	async submitVerificationRequest(req: Request, res: Response): Promise<void> {
		try {
			const {id} = req.params;
			const request: ProviderVerificationRequest = req.body;
			const documentFiles = req.files as Express.Multer.File[];

			const result = await this.providerService.submitVerificationRequest(
				id,
				request,
				documentFiles
			);

			if (result.success) {
				res
					.status(200)
					.json(
						ResponseFormatter.success(
							'Verification request submitted successfully'
						)
					);
			} else {
				res.status(400).json(ResponseFormatter.error(result.error));
			}
		} catch (error) {
			res.status(500).json(ResponseFormatter.error('Internal server error'));
		}
	}
}
