// Base Repository - Following DRY and SOL<PERSON> principles
import { PrismaClient } from '@prisma/client';
import { BaseEntity } from '@/types/common';

// Generic repository interface (ISP)
export interface IBaseRepository<T extends BaseEntity> {
  findById(id: string): Promise<T | null>;
  findAll(options?: {
    skip?: number;
    take?: number;
    where?: Record<string, any>;
    orderBy?: Record<string, any>;
  }): Promise<T[]>;
  create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T>;
  update(id: string, data: Partial<T>): Promise<T>;
  delete(id: string): Promise<void>;
  count(where?: Record<string, any>): Promise<number>;
  exists(where: Record<string, any>): Promise<boolean>;
}

// Base repository implementation (DRY principle)
export abstract class BaseRepository<T extends BaseEntity> implements IBaseRepository<T> {
  protected prisma: PrismaClient;
  protected modelName: string;

  constructor(prisma: PrismaClient, modelName: string) {
    this.prisma = prisma;
    this.modelName = modelName;
  }

  protected get model() {
    return (this.prisma as any)[this.modelName];
  }

  async findById(id: string): Promise<T | null> {
    try {
      return await this.model.findUnique({
        where: { id },
      });
    } catch (error) {
      throw new Error(`Failed to find ${this.modelName} by id: ${error}`);
    }
  }

  async findAll(options: {
    skip?: number;
    take?: number;
    where?: Record<string, any>;
    orderBy?: Record<string, any>;
  } = {}): Promise<T[]> {
    try {
      const { skip, take, where, orderBy } = options;
      return await this.model.findMany({
        skip,
        take,
        where,
        orderBy: orderBy || { createdAt: 'desc' },
      });
    } catch (error) {
      throw new Error(`Failed to find ${this.modelName}s: ${error}`);
    }
  }

  async create(data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>): Promise<T> {
    try {
      return await this.model.create({
        data,
      });
    } catch (error) {
      throw new Error(`Failed to create ${this.modelName}: ${error}`);
    }
  }

  async update(id: string, data: Partial<T>): Promise<T> {
    try {
      return await this.model.update({
        where: { id },
        data,
      });
    } catch (error) {
      throw new Error(`Failed to update ${this.modelName}: ${error}`);
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await this.model.delete({
        where: { id },
      });
    } catch (error) {
      throw new Error(`Failed to delete ${this.modelName}: ${error}`);
    }
  }

  async count(where?: Record<string, any>): Promise<number> {
    try {
      return await this.model.count({ where });
    } catch (error) {
      throw new Error(`Failed to count ${this.modelName}s: ${error}`);
    }
  }

  async exists(where: Record<string, any>): Promise<boolean> {
    try {
      const count = await this.model.count({ where });
      return count > 0;
    } catch (error) {
      throw new Error(`Failed to check ${this.modelName} existence: ${error}`);
    }
  }
}