// Provider Management Service - Following SOLID principles and established patterns
import {
	CreateProviderRequest,
	UpdateProviderRequest,
	ProviderSearchRequest,
	ProviderSearchResult,
	ProviderWithMetrics,
	ProviderMetrics,
	ProviderStatistics,
	ProviderVerificationRequest,
	ProviderBusinessRules,
} from '@/types/providers';
import {BusinessLogicResult} from '@/types/common';
import {IProviderRepository} from '@/repositories/providers';
import {IFileUploadService} from '@/services/services/service.service';
import {IEmailService} from '@/services/email/email.service';
import {
	NotFoundError,
	AuthorizationError,
	ValidationError,
} from '@/middleware/error-handling';

// Provider Management Service (SRP - Single Responsibility Principle)
export class ProviderManagementService {
	private businessRules: ProviderBusinessRules = {
		maxSpecialties: 10,
		maxCertifications: 20,
		maxServiceAreas: 15,
		minBusinessNameLength: 2,
		maxBusinessNameLength: 100,
		minDescriptionLength: 10,
		maxDescriptionLength: 1000,
		requiredFields: ['businessName'],
		verificationRequired: true,
		maxResponseTime: 72, // hours
		minResponseTime: 1, // hours
	};

	constructor(
		private providerRepository: IProviderRepository,
		private fileUploadService: IFileUploadService,
		private emailService: IEmailService
	) {}

	// Create a new provider profile (SRP)
	async createProvider(
		request: CreateProviderRequest,
		userId: string
	): Promise<BusinessLogicResult<ProviderWithMetrics>> {
		try {
			// Validate business rules
			const validationResult = this.validateCreateProviderRequest(request);
			if (!validationResult.success) {
				return validationResult;
			}

			// Check if user already has a provider profile
			const existingProvider = await this.providerRepository.findByUserId(
				userId
			);
			if (existingProvider) {
				return {
					success: false,
					error: 'User already has a provider profile',
				};
			}

			// Check if business name is already taken
			const existingBusinessName =
				await this.providerRepository.findByBusinessName(request.businessName);
			if (existingBusinessName) {
				return {
					success: false,
					error: 'Business name is already taken',
				};
			}

			// Create provider profile
			const provider = await this.providerRepository.create(request, userId);

			// Get provider with metrics
			const providerWithMetrics = await this.getProviderWithMetrics(
				provider.id
			);

			// Send welcome email
			await this.emailService.sendProviderWelcomeEmail(
				userId,
				provider.businessName
			);

			return {
				success: true,
				data: providerWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to create provider profile',
			};
		}
	}

	// Get provider by ID (SRP)
	async getProviderById(
		id: string
	): Promise<BusinessLogicResult<ProviderWithMetrics>> {
		try {
			const provider = await this.providerRepository.findById(id);
			if (!provider) {
				return {
					success: false,
					error: 'Provider not found',
				};
			}

			const providerWithMetrics = await this.getProviderWithMetrics(
				provider.id
			);

			return {
				success: true,
				data: providerWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error ? error.message : 'Failed to get provider',
			};
		}
	}

	// Get provider by user ID (SRP)
	async getProviderByUserId(
		userId: string
	): Promise<BusinessLogicResult<ProviderWithMetrics>> {
		try {
			const provider = await this.providerRepository.findByUserId(userId);
			if (!provider) {
				return {
					success: false,
					error: 'Provider profile not found',
				};
			}

			const providerWithMetrics = await this.getProviderWithMetrics(
				provider.id
			);

			return {
				success: true,
				data: providerWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get provider profile',
			};
		}
	}

	// Update provider profile (SRP)
	async updateProvider(
		id: string,
		request: UpdateProviderRequest,
		userId: string,
		userRole: string
	): Promise<BusinessLogicResult<ProviderWithMetrics>> {
		try {
			// Get existing provider
			const existingProvider = await this.providerRepository.findById(id);
			if (!existingProvider) {
				return {
					success: false,
					error: 'Provider not found',
				};
			}

			// Check permissions (only owner or admin can update)
			if (userRole !== 'admin' && existingProvider.userId !== userId) {
				return {
					success: false,
					error: 'You can only update your own provider profile',
				};
			}

			// Validate business rules
			const validationResult = this.validateUpdateProviderRequest(request);
			if (!validationResult.success) {
				return validationResult;
			}

			// Check business name uniqueness if being updated
			if (
				request.businessName &&
				request.businessName !== existingProvider.businessName
			) {
				const existingBusinessName =
					await this.providerRepository.findByBusinessName(
						request.businessName
					);
				if (existingBusinessName) {
					return {
						success: false,
						error: 'Business name is already taken',
					};
				}
			}

			// Update provider
			const updatedProvider = await this.providerRepository.update(id, request);

			// Get provider with metrics
			const providerWithMetrics = await this.getProviderWithMetrics(
				updatedProvider.id
			);

			return {
				success: true,
				data: providerWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to update provider profile',
			};
		}
	}

	// Delete provider profile (SRP)
	async deleteProvider(
		id: string,
		userId: string,
		userRole: string
	): Promise<BusinessLogicResult<void>> {
		try {
			// Get existing provider
			const existingProvider = await this.providerRepository.findById(id);
			if (!existingProvider) {
				return {
					success: false,
					error: 'Provider not found',
				};
			}

			// Check permissions
			if (userRole !== 'admin' && existingProvider.userId !== userId) {
				return {
					success: false,
					error: 'You can only delete your own provider profile',
				};
			}

			// Delete provider
			await this.providerRepository.delete(id);

			return {
				success: true,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to delete provider profile',
			};
		}
	}

	// Search providers (SRP)
	async searchProviders(
		params: ProviderSearchRequest
	): Promise<BusinessLogicResult<ProviderSearchResult>> {
		try {
			const result = await this.providerRepository.search(params);

			return {
				success: true,
				data: result,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error ? error.message : 'Failed to search providers',
			};
		}
	}

	// Get verified providers (SRP)
	async getVerifiedProviders(
		limit: number = 10
	): Promise<BusinessLogicResult<ProviderWithMetrics[]>> {
		try {
			const providers = await this.providerRepository.getVerifiedProviders(
				limit
			);

			const providersWithMetrics = await Promise.all(
				providers.map((provider) => this.getProviderWithMetrics(provider.id))
			);

			return {
				success: true,
				data: providersWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get verified providers',
			};
		}
	}

	// Get top rated providers (SRP)
	async getTopRatedProviders(
		limit: number = 10
	): Promise<BusinessLogicResult<ProviderWithMetrics[]>> {
		try {
			const providers = await this.providerRepository.getTopRatedProviders(
				limit
			);

			const providersWithMetrics = await Promise.all(
				providers.map((provider) => this.getProviderWithMetrics(provider.id))
			);

			return {
				success: true,
				data: providersWithMetrics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get top rated providers',
			};
		}
	}

	// Get provider statistics (SRP)
	async getProviderStatistics(): Promise<
		BusinessLogicResult<ProviderStatistics>
	> {
		try {
			const statistics = await this.providerRepository.getProviderStatistics();

			return {
				success: true,
				data: statistics,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to get provider statistics',
			};
		}
	}

	// Verify provider (SRP)
	async verifyProvider(
		providerId: string,
		verifiedBy: string,
		verificationNotes?: string
	): Promise<BusinessLogicResult<void>> {
		try {
			const provider = await this.providerRepository.findById(providerId);
			if (!provider) {
				return {
					success: false,
					error: 'Provider not found',
				};
			}

			await this.providerRepository.verifyProvider(providerId, verifiedBy);

			// Send verification email
			await this.emailService.sendProviderVerificationEmail(
				provider.userId,
				provider.businessName
			);

			return {
				success: true,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error ? error.message : 'Failed to verify provider',
			};
		}
	}

	// Deactivate provider (SRP)
	async deactivateProvider(
		providerId: string,
		deactivatedBy: string,
		reason?: string
	): Promise<BusinessLogicResult<void>> {
		try {
			const provider = await this.providerRepository.findById(providerId);
			if (!provider) {
				return {
					success: false,
					error: 'Provider not found',
				};
			}

			await this.providerRepository.deactivateProvider(
				providerId,
				deactivatedBy,
				reason
			);

			// Send deactivation email
			await this.emailService.sendProviderDeactivationEmail(
				provider.userId,
				provider.businessName,
				reason
			);

			return {
				success: true,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to deactivate provider',
			};
		}
	}

	// Submit verification request (SRP)
	async submitVerificationRequest(
		providerId: string,
		request: ProviderVerificationRequest,
		documentFiles?: Express.Multer.File[]
	): Promise<BusinessLogicResult<void>> {
		try {
			const provider = await this.providerRepository.findById(providerId);
			if (!provider) {
				return {
					success: false,
					error: 'Provider not found',
				};
			}

			// Upload documents if provided
			let documentUrls: string[] = [];
			if (documentFiles && documentFiles.length > 0) {
				documentUrls = await this.fileUploadService.uploadMultipleImages(
					documentFiles,
					`verification/${providerId}`
				);
			}

			// In a real implementation, you would store the verification request
			// For now, we'll just send a notification email
			await this.emailService.sendVerificationRequestNotification(
				provider.userId,
				provider.businessName
			);

			return {
				success: true,
			};
		} catch (error) {
			return {
				success: false,
				error:
					error instanceof Error
						? error.message
						: 'Failed to submit verification request',
			};
		}
	}

	// Helper method to get provider with metrics (SRP)
	private async getProviderWithMetrics(
		providerId: string
	): Promise<ProviderWithMetrics> {
		const [provider, metrics] = await Promise.all([
			this.providerRepository.findById(providerId),
			this.providerRepository.getProviderMetrics(providerId),
		]);

		if (!provider) {
			throw new NotFoundError('Provider not found');
		}

		return {
			...provider,
			metrics,
			recentOrders: [], // Would be populated from actual order data
			recentReviews: [], // Would be populated from actual review data
		};
	}

	// Validation methods (SRP)
	private validateCreateProviderRequest(
		request: CreateProviderRequest
	): BusinessLogicResult<void> {
		if (
			!request.businessName ||
			request.businessName.length < this.businessRules.minBusinessNameLength
		) {
			return {
				success: false,
				error: `Business name must be at least ${this.businessRules.minBusinessNameLength} characters long`,
			};
		}

		if (
			request.businessName.length > this.businessRules.maxBusinessNameLength
		) {
			return {
				success: false,
				error: `Business name cannot exceed ${this.businessRules.maxBusinessNameLength} characters`,
			};
		}

		if (
			request.description &&
			request.description.length < this.businessRules.minDescriptionLength
		) {
			return {
				success: false,
				error: `Description must be at least ${this.businessRules.minDescriptionLength} characters long`,
			};
		}

		if (
			request.description &&
			request.description.length > this.businessRules.maxDescriptionLength
		) {
			return {
				success: false,
				error: `Description cannot exceed ${this.businessRules.maxDescriptionLength} characters`,
			};
		}

		if (
			request.specialties &&
			request.specialties.length > this.businessRules.maxSpecialties
		) {
			return {
				success: false,
				error: `Cannot have more than ${this.businessRules.maxSpecialties} specialties`,
			};
		}

		if (
			request.certifications &&
			request.certifications.length > this.businessRules.maxCertifications
		) {
			return {
				success: false,
				error: `Cannot have more than ${this.businessRules.maxCertifications} certifications`,
			};
		}

		if (
			request.serviceAreas &&
			request.serviceAreas.length > this.businessRules.maxServiceAreas
		) {
			return {
				success: false,
				error: `Cannot have more than ${this.businessRules.maxServiceAreas} service areas`,
			};
		}

		if (
			request.responseTime &&
			request.responseTime > this.businessRules.maxResponseTime
		) {
			return {
				success: false,
				error: `Response time cannot exceed ${this.businessRules.maxResponseTime} hours`,
			};
		}

		if (
			request.responseTime &&
			request.responseTime < this.businessRules.minResponseTime
		) {
			return {
				success: false,
				error: `Response time must be at least ${this.businessRules.minResponseTime} hour`,
			};
		}

		return {success: true};
	}

	private validateUpdateProviderRequest(
		request: UpdateProviderRequest
	): BusinessLogicResult<void> {
		if (
			request.businessName &&
			request.businessName.length < this.businessRules.minBusinessNameLength
		) {
			return {
				success: false,
				error: `Business name must be at least ${this.businessRules.minBusinessNameLength} characters long`,
			};
		}

		if (
			request.businessName &&
			request.businessName.length > this.businessRules.maxBusinessNameLength
		) {
			return {
				success: false,
				error: `Business name cannot exceed ${this.businessRules.maxBusinessNameLength} characters`,
			};
		}

		if (
			request.description &&
			request.description.length < this.businessRules.minDescriptionLength
		) {
			return {
				success: false,
				error: `Description must be at least ${this.businessRules.minDescriptionLength} characters long`,
			};
		}

		if (
			request.description &&
			request.description.length > this.businessRules.maxDescriptionLength
		) {
			return {
				success: false,
				error: `Description cannot exceed ${this.businessRules.maxDescriptionLength} characters`,
			};
		}

		if (
			request.specialties &&
			request.specialties.length > this.businessRules.maxSpecialties
		) {
			return {
				success: false,
				error: `Cannot have more than ${this.businessRules.maxSpecialties} specialties`,
			};
		}

		if (
			request.certifications &&
			request.certifications.length > this.businessRules.maxCertifications
		) {
			return {
				success: false,
				error: `Cannot have more than ${this.businessRules.maxCertifications} certifications`,
			};
		}

		if (
			request.serviceAreas &&
			request.serviceAreas.length > this.businessRules.maxServiceAreas
		) {
			return {
				success: false,
				error: `Cannot have more than ${this.businessRules.maxServiceAreas} service areas`,
			};
		}

		if (
			request.responseTime &&
			request.responseTime > this.businessRules.maxResponseTime
		) {
			return {
				success: false,
				error: `Response time cannot exceed ${this.businessRules.maxResponseTime} hours`,
			};
		}

		if (
			request.responseTime &&
			request.responseTime < this.businessRules.minResponseTime
		) {
			return {
				success: false,
				error: `Response time must be at least ${this.businessRules.minResponseTime} hour`,
			};
		}

		return {success: true};
	}
}
