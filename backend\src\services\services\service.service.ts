// Service Management Service - Following SOLID principles
import {
  Service,
  ServiceCategory,
  CreateServiceRequest,
  UpdateServiceRequest,
  ServiceSearchRequest,
} from '@/types/services';
import { BusinessLogicResult, PaginatedResult } from '@/types/common';
import { StringFormatter } from '@/utils/formatting';
import { NotFoundError, AuthorizationError } from '@/middleware/error-handling';

// Abstract interfaces (DIP - Dependency Inversion Principle)
export interface IServiceRepository {
  create(serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>): Promise<Service>;
  findById(id: string): Promise<Service | null>;
  findBySlug(slug: string): Promise<Service | null>;
  findByProvider(providerId: string): Promise<Service[]>;
  findByCategory(category: string): Promise<Service[]>;
  update(id: string, serviceData: Partial<Service>): Promise<Service>;
  delete(id: string): Promise<void>;
  search(params: ServiceSearchRequest): Promise<PaginatedResult<Service>>;
  getFeatured(limit?: number): Promise<Service[]>;
  getPopular(limit?: number): Promise<Service[]>;
  incrementOrderCount(id: string): Promise<void>;
}

export interface ICategoryRepository {
  findAll(): Promise<ServiceCategory[]>;
  findBySlug(slug: string): Promise<ServiceCategory | null>;
  findById(id: string): Promise<ServiceCategory | null>;
  create(categoryData: Omit<ServiceCategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<ServiceCategory>;
  update(id: string, categoryData: Partial<ServiceCategory>): Promise<ServiceCategory>;
  delete(id: string): Promise<void>;
}

export interface IFileUploadService {
  uploadImage(file: Express.Multer.File, folder: string): Promise<string>;
  uploadMultipleImages(files: Express.Multer.File[], folder: string): Promise<string[]>;
  deleteImage(url: string): Promise<void>;
}

// Main Service Management Service (SRP)
export class ServiceManagementService {
  constructor(
    private serviceRepository: IServiceRepository,
    private categoryRepository: ICategoryRepository,
    private fileUploadService: IFileUploadService
  ) {}

  // Create a new service (SRP)
  async createService(
    request: CreateServiceRequest,
    providerId: string,
    imageFiles?: Express.Multer.File[]
  ): Promise<BusinessLogicResult<Service>> {
    try {
      // Validate category exists
      const category = await this.categoryRepository.findBySlug(request.category);
      if (!category) {
        return {
          success: false,
          error: 'Invalid category',
        };
      }

      // Upload images if provided
      let imageUrls: string[] = [];
      if (imageFiles && imageFiles.length > 0) {
        imageUrls = await this.fileUploadService.uploadMultipleImages(
          imageFiles,
          `services/${providerId}`
        );
      }

      // Prepare service data
      const serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt'> = {
        name: request.name,
        description: request.description,
        shortDescription: request.shortDescription,
        category: request.category,
        subcategory: request.subcategory,
        price: request.price,
        priceType: request.priceType,
        images: imageUrls,
        providerId,
        isActive: true,
        isFeatured: false,
        specifications: request.specifications || [],
        customFields: request.customFields || [],
        tags: request.tags || [],
        reviewCount: 0,
        orderCount: 0,
        estimatedDelivery: request.estimatedDelivery,
        seoMetadata: {
          title: request.name,
          description: request.shortDescription || request.description.substring(0, 160),
          keywords: request.tags || [],
        },
      };

      const service = await this.serviceRepository.create(serviceData);

      return {
        success: true,
        data: service,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create service',
      };
    }
  }

  // Get service by ID (SRP)
  async getServiceById(id: string): Promise<BusinessLogicResult<Service>> {
    try {
      const service = await this.serviceRepository.findById(id);
      if (!service) {
        return {
          success: false,
          error: 'Service not found',
        };
      }

      return {
        success: true,
        data: service,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get service',
      };
    }
  }

  // Update service (SRP)
  async updateService(
    id: string,
    request: UpdateServiceRequest,
    userId: string,
    userRole: string,
    imageFiles?: Express.Multer.File[]
  ): Promise<BusinessLogicResult<Service>> {
    try {
      // Get existing service
      const existingService = await this.serviceRepository.findById(id);
      if (!existingService) {
        return {
          success: false,
          error: 'Service not found',
        };
      }

      // Check permissions (only owner or admin can update)
      if (userRole !== 'admin' && existingService.providerId !== userId) {
        return {
          success: false,
          error: 'You can only update your own services',
        };
      }

      // Validate category if provided
      if (request.category) {
        const category = await this.categoryRepository.findBySlug(request.category);
        if (!category) {
          return {
            success: false,
            error: 'Invalid category',
          };
        }
      }

      // Handle image uploads
      let imageUrls = existingService.images;
      if (imageFiles && imageFiles.length > 0) {
        const newImageUrls = await this.fileUploadService.uploadMultipleImages(
          imageFiles,
          `services/${existingService.providerId}`
        );
        imageUrls = [...imageUrls, ...newImageUrls];
      }

      // Prepare update data
      const updateData: Partial<Service> = {
        ...request,
        images: imageUrls,
      };

      // Update SEO metadata if name or description changed
      if (request.name || request.shortDescription || request.description) {
        updateData.seoMetadata = {
          ...existingService.seoMetadata,
          title: request.name || existingService.name,
          description: request.shortDescription || 
                      request.description?.substring(0, 160) || 
                      existingService.seoMetadata?.description,
        };
      }

      const updatedService = await this.serviceRepository.update(id, updateData);

      return {
        success: true,
        data: updatedService,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update service',
      };
    }
  }

  // Delete service (SRP)
  async deleteService(id: string, userId: string, userRole: string): Promise<BusinessLogicResult<void>> {
    try {
      // Get existing service
      const existingService = await this.serviceRepository.findById(id);
      if (!existingService) {
        return {
          success: false,
          error: 'Service not found',
        };
      }

      // Check permissions
      if (userRole !== 'admin' && existingService.providerId !== userId) {
        return {
          success: false,
          error: 'You can only delete your own services',
        };
      }

      // Delete images
      for (const imageUrl of existingService.images) {
        await this.fileUploadService.deleteImage(imageUrl);
      }

      // Delete service
      await this.serviceRepository.delete(id);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete service',
      };
    }
  }

  // Search services (SRP)
  async searchServices(params: ServiceSearchRequest): Promise<BusinessLogicResult<PaginatedResult<Service>>> {
    try {
      const result = await this.serviceRepository.search(params);
      
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to search services',
      };
    }
  }

  // Get services by provider (SRP)
  async getServicesByProvider(providerId: string): Promise<BusinessLogicResult<Service[]>> {
    try {
      const services = await this.serviceRepository.findByProvider(providerId);
      
      return {
        success: true,
        data: services,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get provider services',
      };
    }
  }

  // Get services by category (SRP)
  async getServicesByCategory(category: string): Promise<BusinessLogicResult<Service[]>> {
    try {
      const services = await this.serviceRepository.findByCategory(category);
      
      return {
        success: true,
        data: services,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get category services',
      };
    }
  }

  // Get featured services (SRP)
  async getFeaturedServices(limit: number = 10): Promise<BusinessLogicResult<Service[]>> {
    try {
      const services = await this.serviceRepository.getFeatured(limit);
      
      return {
        success: true,
        data: services,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get featured services',
      };
    }
  }

  // Get popular services (SRP)
  async getPopularServices(limit: number = 10): Promise<BusinessLogicResult<Service[]>> {
    try {
      const services = await this.serviceRepository.getPopular(limit);
      
      return {
        success: true,
        data: services,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get popular services',
      };
    }
  }

  // Toggle service active status (SRP)
  async toggleServiceStatus(
    id: string, 
    userId: string, 
    userRole: string
  ): Promise<BusinessLogicResult<Service>> {
    try {
      const service = await this.serviceRepository.findById(id);
      if (!service) {
        return {
          success: false,
          error: 'Service not found',
        };
      }

      // Check permissions
      if (userRole !== 'admin' && service.providerId !== userId) {
        return {
          success: false,
          error: 'You can only modify your own services',
        };
      }

      const updatedService = await this.serviceRepository.update(id, {
        isActive: !service.isActive,
      });

      return {
        success: true,
        data: updatedService,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to toggle service status',
      };
    }
  }
}