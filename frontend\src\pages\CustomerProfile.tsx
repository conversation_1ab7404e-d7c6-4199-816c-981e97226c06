import React, { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useProviders } from '../contexts/ProviderContext';
import { 
  User, 
  MapPin, 
  Phone, 
  Mail, 
  Plus, 
  Edit, 
  Trash2, 
  Star,
  Package,
  Clock,
  Settings,
  Bell,
  Heart,
  Home,
  Building
} from 'lucide-react';

const CustomerProfile: React.FC = () => {
  const { user } = useAuth();
  const { 
    customerProfile, 
    updateCustomerProfile, 
    addCustomerAddress, 
    updateCustomerAddress, 
    deleteCustomerAddress 
  } = useProviders();
  const [activeTab, setActiveTab] = useState<'profile' | 'addresses' | 'orders' | 'preferences'>('profile');
  const [isAddingAddress, setIsAddingAddress] = useState(false);
  const [editingAddress, setEditingAddress] = useState<string | null>(null);
  const [addressForm, setAddressForm] = useState({
    label: '',
    street: '',
    city: '',
    state: '',
    zipCode: ''
  });

  if (!customerProfile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Profile Not Found</h1>
          <p className="text-gray-600">Please log in to view your profile.</p>
        </div>
      </div>
    );
  }

  const handleAddressSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingAddress) {
      updateCustomerAddress(editingAddress, {
        ...addressForm,
        coordinates: { lat: 40.7128, lng: -74.0060 } // Mock coordinates
      });
      setEditingAddress(null);
    } else {
      addCustomerAddress({
        ...addressForm,
        coordinates: { lat: 40.7128, lng: -74.0060 }, // Mock coordinates
        isDefault: customerProfile.addresses.length === 0
      });
    }
    
    setIsAddingAddress(false);
    setAddressForm({ label: '', street: '', city: '', state: '', zipCode: '' });
  };

  const handleEditAddress = (address: any) => {
    setAddressForm({
      label: address.label,
      street: address.street,
      city: address.city,
      state: address.state,
      zipCode: address.zipCode
    });
    setEditingAddress(address.id);
    setIsAddingAddress(true);
  };

  const handleDeleteAddress = (addressId: string) => {
    if (window.confirm('Are you sure you want to delete this address?')) {
      deleteCustomerAddress(addressId);
    }
  };

  const stats = [
    { title: 'Total Orders', value: customerProfile.orderHistory.length.toString(), icon: Package, color: 'bg-blue-500' },
    { title: 'Saved Addresses', value: customerProfile.addresses.length.toString(), icon: MapPin, color: 'bg-green-500' },
    { title: 'Preferred Providers', value: customerProfile.preferences.preferredProviders.length.toString(), icon: Heart, color: 'bg-red-500' },
    { title: 'Member Since', value: new Date(customerProfile.joinedDate).getFullYear().toString(), icon: Star, color: 'bg-purple-500' },
  ];

  return (
    <div className="min-h-screen bg-warm-cream py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{customerProfile.name}</h1>
              <p className="text-gray-600">Customer Profile</p>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center">
                <div className={`${stat.color} p-3 rounded-full`}>
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4">
                  <p className="text-sm text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-gray-200 mb-8">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'profile', label: 'Profile Information', icon: User },
              { id: 'addresses', label: 'Addresses', icon: MapPin },
              { id: 'orders', label: 'Order History', icon: Package },
              { id: 'preferences', label: 'Preferences', icon: Settings }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Profile Tab */}
        {activeTab === 'profile' && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Profile Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                <div className="text-gray-900 p-3 bg-gray-50 rounded-lg">{customerProfile.name}</div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
                <div className="text-gray-900 p-3 bg-gray-50 rounded-lg flex items-center">
                  <Mail className="h-4 w-4 text-gray-400 mr-2" />
                  {customerProfile.email}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
                <div className="text-gray-900 p-3 bg-gray-50 rounded-lg flex items-center">
                  <Phone className="h-4 w-4 text-gray-400 mr-2" />
                  {customerProfile.phone || 'Not provided'}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Member Since</label>
                <div className="text-gray-900 p-3 bg-gray-50 rounded-lg">
                  {new Date(customerProfile.joinedDate).toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Addresses Tab */}
        {activeTab === 'addresses' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-900">Saved Addresses</h2>
                <button
                  onClick={() => setIsAddingAddress(true)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
                >
                  <Plus className="h-4 w-4" />
                  <span>Add Address</span>
                </button>
              </div>

              {/* Address List */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {customerProfile.addresses.map((address) => (
                  <div key={address.id} className={`border-2 rounded-lg p-4 ${
                    address.isDefault ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                  }`}>
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center space-x-2">
                        {address.label === 'Home' ? (
                          <Home className="h-4 w-4 text-gray-500" />
                        ) : (
                          <Building className="h-4 w-4 text-gray-500" />
                        )}
                        <span className="font-medium text-gray-900">{address.label}</span>
                        {address.isDefault && (
                          <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">
                            Default
                          </span>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEditAddress(address)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Edit className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteAddress(address.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    <div className="text-gray-600 text-sm">
                      {address.street}<br />
                      {address.city}, {address.state} {address.zipCode}
                    </div>
                  </div>
                ))}
              </div>

              {/* Add/Edit Address Form */}
              {isAddingAddress && (
                <div className="mt-6 border-t pt-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    {editingAddress ? 'Edit Address' : 'Add New Address'}
                  </h3>
                  <form onSubmit={handleAddressSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Label</label>
                      <input
                        type="text"
                        value={addressForm.label}
                        onChange={(e) => setAddressForm(prev => ({ ...prev, label: e.target.value }))}
                        placeholder="Home, Office, etc."
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Street Address</label>
                      <input
                        type="text"
                        value={addressForm.street}
                        onChange={(e) => setAddressForm(prev => ({ ...prev, street: e.target.value }))}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                      <input
                        type="text"
                        value={addressForm.city}
                        onChange={(e) => setAddressForm(prev => ({ ...prev, city: e.target.value }))}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">State</label>
                      <input
                        type="text"
                        value={addressForm.state}
                        onChange={(e) => setAddressForm(prev => ({ ...prev, state: e.target.value }))}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">ZIP Code</label>
                      <input
                        type="text"
                        value={addressForm.zipCode}
                        onChange={(e) => setAddressForm(prev => ({ ...prev, zipCode: e.target.value }))}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="md:col-span-2 flex space-x-3">
                      <button
                        type="submit"
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        {editingAddress ? 'Update Address' : 'Add Address'}
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setIsAddingAddress(false);
                          setEditingAddress(null);
                          setAddressForm({ label: '', street: '', city: '', state: '', zipCode: '' });
                        }}
                        className="bg-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-400 transition-colors"
                      >
                        Cancel
                      </button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Orders Tab */}
        {activeTab === 'orders' && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Order History</h2>
            
            {customerProfile.orderHistory.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Orders Yet</h3>
                <p className="text-gray-600 mb-4">You haven't placed any orders yet.</p>
                <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  Browse Services
                </button>
              </div>
            ) : (
              <div className="space-y-4">
                {/* Order history would be displayed here */}
                <p className="text-gray-600">Order history will be displayed here when orders are placed.</p>
              </div>
            )}
          </div>
        )}

        {/* Preferences Tab */}
        {activeTab === 'preferences' && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6">Preferences</h2>
            
            <div className="space-y-6">
              {/* Communication Preferences */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Communication</h3>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="communication"
                      value="email"
                      checked={customerProfile.preferences.communicationMethod === 'email'}
                      onChange={(e) => updateCustomerProfile({
                        preferences: {
                          ...customerProfile.preferences,
                          communicationMethod: e.target.value as any
                        }
                      })}
                      className="mr-3"
                    />
                    <span className="text-gray-700">Email only</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="communication"
                      value="phone"
                      checked={customerProfile.preferences.communicationMethod === 'phone'}
                      onChange={(e) => updateCustomerProfile({
                        preferences: {
                          ...customerProfile.preferences,
                          communicationMethod: e.target.value as any
                        }
                      })}
                      className="mr-3"
                    />
                    <span className="text-gray-700">Phone only</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="radio"
                      name="communication"
                      value="both"
                      checked={customerProfile.preferences.communicationMethod === 'both'}
                      onChange={(e) => updateCustomerProfile({
                        preferences: {
                          ...customerProfile.preferences,
                          communicationMethod: e.target.value as any
                        }
                      })}
                      className="mr-3"
                    />
                    <span className="text-gray-700">Both email and phone</span>
                  </label>
                </div>
              </div>

              {/* Notification Preferences */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Notifications</h3>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span className="text-gray-700">Order updates</span>
                    <input
                      type="checkbox"
                      checked={customerProfile.preferences.notifications.orderUpdates}
                      onChange={(e) => updateCustomerProfile({
                        preferences: {
                          ...customerProfile.preferences,
                          notifications: {
                            ...customerProfile.preferences.notifications,
                            orderUpdates: e.target.checked
                          }
                        }
                      })}
                      className="rounded"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-700">Promotions and offers</span>
                    <input
                      type="checkbox"
                      checked={customerProfile.preferences.notifications.promotions}
                      onChange={(e) => updateCustomerProfile({
                        preferences: {
                          ...customerProfile.preferences,
                          notifications: {
                            ...customerProfile.preferences.notifications,
                            promotions: e.target.checked
                          }
                        }
                      })}
                      className="rounded"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span className="text-gray-700">New providers in your area</span>
                    <input
                      type="checkbox"
                      checked={customerProfile.preferences.notifications.newProviders}
                      onChange={(e) => updateCustomerProfile({
                        preferences: {
                          ...customerProfile.preferences,
                          notifications: {
                            ...customerProfile.preferences.notifications,
                            newProviders: e.target.checked
                          }
                        }
                      })}
                      className="rounded"
                    />
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomerProfile;