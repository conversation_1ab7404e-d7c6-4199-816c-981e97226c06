import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { ServiceProvider } from './contexts/ServiceContext';
import { ProviderProvider } from './contexts/ProviderContext';
import Header from './components/Header';
import Breadcrumbs from './components/Breadcrumbs';
import Footer from './components/Footer';
import ProtectedRoute from './components/ProtectedRoute';
import Home from './pages/Home';
import Services from './pages/Services';
import Login from './pages/Login';
import Register from './pages/Register';
import ServiceDetail from './pages/ServiceDetail';
import Dashboard from './pages/Dashboard';
import Admin from './pages/Admin';
import BusinessCards from './pages/BusinessCards';
import MarketingMaterials from './pages/MarketingMaterials';
import SignsBanners from './pages/SignsBanners';
import InvitationsStationery from './pages/InvitationsStationery';
import StickersLabels from './pages/StickersLabels';
import GiftsDecor from './pages/GiftsDecor';
import Apparel from './pages/Apparel';
import ProviderServices from './pages/ProviderServices';
import ProviderLocator from './pages/ProviderLocator';
import ProviderDashboard from './pages/ProviderDashboard';
import CustomerProfile from './pages/CustomerProfile';
import ProviderProfile from './pages/ProviderProfile';
import DesignServices from './pages/DesignServices';
import LogoDesign from './pages/LogoDesign';
import WebsiteDesign from './pages/WebsiteDesign';
import PrintingDesign from './pages/PrintingDesign';
import PackageDesign from './pages/PackageDesign';
import IllustratorArt from './pages/IllustratorArt';
import VehicleWraps from './pages/VehicleWraps';
import Contact from './pages/Contact';
import OurWork from './pages/OurWork';

function App() {
  return (
    <AuthProvider>
      <ProviderProvider>
        <ServiceProvider>
          <Router>
            <div className="min-h-screen flex flex-col">
              <Header />
              <Breadcrumbs />
              <main className="flex-1">
                <Routes>
                  <Route path="/" element={<Home />} />
                  <Route path="/services" element={<Services />} />
                  <Route path="/business-cards" element={<BusinessCards />} />
                  <Route path="/marketing-materials" element={<MarketingMaterials />} />
                  <Route path="/signs-banners" element={<SignsBanners />} />
                  <Route path="/invitations-stationery" element={<InvitationsStationery />} />
                  <Route path="/stickers-labels" element={<StickersLabels />} />
                  <Route path="/gifts-decor" element={<GiftsDecor />} />
                  <Route path="/apparel" element={<Apparel />} />
                  <Route path="/design-services" element={<DesignServices />} />
                  <Route path="/provider-services" element={<ProviderServices />} />
                  <Route path="/find-providers" element={<ProviderLocator />} />
                  <Route path="/service/:id" element={<ServiceDetail />} />
                  <Route path="/login" element={<Login />} />
                  <Route path="/register" element={<Register />} />
                  <Route
                    path="/dashboard"
                    element={
                      <ProtectedRoute>
                        <Dashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/admin"
                    element={
                      <ProtectedRoute adminOnly>
                        <Admin />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/provider-dashboard"
                    element={
                      <ProtectedRoute>
                        <ProviderDashboard />
                      </ProtectedRoute>
                    }
                  />
                  <Route
                    path="/customer-profile"
                    element={
                      <ProtectedRoute>
                        <CustomerProfile />
                      </ProtectedRoute>
                    }
                  />
                  <Route path="/provider/:id" element={<ProviderProfile />} />
                  <Route path="/design-services/logo-design" element={<LogoDesign />} />
                  <Route path="/design-services/website-design" element={<WebsiteDesign />} />
                  <Route path="/design-services/printing-design" element={<PrintingDesign />} />
                  <Route path="/design-services/package-design" element={<PackageDesign />} />
                  <Route path="/design-services/illustrator-art" element={<IllustratorArt />} />
                  <Route path="/design-services/vehicle-wraps" element={<VehicleWraps />} />
                  <Route path="/contact" element={<Contact />} />
                  <Route path="/our-work" element={<OurWork />} />
                </Routes>
              </main>
              <Footer />
            </div>
          </Router>
        </ServiceProvider>
      </ProviderProvider>
    </AuthProvider>
  );
}

export default App;