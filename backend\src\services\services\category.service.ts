// Category Service - Following SRP principle
import { ServiceCategory } from '@/types/services';
import { BusinessLogicResult } from '@/types/common';
import { StringFormatter } from '@/utils/formatting';
import { ICategoryRepository } from './service.service';

export class CategoryService {
  constructor(private categoryRepository: ICategoryRepository) {}

  // Get all categories (SRP)
  async getAllCategories(): Promise<BusinessLogicResult<ServiceCategory[]>> {
    try {
      const categories = await this.categoryRepository.findAll();
      
      return {
        success: true,
        data: categories,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get categories',
      };
    }
  }

  // Get category by slug (SRP)
  async getCategoryBySlug(slug: string): Promise<BusinessLogicResult<ServiceCategory>> {
    try {
      const category = await this.categoryRepository.findBySlug(slug);
      if (!category) {
        return {
          success: false,
          error: 'Category not found',
        };
      }

      return {
        success: true,
        data: category,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get category',
      };
    }
  }

  // Create category (Admin only) (SRP)
  async createCategory(
    categoryData: {
      name: string;
      description: string;
      parentId?: string;
      icon?: string;
    }
  ): Promise<BusinessLogicResult<ServiceCategory>> {
    try {
      const slug = StringFormatter.slugify(categoryData.name);
      
      // Check if slug already exists
      const existingCategory = await this.categoryRepository.findBySlug(slug);
      if (existingCategory) {
        return {
          success: false,
          error: 'Category with this name already exists',
        };
      }

      // Get sort order (last + 1)
      const allCategories = await this.categoryRepository.findAll();
      const maxSortOrder = Math.max(...allCategories.map(cat => cat.sortOrder), 0);

      const newCategoryData: Omit<ServiceCategory, 'id' | 'createdAt' | 'updatedAt'> = {
        name: categoryData.name,
        description: categoryData.description,
        slug,
        parentId: categoryData.parentId,
        icon: categoryData.icon,
        isActive: true,
        sortOrder: maxSortOrder + 1,
        seoMetadata: {
          title: `${categoryData.name} - Printing Services`,
          description: categoryData.description,
          keywords: [categoryData.name.toLowerCase(), 'printing', 'services'],
        },
      };

      const category = await this.categoryRepository.create(newCategoryData);

      return {
        success: true,
        data: category,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create category',
      };
    }
  }

  // Update category (Admin only) (SRP)
  async updateCategory(
    id: string,
    updateData: {
      name?: string;
      description?: string;
      icon?: string;
      isActive?: boolean;
      sortOrder?: number;
    }
  ): Promise<BusinessLogicResult<ServiceCategory>> {
    try {
      const existingCategory = await this.categoryRepository.findById(id);
      if (!existingCategory) {
        return {
          success: false,
          error: 'Category not found',
        };
      }

      let updatePayload: Partial<ServiceCategory> = { ...updateData };

      // If name is being updated, update slug too
      if (updateData.name && updateData.name !== existingCategory.name) {
        const newSlug = StringFormatter.slugify(updateData.name);
        
        // Check if new slug already exists
        const existingWithSlug = await this.categoryRepository.findBySlug(newSlug);
        if (existingWithSlug && existingWithSlug.id !== id) {
          return {
            success: false,
            error: 'Category with this name already exists',
          };
        }

        updatePayload.slug = newSlug;
        updatePayload.seoMetadata = {
          ...existingCategory.seoMetadata,
          title: `${updateData.name} - Printing Services`,
          keywords: [updateData.name.toLowerCase(), 'printing', 'services'],
        };
      }

      // Update description in SEO metadata if changed
      if (updateData.description) {
        updatePayload.seoMetadata = {
          ...existingCategory.seoMetadata,
          ...updatePayload.seoMetadata,
          description: updateData.description,
        };
      }

      const updatedCategory = await this.categoryRepository.update(id, updatePayload);

      return {
        success: true,
        data: updatedCategory,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update category',
      };
    }
  }

  // Delete category (Admin only) (SRP)
  async deleteCategory(id: string): Promise<BusinessLogicResult<void>> {
    try {
      const existingCategory = await this.categoryRepository.findById(id);
      if (!existingCategory) {
        return {
          success: false,
          error: 'Category not found',
        };
      }

      // Check if category has subcategories or services
      // This would require additional repository methods to check dependencies
      // For now, we'll assume it's safe to delete

      await this.categoryRepository.delete(id);

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete category',
      };
    }
  }

  // Reorder categories (Admin only) (SRP)
  async reorderCategories(
    categoryOrders: { id: string; sortOrder: number }[]
  ): Promise<BusinessLogicResult<void>> {
    try {
      // Update sort orders for all categories
      for (const categoryOrder of categoryOrders) {
        await this.categoryRepository.update(categoryOrder.id, {
          sortOrder: categoryOrder.sortOrder,
        });
      }

      return {
        success: true,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to reorder categories',
      };
    }
  }
}