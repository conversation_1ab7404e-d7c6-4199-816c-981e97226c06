// Prisma Schema - Database models following SOLID principles
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Base model with common fields (DRY principle)
abstract model BaseEntity {
  id        String   @id @default(cuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

// User model (SRP - Single responsibility for user data)
model User {
  id              String   @id @default(cuid())
  email           String   @unique
  password        String
  name            String
  role            UserRole @default(USER)
  isEmailVerified Boolean  @default(false)
  emailToken      String?  @unique
  resetToken      String?  @unique
  resetTokenExpiry DateTime?
  lastLogin       DateTime?
  isActive        Boolean  @default(true)
  profileImage    String?
  phone           String?
  address         String?
  city            String?
  state           String?
  zipCode         String?
  country         String?  @default("US")
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  sessions        Session[]
  services        Service[]        @relation("UserServices")
  orders          Order[]          @relation("UserOrders")
  reviews         Review[]         @relation("UserReviews")
  providerProfile ProviderProfile?
  adminProfile    AdminProfile?

  @@map("users")
}

// User roles enum
enum UserRole {
  USER
  PROVIDER
  ADMIN
}

// Session model for JWT token management (SRP)
model Session {
  id           String    @id @default(cuid())
  userId       String
  accessToken  String    @unique
  refreshToken String    @unique
  userAgent    String?
  ipAddress    String?
  isActive     Boolean   @default(true)
  expiresAt    DateTime
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// Provider profile model (SRP - Provider-specific data)
model ProviderProfile {
  id              String   @id @default(cuid())
  userId          String   @unique
  businessName    String
  description     String?
  website         String?
  businessPhone   String?
  businessAddress String?
  businessCity    String?
  businessState   String?
  businessZip     String?
  licenseNumber   String?
  insuranceNumber String?
  isVerified      Boolean  @default(false)
  verifiedAt      DateTime?
  rating          Float?   @default(0)
  totalReviews    Int      @default(0)
  totalOrders     Int      @default(0)
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relationships
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  services Service[] @relation("ProviderServices")
  orders   Order[]   @relation("ProviderOrders")

  @@map("provider_profiles")
}

// Admin profile model (SRP - Admin-specific data)
model AdminProfile {
  id          String   @id @default(cuid())
  userId      String   @unique
  permissions String[] // JSON array of permissions
  department  String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relationships
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("admin_profiles")
}

// Service category model (SRP)
model ServiceCategory {
  id          String    @id @default(cuid())
  name        String    @unique
  description String?
  icon        String?
  isActive    Boolean   @default(true)
  sortOrder   Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relationships
  services Service[] @relation("CategoryServices")

  @@map("service_categories")
}

// Service model (SRP - Service business logic)
model Service {
  id            String          @id @default(cuid())
  name          String
  description   String
  categoryId    String
  providerId    String
  userId        String
  price         Float
  priceType     ServicePriceType @default(FIXED)
  duration      Int?            // in minutes
  location      String?
  isActive      Boolean         @default(true)
  isFeatured    Boolean         @default(false)
  images        String[]        // JSON array of image URLs
  tags          String[]        // JSON array of tags
  requirements  String?         // Special requirements
  deliverables  String?         // What customer gets
  rating        Float?          @default(0)
  totalReviews  Int             @default(0)
  totalOrders   Int             @default(0)
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  // Relationships
  category ServiceCategory   @relation("CategoryServices", fields: [categoryId], references: [id])
  provider ProviderProfile  @relation("ProviderServices", fields: [providerId], references: [id])
  user     User             @relation("UserServices", fields: [userId], references: [id])
  orders   Order[]          @relation("ServiceOrders")
  reviews  Review[]         @relation("ServiceReviews")

  @@map("services")
}

// Service price types enum
enum ServicePriceType {
  FIXED
  HOURLY
  QUOTE
}

// Order model (SRP - Order management)
model Order {
  id              String      @id @default(cuid())
  orderNumber     String      @unique
  serviceId       String
  userId          String
  providerId      String
  status          OrderStatus @default(PENDING)
  totalAmount     Float
  paymentStatus   PaymentStatus @default(PENDING)
  paymentMethod   String?
  customerNotes   String?
  providerNotes   String?
  scheduledDate   DateTime?
  completedDate   DateTime?
  cancelledDate   DateTime?
  cancellationReason String?
  files           String[]    // JSON array of file URLs
  createdAt       DateTime    @default(now())
  updatedAt       DateTime    @updatedAt

  // Relationships
  service  Service         @relation("ServiceOrders", fields: [serviceId], references: [id])
  user     User            @relation("UserOrders", fields: [userId], references: [id])
  provider ProviderProfile @relation("ProviderOrders", fields: [providerId], references: [id])
  review   Review?         @relation("OrderReview")

  @@map("orders")
}

// Order status enum
enum OrderStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
  REFUNDED
}

// Payment status enum
enum PaymentStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  REFUNDED
}

// Review model (SRP - Review system)
model Review {
  id         String   @id @default(cuid())
  orderId    String   @unique
  serviceId  String
  userId     String
  rating     Int      // 1-5 stars
  comment    String?
  isPublic   Boolean  @default(true)
  isVerified Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relationships
  order   Order   @relation("OrderReview", fields: [orderId], references: [id])
  service Service @relation("ServiceReviews", fields: [serviceId], references: [id])
  user    User    @relation("UserReviews", fields: [userId], references: [id])

  @@map("reviews")
}

// File upload model (SRP - File management)
model FileUpload {
  id           String   @id @default(cuid())
  fileName     String
  originalName String
  mimeType     String
  size         Int
  url          String
  key          String?  // S3 key or file path
  uploadedBy   String?  // User ID
  isPublic     Boolean  @default(false)
  metadata     Json?    // Additional file metadata
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("file_uploads")
}

// System settings model (SRP - Configuration management)
model SystemSetting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  category  String?
  isPublic  Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_settings")
}

// Email log model (SRP - Email tracking)
model EmailLog {
  id         String      @id @default(cuid())
  recipient  String
  subject    String
  type       EmailType
  status     EmailStatus @default(PENDING)
  sentAt     DateTime?
  error      String?
  retryCount Int         @default(0)
  metadata   Json?
  createdAt  DateTime    @default(now())
  updatedAt  DateTime    @updatedAt

  @@map("email_logs")
}

// Email types enum
enum EmailType {
  VERIFICATION
  PASSWORD_RESET
  WELCOME
  ORDER_NOTIFICATION
  PROVIDER_APPLICATION
  SYSTEM_NOTIFICATION
}

// Email status enum
enum EmailStatus {
  PENDING
  SENT
  FAILED
  BOUNCED
}