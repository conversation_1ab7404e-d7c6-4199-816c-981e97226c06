"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrderModel = exports.OrderFiles = exports.OrderTimeline = exports.OrderAmount = exports.OrderNumber = void 0;
// Order Domain Model - Manages order lifecycle and business rules
const client_1 = require("@prisma/client");
const BaseModel_1 = require("../base/BaseModel");
const validation_schemas_1 = require("../schemas/validation.schemas");
// Value Objects for Order domain
class OrderNumber extends BaseModel_1.ValueObject {
    constructor(value) {
        super();
        this.value = value;
        this.validate();
    }
    validate() {
        if (!this.value || this.value.trim().length === 0) {
            throw new Error('Order number cannot be empty');
        }
        // Order number format: ORD-YYYYMMDD-XXXXX
        const orderRegex = /^ORD-\d{8}-\d{5}$/;
        if (!orderRegex.test(this.value)) {
            throw new Error('Invalid order number format');
        }
    }
    getValue() {
        return this.value;
    }
    static generate() {
        const date = new Date();
        const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
        const random = Math.floor(Math.random() * 100000).toString().padStart(5, '0');
        return new OrderNumber(`ORD-${dateStr}-${random}`);
    }
    getEqualityComponents() {
        return [this.value];
    }
}
exports.OrderNumber = OrderNumber;
class OrderAmount extends BaseModel_1.ValueObject {
    constructor(amount) {
        super();
        this.amount = amount;
        this.validate();
    }
    validate() {
        if (this.amount <= 0) {
            throw new Error('Order amount must be positive');
        }
        if (this.amount > 100000) {
            throw new Error('Order amount cannot exceed $100,000');
        }
    }
    getAmount() {
        return this.amount;
    }
    getFormattedAmount() {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
        }).format(this.amount);
    }
    calculateTax(taxRate = 0.08) {
        return Math.round(this.amount * taxRate * 100) / 100;
    }
    calculateTotal(taxRate = 0.08) {
        return this.amount + this.calculateTax(taxRate);
    }
    getEqualityComponents() {
        return [this.amount];
    }
}
exports.OrderAmount = OrderAmount;
class OrderTimeline extends BaseModel_1.ValueObject {
    constructor(status, scheduledDate, completedDate, cancelledDate) {
        super();
        this.status = status;
        this.scheduledDate = scheduledDate;
        this.completedDate = completedDate;
        this.cancelledDate = cancelledDate;
        this.validate();
    }
    validate() {
        const now = new Date();
        // Completed orders must have completion date
        if (this.status === client_1.OrderStatus.COMPLETED && !this.completedDate) {
            throw new Error('Completed orders must have a completion date');
        }
        // Cancelled orders must have cancellation date
        if (this.status === client_1.OrderStatus.CANCELLED && !this.cancelledDate) {
            throw new Error('Cancelled orders must have a cancellation date');
        }
        // Completion date cannot be in the future
        if (this.completedDate && this.completedDate > now) {
            throw new Error('Completion date cannot be in the future');
        }
        // Scheduled date should be in the future for pending orders
        if (this.status === client_1.OrderStatus.PENDING && this.scheduledDate && this.scheduledDate < now) {
            // Allow some tolerance for recently passed dates
            const hourAgo = new Date(now.getTime() - (60 * 60 * 1000));
            if (this.scheduledDate < hourAgo) {
                throw new Error('Scheduled date for pending orders should be in the future');
            }
        }
    }
    getStatus() {
        return this.status;
    }
    getScheduledDate() {
        return this.scheduledDate;
    }
    getCompletedDate() {
        return this.completedDate;
    }
    getCancelledDate() {
        return this.cancelledDate;
    }
    isOverdue() {
        if (!this.scheduledDate)
            return false;
        if (this.status === client_1.OrderStatus.COMPLETED || this.status === client_1.OrderStatus.CANCELLED)
            return false;
        return new Date() > this.scheduledDate;
    }
    getDaysUntilScheduled() {
        if (!this.scheduledDate)
            return 0;
        const now = new Date();
        const diffMs = this.scheduledDate.getTime() - now.getTime();
        return Math.ceil(diffMs / (1000 * 60 * 60 * 24));
    }
    getProcessingTime() {
        if (!this.completedDate)
            return undefined;
        // Calculate from creation to completion
        const createdAt = new Date(); // This would come from the order creation date
        const diffMs = this.completedDate.getTime() - createdAt.getTime();
        return Math.ceil(diffMs / (1000 * 60 * 60)); // Hours
    }
    canTransitionTo(newStatus) {
        const validTransitions = {
            [client_1.OrderStatus.PENDING]: [client_1.OrderStatus.CONFIRMED, client_1.OrderStatus.CANCELLED],
            [client_1.OrderStatus.CONFIRMED]: [client_1.OrderStatus.IN_PROGRESS, client_1.OrderStatus.CANCELLED],
            [client_1.OrderStatus.IN_PROGRESS]: [client_1.OrderStatus.COMPLETED, client_1.OrderStatus.CANCELLED],
            [client_1.OrderStatus.COMPLETED]: [client_1.OrderStatus.REFUNDED],
            [client_1.OrderStatus.CANCELLED]: [client_1.OrderStatus.REFUNDED],
            [client_1.OrderStatus.REFUNDED]: [], // Terminal state
        };
        return validTransitions[this.status].includes(newStatus);
    }
    getEqualityComponents() {
        return [this.status, this.scheduledDate, this.completedDate, this.cancelledDate];
    }
}
exports.OrderTimeline = OrderTimeline;
class OrderFiles extends BaseModel_1.ValueObject {
    constructor(files) {
        super();
        this.files = files;
        this.validate();
    }
    validate() {
        if (this.files.length > 10) {
            throw new Error('Maximum 10 files allowed per order');
        }
        this.files.forEach(file => {
            if (!/^https?:\/\/.+/.test(file)) {
                throw new Error('All files must be valid URLs');
            }
        });
    }
    getFiles() {
        return [...this.files];
    }
    hasFiles() {
        return this.files.length > 0;
    }
    getFileCount() {
        return this.files.length;
    }
    getEqualityComponents() {
        return [this.files.sort()];
    }
}
exports.OrderFiles = OrderFiles;
// Order Domain Model
class OrderModel extends BaseModel_1.BaseModel {
    constructor(data) {
        super(data);
        this._validationSchema = validation_schemas_1.orderSchema;
        this.orderNumber = new OrderNumber(data.orderNumber);
        this.amount = new OrderAmount(data.totalAmount);
        this.timeline = new OrderTimeline(data.status, data.scheduledDate || undefined, data.completedDate || undefined, data.cancelledDate || undefined);
        this.files = new OrderFiles(data.files);
    }
    // Business Logic Methods
    getOrderNumber() {
        return this.orderNumber;
    }
    getServiceId() {
        return this._data.serviceId;
    }
    getUserId() {
        return this._data.userId;
    }
    getProviderId() {
        return this._data.providerId;
    }
    getAmount() {
        return this.amount;
    }
    getTimeline() {
        return this.timeline;
    }
    getFiles() {
        return this.files;
    }
    getPaymentStatus() {
        return this._data.paymentStatus;
    }
    getPaymentMethod() {
        return this._data.paymentMethod || undefined;
    }
    getCustomerNotes() {
        return this._data.customerNotes || undefined;
    }
    getProviderNotes() {
        return this._data.providerNotes || undefined;
    }
    getCancellationReason() {
        return this._data.cancellationReason || undefined;
    }
    isActive() {
        return ![client_1.OrderStatus.COMPLETED, client_1.OrderStatus.CANCELLED, client_1.OrderStatus.REFUNDED].includes(this._data.status);
    }
    canBeCancelled() {
        return this.isActive() && this._data.status !== client_1.OrderStatus.IN_PROGRESS;
    }
    canBeRefunded() {
        return [client_1.OrderStatus.COMPLETED, client_1.OrderStatus.CANCELLED].includes(this._data.status) &&
            this._data.paymentStatus === client_1.PaymentStatus.COMPLETED;
    }
    requiresProviderAction() {
        return [client_1.OrderStatus.PENDING, client_1.OrderStatus.CONFIRMED].includes(this._data.status);
    }
    requiresCustomerAction() {
        return this._data.status === client_1.OrderStatus.PENDING && this._data.paymentStatus === client_1.PaymentStatus.PENDING;
    }
    // Business Actions
    updateStatus(newStatus, notes, performedBy) {
        if (!this.timeline.canTransitionTo(newStatus)) {
            return BaseModel_1.Result.failure(`Cannot transition from ${this._data.status} to ${newStatus}`);
        }
        try {
            const now = new Date();
            let scheduledDate = this.timeline.getScheduledDate();
            let completedDate = this.timeline.getCompletedDate();
            let cancelledDate = this.timeline.getCancelledDate();
            // Set appropriate dates based on status
            if (newStatus === client_1.OrderStatus.COMPLETED) {
                completedDate = now;
            }
            else if (newStatus === client_1.OrderStatus.CANCELLED) {
                cancelledDate = now;
            }
            const newTimeline = new OrderTimeline(newStatus, scheduledDate, completedDate, cancelledDate);
            this.timeline = newTimeline;
            this.updateData({
                ...this._data,
                status: newStatus,
                completedDate: completedDate || null,
                cancelledDate: cancelledDate || null,
                providerNotes: notes || this._data.providerNotes,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'OrderStatusChanged',
                aggregateId: this.id,
                data: {
                    orderId: this.id,
                    orderNumber: this.orderNumber.getValue(),
                    previousStatus: this._data.status,
                    newStatus,
                    performedBy,
                    notes,
                    timestamp: now
                },
                timestamp: now,
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update status');
        }
    }
    updatePaymentStatus(paymentStatus, paymentMethod, transactionId) {
        this.updateData({
            ...this._data,
            paymentStatus,
            paymentMethod: paymentMethod || this._data.paymentMethod,
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'OrderPaymentUpdated',
            aggregateId: this.id,
            data: {
                orderId: this.id,
                orderNumber: this.orderNumber.getValue(),
                paymentStatus,
                paymentMethod,
                transactionId,
                amount: this.amount.getAmount()
            },
            timestamp: new Date(),
            version: 1,
        });
        return BaseModel_1.Result.success(undefined);
    }
    updateScheduledDate(scheduledDate) {
        try {
            const newTimeline = new OrderTimeline(this._data.status, scheduledDate, this.timeline.getCompletedDate(), this.timeline.getCancelledDate());
            this.timeline = newTimeline;
            this.updateData({
                ...this._data,
                scheduledDate,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'OrderScheduleUpdated',
                aggregateId: this.id,
                data: {
                    orderId: this.id,
                    orderNumber: this.orderNumber.getValue(),
                    scheduledDate
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to update scheduled date');
        }
    }
    addFiles(newFiles) {
        try {
            const allFiles = [...this.files.getFiles(), ...newFiles];
            const files = new OrderFiles(allFiles);
            this.files = files;
            this.updateData({
                ...this._data,
                files: allFiles,
            });
            BaseModel_1.DomainEventPublisher.publish({
                eventType: 'OrderFilesAdded',
                aggregateId: this.id,
                data: {
                    orderId: this.id,
                    orderNumber: this.orderNumber.getValue(),
                    newFiles,
                    totalFiles: allFiles.length
                },
                timestamp: new Date(),
                version: 1,
            });
            return BaseModel_1.Result.success(undefined);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to add files');
        }
    }
    cancel(reason, cancelledBy) {
        if (!this.canBeCancelled()) {
            return BaseModel_1.Result.failure('Order cannot be cancelled in its current state');
        }
        const result = this.updateStatus(client_1.OrderStatus.CANCELLED, reason, cancelledBy);
        if (result.isFailure) {
            return result;
        }
        this.updateData({
            ...this._data,
            cancellationReason: reason,
        });
        BaseModel_1.DomainEventPublisher.publish({
            eventType: 'OrderCancelled',
            aggregateId: this.id,
            data: {
                orderId: this.id,
                orderNumber: this.orderNumber.getValue(),
                reason,
                cancelledBy,
                refundAmount: this.canBeRefunded() ? this.amount.getAmount() : 0
            },
            timestamp: new Date(),
            version: 1,
        });
        return BaseModel_1.Result.success(undefined);
    }
    complete(completedBy, notes) {
        if (this._data.status !== client_1.OrderStatus.IN_PROGRESS) {
            return BaseModel_1.Result.failure('Only in-progress orders can be completed');
        }
        return this.updateStatus(client_1.OrderStatus.COMPLETED, notes, completedBy);
    }
    updateNotes(customerNotes, providerNotes) {
        if (customerNotes && customerNotes.length > 1000) {
            return BaseModel_1.Result.failure('Customer notes cannot exceed 1000 characters');
        }
        if (providerNotes && providerNotes.length > 1000) {
            return BaseModel_1.Result.failure('Provider notes cannot exceed 1000 characters');
        }
        this.updateData({
            ...this._data,
            customerNotes: customerNotes ?? this._data.customerNotes,
            providerNotes: providerNotes ?? this._data.providerNotes,
        });
        return BaseModel_1.Result.success(undefined);
    }
    // Business Rule Validation
    validateBusinessRules() {
        const errors = [];
        // Basic validation
        if (!this._data.serviceId) {
            errors.push('Order must be associated with a service');
        }
        if (!this._data.userId) {
            errors.push('Order must be associated with a user');
        }
        if (!this._data.providerId) {
            errors.push('Order must be associated with a provider');
        }
        // Payment validation
        if (this._data.status === client_1.OrderStatus.CONFIRMED && this._data.paymentStatus === client_1.PaymentStatus.FAILED) {
            errors.push('Confirmed orders cannot have failed payment status');
        }
        // Timeline validation
        if (this.timeline.isOverdue() && this.isActive()) {
            errors.push('Active orders should not be overdue');
        }
        return errors;
    }
    // Static Factory Methods
    static createOrder(orderData) {
        try {
            // Generate order number
            const orderNumber = OrderNumber.generate();
            // Validate amount
            const amount = new OrderAmount(orderData.totalAmount);
            // Validate timeline
            const timeline = new OrderTimeline(client_1.OrderStatus.PENDING, orderData.scheduledDate);
            // Validate files
            const files = new OrderFiles(orderData.files || []);
            const now = new Date();
            const order = new OrderModel({
                id: '', // Will be set by the database
                orderNumber: orderNumber.getValue(),
                serviceId: orderData.serviceId,
                userId: orderData.userId,
                providerId: orderData.providerId,
                status: client_1.OrderStatus.PENDING,
                totalAmount: orderData.totalAmount,
                paymentStatus: client_1.PaymentStatus.PENDING,
                paymentMethod: null,
                customerNotes: orderData.customerNotes || null,
                providerNotes: null,
                scheduledDate: orderData.scheduledDate || null,
                completedDate: null,
                cancelledDate: null,
                cancellationReason: null,
                files: files.getFiles(),
                createdAt: now,
                updatedAt: now,
            });
            const validation = order.validate();
            if (!validation.isValid) {
                return BaseModel_1.Result.failure(`Validation failed: ${validation.errors.join(', ')}`);
            }
            return BaseModel_1.Result.success(order);
        }
        catch (error) {
            return BaseModel_1.Result.failure(error instanceof Error ? error.message : 'Failed to create order');
        }
    }
    static fromPrismaOrder(prismaOrder) {
        return new OrderModel(prismaOrder);
    }
    // Serialization methods for API responses
    toPublicObject() {
        let requiresAction = 'none';
        if (this.requiresCustomerAction())
            requiresAction = 'customer';
        else if (this.requiresProviderAction())
            requiresAction = 'provider';
        return {
            ...this._data,
            formattedAmount: this.amount.getFormattedAmount(),
            isOverdue: this.timeline.isOverdue(),
            canBeCancelled: this.canBeCancelled(),
            canBeRefunded: this.canBeRefunded(),
            requiresAction,
            daysUntilScheduled: this.timeline.getDaysUntilScheduled(),
        };
    }
    toCustomerView() {
        return {
            id: this._data.id,
            orderNumber: this.orderNumber.getValue(),
            status: this._data.status,
            totalAmount: this.amount.getAmount(),
            formattedAmount: this.amount.getFormattedAmount(),
            paymentStatus: this._data.paymentStatus,
            scheduledDate: this.timeline.getScheduledDate(),
            completedDate: this.timeline.getCompletedDate(),
            customerNotes: this._data.customerNotes || undefined,
            files: this.files.getFiles(),
            isOverdue: this.timeline.isOverdue(),
            canBeCancelled: this.canBeCancelled(),
            daysUntilScheduled: this.timeline.getDaysUntilScheduled(),
        };
    }
    toProviderView() {
        return {
            id: this._data.id,
            orderNumber: this.orderNumber.getValue(),
            status: this._data.status,
            totalAmount: this.amount.getAmount(),
            formattedAmount: this.amount.getFormattedAmount(),
            paymentStatus: this._data.paymentStatus,
            scheduledDate: this.timeline.getScheduledDate(),
            customerNotes: this._data.customerNotes || undefined,
            providerNotes: this._data.providerNotes || undefined,
            files: this.files.getFiles(),
            requiresAction: this.requiresProviderAction(),
            isOverdue: this.timeline.isOverdue(),
        };
    }
}
exports.OrderModel = OrderModel;
//# sourceMappingURL=Order.model.js.map