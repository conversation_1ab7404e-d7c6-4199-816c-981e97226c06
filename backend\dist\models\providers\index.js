"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProviderRating = exports.VerificationInfo = exports.BusinessAddress = exports.BusinessInfo = exports.ProviderProfileModel = void 0;
// Provider Models Index
var ProviderProfile_model_1 = require("./ProviderProfile.model");
Object.defineProperty(exports, "ProviderProfileModel", { enumerable: true, get: function () { return ProviderProfile_model_1.ProviderProfileModel; } });
Object.defineProperty(exports, "BusinessInfo", { enumerable: true, get: function () { return ProviderProfile_model_1.BusinessInfo; } });
Object.defineProperty(exports, "BusinessAddress", { enumerable: true, get: function () { return ProviderProfile_model_1.BusinessAddress; } });
Object.defineProperty(exports, "VerificationInfo", { enumerable: true, get: function () { return ProviderProfile_model_1.VerificationInfo; } });
Object.defineProperty(exports, "ProviderRating", { enumerable: true, get: function () { return ProviderProfile_model_1.ProviderRating; } });
//# sourceMappingURL=index.js.map