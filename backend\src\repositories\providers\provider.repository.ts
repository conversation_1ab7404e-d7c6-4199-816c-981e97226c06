// Provider Repository - Following established patterns and SOLID principles
import {PrismaClient, ProviderProfile, User} from '@prisma/client';
import {BaseRepository} from '@/repositories/base/base.repository';
import {
	CreateProviderRequest,
	UpdateProviderRequest,
	ProviderSearchRequest,
	ProviderFilters,
	ProviderSearchResult,
	ProviderWithMetrics,
	ProviderMetrics,
	ProviderStatistics,
} from '@/types/providers';
import {PaginatedResult} from '@/types/common';

// Provider Repository Interface (DIP - Dependency Inversion Principle)
export interface IProviderRepository {
	create(
		providerData: CreateProviderRequest,
		userId: string
	): Promise<ProviderProfile>;
	findById(id: string): Promise<ProviderProfile | null>;
	findByUserId(userId: string): Promise<ProviderProfile | null>;
	findByBusinessName(businessName: string): Promise<ProviderProfile | null>;
	update(
		id: string,
		providerData: UpdateProviderRequest
	): Promise<ProviderProfile>;
	delete(id: string): Promise<void>;
	search(params: ProviderSearchRequest): Promise<ProviderSearchResult>;
	getVerifiedProviders(limit?: number): Promise<ProviderProfile[]>;
	getTopRatedProviders(limit?: number): Promise<ProviderProfile[]>;
	getProviderMetrics(providerId: string): Promise<ProviderMetrics>;
	getProviderStatistics(): Promise<ProviderStatistics>;
	updateRating(providerId: string, newRating: number): Promise<void>;
	incrementOrderCount(providerId: string): Promise<void>;
	incrementReviewCount(providerId: string): Promise<void>;
	verifyProvider(providerId: string, verifiedBy: string): Promise<void>;
	deactivateProvider(
		providerId: string,
		deactivatedBy: string,
		reason?: string
	): Promise<void>;
}

// Provider Repository Implementation (SRP - Single Responsibility Principle)
export class ProviderRepository
	extends BaseRepository
	implements IProviderRepository
{
	constructor(private prisma: PrismaClient) {
		super();
	}

	// Create a new provider profile (SRP)
	async create(
		providerData: CreateProviderRequest,
		userId: string
	): Promise<ProviderProfile> {
		return this.prisma.providerProfile.create({
			data: {
				userId,
				businessName: providerData.businessName,
				description: providerData.description,
				website: providerData.website,
				businessPhone: providerData.businessPhone,
				businessAddress: providerData.businessAddress,
				businessCity: providerData.businessCity,
				businessState: providerData.businessState,
				businessZip: providerData.businessZip,
				licenseNumber: providerData.licenseNumber,
				insuranceNumber: providerData.insuranceNumber,
				isVerified: false,
				rating: 0,
				totalReviews: 0,
				totalOrders: 0,
				isActive: true,
			},
		});
	}

	// Find provider by ID (SRP)
	async findById(id: string): Promise<ProviderProfile | null> {
		return this.prisma.providerProfile.findUnique({
			where: {id},
			include: {
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
			},
		});
	}

	// Find provider by user ID (SRP)
	async findByUserId(userId: string): Promise<ProviderProfile | null> {
		return this.prisma.providerProfile.findUnique({
			where: {userId},
			include: {
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
			},
		});
	}

	// Find provider by business name (SRP)
	async findByBusinessName(
		businessName: string
	): Promise<ProviderProfile | null> {
		return this.prisma.providerProfile.findFirst({
			where: {businessName},
		});
	}

	// Update provider profile (SRP)
	async update(
		id: string,
		providerData: UpdateProviderRequest
	): Promise<ProviderProfile> {
		return this.prisma.providerProfile.update({
			where: {id},
			data: {
				businessName: providerData.businessName,
				description: providerData.description,
				website: providerData.website,
				businessPhone: providerData.businessPhone,
				businessAddress: providerData.businessAddress,
				businessCity: providerData.businessCity,
				businessState: providerData.businessState,
				businessZip: providerData.businessZip,
				licenseNumber: providerData.licenseNumber,
				insuranceNumber: providerData.insuranceNumber,
				isActive: providerData.isActive,
			},
		});
	}

	// Delete provider profile (SRP)
	async delete(id: string): Promise<void> {
		await this.prisma.providerProfile.delete({
			where: {id},
		});
	}

	// Search providers with filters and pagination (SRP)
	async search(params: ProviderSearchRequest): Promise<ProviderSearchResult> {
		const {query, filters, pagination} = params;
		const page = pagination?.page || 1;
		const limit = pagination?.limit || 10;
		const skip = (page - 1) * limit;

		// Build where clause based on filters
		const where: any = {
			isActive: true,
		};

		if (query) {
			where.OR = [
				{businessName: {contains: query, mode: 'insensitive'}},
				{description: {contains: query, mode: 'insensitive'}},
				{businessCity: {contains: query, mode: 'insensitive'}},
				{businessState: {contains: query, mode: 'insensitive'}},
			];
		}

		if (filters) {
			if (filters.isVerified !== undefined) {
				where.isVerified = filters.isVerified;
			}
			if (filters.minRating) {
				where.rating = {gte: filters.minRating};
			}
			if (filters.maxRating) {
				where.rating = {...where.rating, lte: filters.maxRating};
			}
		}

		// Get total count
		const total = await this.prisma.providerProfile.count({where});

		// Get providers with pagination
		const providers = await this.prisma.providerProfile.findMany({
			where,
			skip,
			take: limit,
			include: {
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
				services: {
					select: {
						id: true,
						name: true,
						price: true,
						rating: true,
						totalOrders: true,
					},
				},
				orders: {
					select: {
						id: true,
						status: true,
						totalAmount: true,
						createdAt: true,
					},
					orderBy: {createdAt: 'desc'},
					take: 5,
				},
			},
			orderBy: this.buildOrderBy(filters),
		});

		// Calculate metrics for each provider
		const providersWithMetrics = await Promise.all(
			providers.map(async (provider) => {
				const metrics = await this.getProviderMetrics(provider.id);
				return {
					...provider,
					metrics,
				};
			})
		);

		return {
			providers: providersWithMetrics,
			total,
			page,
			limit,
			totalPages: Math.ceil(total / limit),
			hasNext: page < Math.ceil(total / limit),
			hasPrev: page > 1,
		};
	}

	// Get verified providers (SRP)
	async getVerifiedProviders(limit: number = 10): Promise<ProviderProfile[]> {
		return this.prisma.providerProfile.findMany({
			where: {
				isVerified: true,
				isActive: true,
			},
			take: limit,
			orderBy: {rating: 'desc'},
			include: {
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
			},
		});
	}

	// Get top rated providers (SRP)
	async getTopRatedProviders(limit: number = 10): Promise<ProviderProfile[]> {
		return this.prisma.providerProfile.findMany({
			where: {
				isActive: true,
				rating: {gt: 0},
			},
			take: limit,
			orderBy: {rating: 'desc'},
			include: {
				user: {
					select: {
						id: true,
						name: true,
						email: true,
						profileImage: true,
					},
				},
			},
		});
	}

	// Get provider metrics (SRP)
	async getProviderMetrics(providerId: string): Promise<ProviderMetrics> {
		const [services, orders, reviews, revenue, topServices] = await Promise.all(
			[
				// Get services count
				this.prisma.service.count({
					where: {providerId},
				}),
				// Get active services count
				this.prisma.service.count({
					where: {providerId, isActive: true},
				}),
				// Get orders statistics
				this.prisma.order.groupBy({
					by: ['status'],
					where: {providerId},
					_count: {id: true},
					_sum: {totalAmount: true},
				}),
				// Get reviews
				this.prisma.review.findMany({
					where: {
						service: {providerId},
					},
					select: {
						rating: true,
						createdAt: true,
					},
				}),
				// Get top services by order count
				this.prisma.service.findMany({
					where: {providerId},
					select: {
						id: true,
						name: true,
						totalOrders: true,
						price: true,
					},
					orderBy: {totalOrders: 'desc'},
					take: 5,
				}),
			]
		);

		// Calculate metrics
		const totalOrders = orders.reduce((sum, order) => sum + order._count.id, 0);
		const completedOrders =
			orders.find((o) => o.status === 'COMPLETED')?._count.id || 0;
		const cancelledOrders =
			orders.find((o) => o.status === 'CANCELLED')?._count.id || 0;
		const totalRevenue = orders.reduce(
			(sum, order) => sum + (order._sum.totalAmount || 0),
			0
		);
		const averageRating =
			reviews.length > 0
				? reviews.reduce((sum, review) => sum + review.rating, 0) /
				  reviews.length
				: 0;

		return {
			totalServices: services,
			activeServices: services, // Simplified for now
			totalOrders,
			completedOrders,
			cancelledOrders,
			averageRating,
			totalReviews: reviews.length,
			responseRate: 95, // Placeholder - would need to calculate from actual data
			averageResponseTime: 2, // Placeholder - would need to calculate from actual data
			completionRate:
				totalOrders > 0 ? (completedOrders / totalOrders) * 100 : 0,
			revenue: {
				total: totalRevenue,
				thisMonth: totalRevenue * 0.3, // Placeholder
				lastMonth: totalRevenue * 0.25, // Placeholder
			},
			topServices: topServices.map((service) => ({
				serviceId: service.id,
				serviceName: service.name,
				orderCount: service.totalOrders,
				revenue: service.totalOrders * service.price,
			})),
		};
	}

	// Get provider statistics (SRP)
	async getProviderStatistics(): Promise<ProviderStatistics> {
		const [
			totalProviders,
			verifiedProviders,
			activeProviders,
			averageRating,
			topSpecialties,
			topServiceAreas,
		] = await Promise.all([
			this.prisma.providerProfile.count(),
			this.prisma.providerProfile.count({where: {isVerified: true}}),
			this.prisma.providerProfile.count({where: {isActive: true}}),
			this.prisma.providerProfile.aggregate({
				_avg: {rating: true},
			}),
			// Placeholder for top specialties - would need to implement based on actual data structure
			Promise.resolve([]),
			// Placeholder for top service areas - would need to implement based on actual data structure
			Promise.resolve([]),
		]);

		return {
			totalProviders,
			verifiedProviders,
			activeProviders,
			averageRating: averageRating._avg.rating || 0,
			topSpecialties,
			topServiceAreas,
			verificationRate:
				totalProviders > 0 ? (verifiedProviders / totalProviders) * 100 : 0,
			averageResponseTime: 2, // Placeholder
		};
	}

	// Update provider rating (SRP)
	async updateRating(providerId: string, newRating: number): Promise<void> {
		await this.prisma.providerProfile.update({
			where: {id: providerId},
			data: {rating: newRating},
		});
	}

	// Increment order count (SRP)
	async incrementOrderCount(providerId: string): Promise<void> {
		await this.prisma.providerProfile.update({
			where: {id: providerId},
			data: {
				totalOrders: {
					increment: 1,
				},
			},
		});
	}

	// Increment review count (SRP)
	async incrementReviewCount(providerId: string): Promise<void> {
		await this.prisma.providerProfile.update({
			where: {id: providerId},
			data: {
				totalReviews: {
					increment: 1,
				},
			},
		});
	}

	// Verify provider (SRP)
	async verifyProvider(providerId: string, verifiedBy: string): Promise<void> {
		await this.prisma.providerProfile.update({
			where: {id: providerId},
			data: {
				isVerified: true,
				verifiedAt: new Date(),
			},
		});
	}

	// Deactivate provider (SRP)
	async deactivateProvider(
		providerId: string,
		deactivatedBy: string,
		reason?: string
	): Promise<void> {
		await this.prisma.providerProfile.update({
			where: {id: providerId},
			data: {
				isActive: false,
			},
		});
	}

	// Helper method to build order by clause (SRP)
	private buildOrderBy(filters?: ProviderFilters): any {
		if (!filters?.sortBy) {
			return {createdAt: 'desc'};
		}

		const order = filters.sortOrder || 'desc';

		switch (filters.sortBy) {
			case 'businessName':
				return {businessName: order};
			case 'rating':
				return {rating: order};
			case 'totalOrders':
				return {totalOrders: order};
			case 'responseTime':
				return {createdAt: order}; // Placeholder - would need actual response time field
			case 'yearsOfExperience':
				return {createdAt: order}; // Placeholder - would need actual experience field
			case 'createdAt':
				return {createdAt: order};
			default:
				return {createdAt: 'desc'};
		}
	}
}
