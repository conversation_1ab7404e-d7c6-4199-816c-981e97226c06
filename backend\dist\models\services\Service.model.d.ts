import { Service as PrismaService, ServicePriceType } from '@prisma/client';
import { BaseModel, Result, ValueObject } from '../base/BaseModel';
export declare class ServicePrice extends ValueObject {
    private readonly amount;
    private readonly type;
    constructor(amount: number, type: ServicePriceType);
    private validate;
    getAmount(): number;
    getType(): ServicePriceType;
    getFormattedPrice(): string;
    calculateEstimate(quantity?: number, hours?: number): number;
    protected getEqualityComponents(): any[];
}
export declare class ServiceRating extends ValueObject {
    private readonly rating;
    private readonly totalReviews;
    constructor(rating: number, totalReviews: number);
    private validate;
    getRating(): number;
    getTotalReviews(): number;
    getStars(): string;
    getDisplayRating(): string;
    isHighlyRated(): boolean;
    protected getEqualityComponents(): any[];
}
export declare class ServiceMetadata extends ValueObject {
    private readonly images;
    private readonly tags;
    private readonly requirements?;
    private readonly deliverables?;
    constructor(images: string[], tags: string[], requirements?: string | undefined, deliverables?: string | undefined);
    private validate;
    getImages(): string[];
    getTags(): string[];
    getRequirements(): string | undefined;
    getDeliverables(): string | undefined;
    getPrimaryImage(): string | undefined;
    hasTag(tag: string): boolean;
    protected getEqualityComponents(): any[];
}
export declare class ServiceModel extends BaseModel<PrismaService> {
    private price;
    private rating;
    private metadata;
    constructor(data: PrismaService);
    getName(): string;
    getDescription(): string;
    getPrice(): ServicePrice;
    getRating(): ServiceRating;
    getMetadata(): ServiceMetadata;
    getCategoryId(): string;
    getProviderId(): string;
    getUserId(): string;
    getDuration(): number | undefined;
    getLocation(): string | undefined;
    isActive(): boolean;
    isFeatured(): boolean;
    getTotalOrders(): number;
    getConversionRate(): number;
    getPopularityScore(): number;
    isEligibleForFeaturing(): boolean;
    updateBasicInfo(updates: {
        name?: string;
        description?: string;
        location?: string;
        duration?: number;
    }): Result<void, string>;
    updatePricing(amount: number, type: ServicePriceType): Result<void, string>;
    updateMetadata(updates: {
        images?: string[];
        tags?: string[];
        requirements?: string;
        deliverables?: string;
    }): Result<void, string>;
    activate(): Result<void, string>;
    deactivate(reason?: string): Result<void, string>;
    feature(): Result<void, string>;
    unfeature(): Result<void, string>;
    recordOrder(): void;
    updateRating(newRating: number, totalReviews: number): Result<void, string>;
    protected validateBusinessRules(): string[];
    static createService(serviceData: {
        name: string;
        description: string;
        categoryId: string;
        providerId: string;
        userId: string;
        price: number;
        priceType: ServicePriceType;
        duration?: number;
        location?: string;
        images?: string[];
        tags?: string[];
        requirements?: string;
        deliverables?: string;
    }): Result<ServiceModel, string>;
    static fromPrismaService(prismaService: PrismaService): ServiceModel;
    toPublicObject(): PrismaService & {
        formattedPrice: string;
        ratingDisplay: string;
        popularityScore: number;
        isEligibleForFeaturing: boolean;
    };
    toSearchResult(): {
        id: string;
        name: string;
        description: string;
        price: number;
        formattedPrice: string;
        rating: number;
        totalReviews: number;
        totalOrders: number;
        primaryImage?: string;
        tags: string[];
        location?: string;
        isFeatured: boolean;
    };
}
//# sourceMappingURL=Service.model.d.ts.map