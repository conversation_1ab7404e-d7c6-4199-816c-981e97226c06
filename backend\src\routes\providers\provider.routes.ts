// Provider Routes - Following REST principles and proper middleware organization
import {Router} from 'express';
import {ProviderController} from '@/controllers/providers';
import {ProviderManagementService} from '@/services/providers';
import {ProviderRepository} from '@/repositories/providers';
import {ValidationChains} from '@/middleware/validation';
import {handleValidation} from '@/middleware/validation/validator';
import {
	authenticate,
	adminOnly,
	providerOrAdmin,
	optionalAuth,
} from '@/middleware/auth';
import {validatePagination} from '@/middleware/validation';
import {prisma} from '@/config/database';

const router = Router();

// Initialize dependencies (DIP - Dependency Inversion Principle)
const providerRepository = new ProviderRepository(prisma);
const providerService = new ProviderManagementService(
	providerRepository,
	// These would be injected from a DI container in a real implementation
	{} as any, // IFileUploadService
	{} as any // IEmailService
);
const providerController = new ProviderController(providerService);

// GET /api/v1/providers - Search providers (Public)
router.get(
	'/',
	optionalAuth,
	validatePagination,
	providerController.searchProviders.bind(providerController)
);

// GET /api/v1/providers/verified - Get verified providers (Public)
router.get(
	'/verified',
	providerController.getVerifiedProviders.bind(providerController)
);

// GET /api/v1/providers/top-rated - Get top rated providers (Public)
router.get(
	'/top-rated',
	providerController.getTopRatedProviders.bind(providerController)
);

// GET /api/v1/providers/stats - Get provider statistics (Admin only)
router.get(
	'/stats',
	authenticate,
	adminOnly,
	providerController.getProviderStatistics.bind(providerController)
);

// GET /api/v1/providers/me - Get current user's provider profile (Provider only)
router.get(
	'/me',
	authenticate,
	providerOrAdmin,
	providerController.getMyProviderProfile.bind(providerController)
);

// GET /api/v1/providers/:id - Get provider by ID (Public)
router.get(
	'/:id',
	optionalAuth,
	providerController.getProviderById.bind(providerController)
);

// GET /api/v1/providers/user/:userId - Get provider by user ID (Public)
router.get(
	'/user/:userId',
	optionalAuth,
	providerController.getProviderByUserId.bind(providerController)
);

// POST /api/v1/providers - Create provider profile (Authenticated users)
router.post(
	'/',
	authenticate,
	ValidationChains.createProvider(),
	handleValidation,
	providerController.createProvider.bind(providerController)
);

// PUT /api/v1/providers/:id - Update provider profile (Owner/Admin only)
router.put(
	'/:id',
	authenticate,
	ValidationChains.updateProvider(),
	handleValidation,
	providerController.updateProvider.bind(providerController)
);

// DELETE /api/v1/providers/:id - Delete provider profile (Owner/Admin only)
router.delete(
	'/:id',
	authenticate,
	providerController.deleteProvider.bind(providerController)
);

// POST /api/v1/providers/:id/verify - Verify provider (Admin only)
router.post(
	'/:id/verify',
	authenticate,
	adminOnly,
	ValidationChains.verifyProvider(),
	handleValidation,
	providerController.verifyProvider.bind(providerController)
);

// POST /api/v1/providers/:id/deactivate - Deactivate provider (Admin only)
router.post(
	'/:id/deactivate',
	authenticate,
	adminOnly,
	ValidationChains.deactivateProvider(),
	handleValidation,
	providerController.deactivateProvider.bind(providerController)
);

// POST /api/v1/providers/:id/verification-request - Submit verification request (Provider only)
router.post(
	'/:id/verification-request',
	authenticate,
	providerOrAdmin,
	ValidationChains.submitVerificationRequest(),
	handleValidation,
	providerController.submitVerificationRequest.bind(providerController)
);

export default router;
