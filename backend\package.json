{"name": "printwedit-backend", "version": "1.0.0", "description": "Backend API for PrintWeditt platform - follows SOLID principles and complements frontend architecture", "main": "dist/app.js", "scripts": {"start": "node dist/app.js", "dev": "nodemon src/app.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "migrate": "npx prisma migrate dev", "db:seed": "ts-node src/scripts/seed.ts", "db:generate": "npx prisma generate"}, "keywords": ["nodejs", "typescript", "express", "prisma", "printing", "api"], "author": "PrintWeditt Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "multer": "^1.4.5", "nodemailer": "^6.9.7", "uuid": "^9.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "@prisma/client": "^5.7.1", "prisma": "^5.7.1"}, "devDependencies": {"@types/node": "^20.10.5", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/uuid": "^9.0.7", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0"}}