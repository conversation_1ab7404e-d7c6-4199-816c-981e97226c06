// Main Application Entry Point - Following SOLID principles and clean architecture
import express from 'express';
import dotenv from 'dotenv';
import {
  corsOptions,
  rateLimiter,
  authRateLimiter,
  securityHeaders,
  compressionMiddleware,
  requestLogger,
  globalErrorHandler,
  notFoundHandler,
} from '@/middleware';
import { API_ROUTES } from '@/constants';

// Load environment variables
dotenv.config();

// Create Express application
const app = express();

// Security middleware
app.use(securityHeaders);
app.use(compressionMiddleware);

// CORS configuration
const cors = require('cors');
app.use(cors(corsOptions));

// Request parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
app.use(requestLogger);

// Rate limiting
app.use(rateLimiter);

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Server is healthy',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version || '1.0.0',
  });
});

// API versioning prefix
const apiV1 = express.Router();

// Apply stricter rate limiting to auth routes
apiV1.use('/auth', authRateLimiter);

// Route imports (following the Open/Closed principle)
import { authRoutes } from '@/routes/auth';
// import serviceRoutes from '@/routes/services';
// import providerRoutes from '@/routes/providers';
// import adminRoutes from '@/routes/admin';

// Route registration (following the Single Responsibility principle)
apiV1.use('/auth', authRoutes);
// apiV1.use('/services', serviceRoutes);
// apiV1.use('/providers', providerRoutes);
// apiV1.use('/admin', adminRoutes);

// Temporary placeholder routes for demonstration
apiV1.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'PrintWeditt API v1',
    endpoints: {
      auth: '/api/v1/auth',
      services: '/api/v1/services',
      providers: '/api/v1/providers',
      admin: '/api/v1/admin',
    },
    documentation: '/api/v1/docs',
  });
});

// Mount API routes
app.use('/api/v1', apiV1);

// 404 handler for undefined routes
app.use(notFoundHandler);

// Global error handling middleware (must be last)
app.use(globalErrorHandler);

// Start server
const PORT = process.env.PORT || 3001;
const NODE_ENV = process.env.NODE_ENV || 'development';

const server = app.listen(PORT, () => {
  console.log(`🚀 PrintWeditt Backend API running in ${NODE_ENV} mode on port ${PORT}`);
  console.log(`📋 Health check: http://localhost:${PORT}/health`);
  console.log(`🌐 API v1: http://localhost:${PORT}/api/v1`);
  
  if (NODE_ENV === 'development') {
    console.log('\n📋 Available endpoints:');
    console.log(`   Authentication: http://localhost:${PORT}${API_ROUTES.AUTH.BASE}`);
    console.log(`   Services: http://localhost:${PORT}${API_ROUTES.SERVICES.BASE}`);
    console.log(`   Providers: http://localhost:${PORT}${API_ROUTES.PROVIDERS.BASE}`);
    console.log(`   Admin: http://localhost:${PORT}${API_ROUTES.ADMIN.BASE}`);
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM signal received: closing HTTP server');
  server.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT signal received: closing HTTP server');
  server.close(() => {
    console.log('HTTP server closed');
    process.exit(0);
  });
});

export default app;