// Service Types - Enhanced from frontend service types and aligned with Prisma
import { BaseEntity } from '@/types/common';

// Re-export all service types
export * from './service.types';

// Legacy interface for backward compatibility
export interface Service extends BaseEntity {
  name: string;
  description: string;
  shortDescription?: string;
  category: string;
  subcategory?: string;
  price?: number;
  priceType: 'fixed' | 'starting_at' | 'quote_required';
  images: string[];
  providerId?: string;
  isActive: boolean;
  isFeatured: boolean;
  specifications?: ServiceSpecification[];
  customFields?: CustomField[];
  seoMetadata?: SEOMetadata;
  tags: string[];
  averageRating?: number;
  reviewCount: number;
  orderCount: number;
  estimatedDelivery?: string;
}

export interface ServiceCategory extends BaseEntity {
  name: string;
  description: string;
  slug: string;
  icon?: string;
  parentId?: string;
  subcategories?: ServiceCategory[];
  isActive: boolean;
  sortOrder: number;
  seoMetadata?: SEOMetadata;
}

export interface ServiceSpecification {
  name: string;
  value: string;
  unit?: string;
  category: 'size' | 'material' | 'color' | 'finish' | 'other';
}

export interface CustomField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'select' | 'multiselect' | 'boolean' | 'file';
  required: boolean;
  options?: string[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface SEOMetadata {
  title?: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
}

// Provider Types
export interface Provider extends BaseEntity {
  userId: string;
  businessName: string;
  description?: string;
  logo?: string;
  website?: string;
  phone?: string;
  email: string;
  address: ProviderAddress;
  businessHours: BusinessHours;
  services: string[]; // Service IDs
  specialties: string[];
  certifications: string[];
  portfolio: PortfolioItem[];
  ratings: {
    overall: number;
    quality: number;
    delivery: number;
    communication: number;
    value: number;
  };
  reviewCount: number;
  isVerified: boolean;
  isActive: boolean;
  subscription: ProviderSubscription;
  settings: ProviderSettings;
}

export interface ProviderAddress {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

export interface BusinessHours {
  monday: DayHours;
  tuesday: DayHours;
  wednesday: DayHours;
  thursday: DayHours;
  friday: DayHours;
  saturday: DayHours;
  sunday: DayHours;
}

export interface DayHours {
  isOpen: boolean;
  openTime?: string; // "09:00"
  closeTime?: string; // "17:00"
}

export interface PortfolioItem extends BaseEntity {
  title: string;
  description?: string;
  images: string[];
  serviceCategory: string;
  completedAt: Date;
  isPublic: boolean;
}

export interface ProviderSubscription extends BaseEntity {
  plan: 'free' | 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'cancelled' | 'expired' | 'suspended';
  startDate: Date;
  endDate?: Date;
  autoRenew: boolean;
  features: string[];
}

export interface ProviderSettings {
  visibility: 'public' | 'private';
  autoAcceptOrders: boolean;
  requireQuoteApproval: boolean;
  notifications: {
    newOrders: boolean;
    messages: boolean;
    reviews: boolean;
    marketing: boolean;
  };
  communicationPreferences: {
    preferredMethod: 'email' | 'phone' | 'platform';
    responseTimeCommitment: number; // hours
  };
}

// Order/Quote Types
export interface ServiceOrder extends BaseEntity {
  orderNumber: string;
  customerId: string;
  providerId: string;
  serviceId: string;
  status: 'pending' | 'quote_requested' | 'quoted' | 'accepted' | 'in_progress' | 'completed' | 'cancelled' | 'disputed';
  requirements: OrderRequirement[];
  files: string[];
  totalAmount?: number;
  quotedAmount?: number;
  quotedDelivery?: Date;
  actualDelivery?: Date;
  notes?: string;
  customerNotes?: string;
  providerNotes?: string;
  timeline: OrderTimeline[];
}

export interface OrderRequirement {
  fieldId: string;
  fieldName: string;
  value: any;
  type: 'text' | 'number' | 'select' | 'file' | 'boolean';
}

export interface OrderTimeline extends BaseEntity {
  orderId: string;
  action: string;
  description: string;
  performedBy: string;
  metadata?: Record<string, any>;
}

// Review Types
export interface ServiceReview extends BaseEntity {
  orderId: string;
  customerId: string;
  providerId: string;
  serviceId: string;
  ratings: {
    overall: number;
    quality: number;
    delivery: number;
    communication: number;
    value: number;
  };
  comment?: string;
  images?: string[];
  isPublic: boolean;
  response?: ReviewResponse;
}

export interface ReviewResponse extends BaseEntity {
  reviewId: string;
  providerId: string;
  comment: string;
}

// Request/Response Types
export interface CreateServiceRequest {
  name: string;
  description: string;
  shortDescription?: string;
  category: string;
  subcategory?: string;
  price?: number;
  priceType: 'fixed' | 'starting_at' | 'quote_required';
  specifications?: ServiceSpecification[];
  customFields?: CustomField[];
  tags?: string[];
  estimatedDelivery?: string;
}

export interface UpdateServiceRequest extends Partial<CreateServiceRequest> {
  isActive?: boolean;
  isFeatured?: boolean;
}

export interface ServiceSearchRequest {
  query?: string;
  category?: string;
  subcategory?: string;
  location?: string;
  priceMin?: number;
  priceMax?: number;
  tags?: string[];
  providerId?: string;
  page?: number;
  limit?: number;
  sortBy?: 'relevance' | 'price' | 'rating' | 'popular' | 'newest';
  sortOrder?: 'asc' | 'desc';
}