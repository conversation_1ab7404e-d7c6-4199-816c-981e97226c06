import React from 'react';
import { Link } from 'react-router-dom';
import { useServices } from '../contexts/ServiceContext';
import ServiceCard from '../components/ServiceCard';
import {
  CheckCircle,
  Clock,
  Shield,
  Award,
  ChevronDown,
  ChevronUp,
  Star,
  Palette,
  Lightbulb,
  Users,
  Zap,
  ArrowLeft,
  Heart,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';

const LogoDesign: React.FC = () => {
  const [openFaq, setOpenFaq] = React.useState<number | null>(null);
  const { services } = useServices();
  const [portfolioScrollPosition, setPortfolioScrollPosition] = React.useState(0);
  const portfolioScrollRef = React.useRef<HTMLDivElement>(null);

  // Filter logo design services
  const logoDesignServices = services.filter(
    (service) => service.name.toLowerCase().includes('logo') && service.isActive
  );

  const toggleFaq = (index: number) => {
    setOpenFaq(openFaq === index ? null : index);
  };

  const scrollPortfolio = (direction: 'left' | 'right') => {
    if (portfolioScrollRef.current) {
      const scrollAmount = 280; // Width of card + gap
      const currentScroll = portfolioScrollRef.current.scrollLeft;
      const newScroll = direction === 'left' 
        ? currentScroll - scrollAmount 
        : currentScroll + scrollAmount;
      
      portfolioScrollRef.current.scrollTo({
        left: newScroll,
        behavior: 'smooth'
      });
      setPortfolioScrollPosition(newScroll);
    }
  };

  const handlePortfolioScroll = () => {
    if (portfolioScrollRef.current) {
      setPortfolioScrollPosition(portfolioScrollRef.current.scrollLeft);
    }
  };
  const faqs = [
    {
      question: "What's included in a logo design package?",
      answer:
        'Our logo design package includes 3-5 initial concepts, up to 3 rounds of revisions, final files in multiple formats (PNG, JPG, SVG, AI), and a style guide with color codes and usage guidelines.',
    },
    {
      question: 'How long does the logo design process take?',
      answer:
        'The typical logo design process takes 5-7 business days from initial consultation to final delivery. This includes concept development, client feedback, revisions, and final file preparation.',
    },
    {
      question: 'What information do you need to start my logo design?',
      answer:
        "We'll need information about your business, target audience, preferred style, colors you like/dislike, any specific elements to include, and examples of logos you admire. A creative brief helps us understand your vision.",
    },
    {
      question: 'Can I request changes to my logo design?',
      answer:
        "Yes! We include up to 3 rounds of revisions in our standard package. We want to ensure you're completely satisfied with your logo before finalizing the design.",
    },
    {
      question: 'What file formats will I receive?',
      answer:
        "You'll receive your logo in multiple formats: high-resolution PNG and JPG for web use, vector files (AI, EPS, SVG) for print and scalability, and a PDF with usage guidelines.",
    },
    {
      question: 'Do you provide trademark assistance?',
      answer:
        "While we don't provide legal services, we can guide you on basic trademark considerations and recommend working with a trademark attorney if you want to protect your logo legally.",
    },
  ];

  const portfolioSamples = [
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
      title: 'Tech Startup Logo',
      description: 'Modern, minimalist design for a software company',
      reviewerName: 'Sarah Johnson',
      reviewerImage: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
      title: 'Restaurant Brand',
      description: 'Elegant logo design for fine dining establishment',
      reviewerName: 'Mike Chen',
      reviewerImage: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
      title: 'Fitness Brand',
      description: 'Dynamic logo for health and wellness company',
      reviewerName: 'Emily Rodriguez',
      reviewerImage: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75',
    },
    {
      image:
        'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
      title: 'Creative Agency',
      description: 'Bold and artistic logo for design studio',
      reviewerName: 'David Thompson',
      reviewerImage: 'https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75',
    },
  ];

  const designProcess = [
    {
      step: '01',
      title: 'Discovery & Research',
      description:
        'We start by understanding your business, target audience, and design preferences through a detailed consultation.',
    },
    {
      step: '02',
      title: 'Concept Development',
      description:
        'Our designers create 3-5 unique logo concepts based on your requirements and brand personality.',
    },
    {
      step: '03',
      title: 'Feedback & Refinement',
      description:
        'We present the concepts to you and refine your chosen design based on your feedback and preferences.',
    },
    {
      step: '04',
      title: 'Final Delivery',
      description:
        'You receive your completed logo in multiple file formats along with a comprehensive style guide.',
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Back Button */}
      <div className="bg-warm-cream py-4">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link
            to="/design-services"
            className="flex items-center text-blue-600 hover:text-blue-700 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 mr-2" />
            Back to Design Studio
          </Link>
        </div>
      </div>

      {/* Hero Section */}
      <section className="bg-white py-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold mb-6 text-gray-900">
                Professional Logo Design
              </h1>
              <p className="text-xl mb-8 text-gray-600">
                Your business deserves a standout logo. Let our expert designers
                create a memorable brand identity that represents your vision
                and connects with your audience.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center justify-center"
                >
                  Start Your Logo Project
                </Link>
                <Link
                  to="#portfolio"
                  className="border-2 border-brand-orange text-brand-orange px-8 py-3 rounded-lg font-semibold hover:bg-brand-orange hover:text-white transition-colors inline-flex items-center justify-center"
                >
                  View Portfolio
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75"
                alt="Professional Logo Design"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-purple-100 p-2 rounded-full">
                    <Palette className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Custom Design</p>
                    <p className="text-sm text-gray-600">
                      Unique to your brand
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Section */}
      <section className="py-10 bg-warm-cream">
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className="mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Your Brand's Journey Starts With a Logo
            </h2>
          </div>

          {/* Gallery Grid - 2 rows x 7 columns */}
          <div className="space-y-4">
            {/* First Row */}
            <div className="grid grid-cols-7 gap-4">
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1742071067670.jpg&w=1920&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1745165390393.jpg&w=1200&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738171224540.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733972201436.png&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789569756.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1738899260347.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1691933135035.jpg&w=1920&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Second Row */}
            <div className="grid grid-cols-7 gap-4">
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725932233225.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1726835998298.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733797188503.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1725923064812.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733789523124.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="aspect-square rounded-lg overflow-hidden">
                <img
                  src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733181112948.jpg&w=1080&q=75"
                  alt="Logo design sample"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>
{/* Logo Design Services Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Logo Design Services
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Professional logo design services to create a memorable brand identity that represents your business perfectly.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {logoDesignServices.map((service) => (
              <ServiceCard key={service.id} service={service} />
            ))}
          </div>

          {logoDesignServices.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">
                No logo design services available at the moment.
              </p>
            </div>
          )}
        </div>
      </section>
      {/* Why Choose Our Logo Design */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left Content */}
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Why Your Logo Matters
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                A great logo is more than just a pretty picture—it's the
                foundation of your brand identity and the first impression
                customers have of your business.
              </p>

              <div className="space-y-6">
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Brand Recognition
                  </h3>
                  <p className="text-gray-600">
                    A memorable logo helps customers instantly recognize and
                    remember your brand.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Professional Credibility
                  </h3>
                  <p className="text-gray-600">
                    A well-designed logo establishes trust and professionalism
                    with your audience.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Brand Differentiation
                  </h3>
                  <p className="text-gray-600">
                    Stand out from competitors with a unique logo that reflects
                    your brand personality.
                  </p>
                </div>

                <div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    Marketing Impact
                  </h3>
                  <p className="text-gray-600">
                    A strong logo enhances all your marketing materials and
                    brand communications.
                  </p>
                </div>
              </div>
            </div>

            {/* Right Image */}
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75"
                alt="Professional Logo Design Examples"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Palette className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">Expert Design</p>
                    <p className="text-sm text-gray-600">
                      Professional quality
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Design Process */}
      <section className="py-20 bg-warm-cream">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Left side - Image */}
            <div className="relative">
              <img
                src="https://weditt.com/_next/image?url=https%3A%2F%2Fd1tdmn7vm5ewlp.cloudfront.net%2Fgallery-uploads%2F1733195102611.jpg&w=1080&q=75"
                alt="Logo Design Process"
                className="rounded-lg shadow-2xl"
              />
              <div className="absolute -bottom-6 -right-6 bg-white p-4 rounded-lg shadow-xl">
                <div className="flex items-center space-x-3">
                  <div className="bg-blue-100 p-2 rounded-full">
                    <Palette className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="font-semibold text-gray-900">
                      Creative Process
                    </p>
                    <p className="text-sm text-gray-600">
                      Step by step excellence
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Right side - Content */}
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Our Logo Design Process
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We follow a proven process to ensure your logo perfectly
                represents your brand and resonates with your target audience.
              </p>

              <div className="space-y-6">
                {designProcess.map((process, index) => (
                  <div key={index} className="flex items-start space-x-4">
                    <div className="bg-blue-600 text-white w-12 h-12 rounded-full flex items-center justify-center text-lg font-bold flex-shrink-0">
                      {process.step}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {process.title}
                      </h3>
                      <p className="text-gray-600">{process.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      

      {/* Portfolio Section */}
      <section id="portfolio" className="w-full py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12 items-start">
            {/* Left Column - Client Love Content */}
            <div className="lg:col-span-1">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                What our clients say ...
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                We're a global team of skilled designers united by a passion for design and a commitment to excellence.
              </p>
              
              {/* Statistics */}
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <Star className="h-8 w-8 text-yellow-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Multiple Concepts</div>
                    <div className="text-gray-600"> Multiple logo ideas crafted for your exact brief.</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Lightbulb className="h-8 w-8 text-purple-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Unlimited Tweaks</div>
                    <div className="text-gray-600"> We iterate until you say “perfect.”</div>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Award className="h-8 w-8 text-orange-500" />
                  <div>
                    <div className="text-l font-bold text-gray-900">Print‑Ready Files</div>
                    <div className="text-gray-600">Get PNG, PDF, and editable AI/SVG—no extra fees.</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Right Column - Scrollable Portfolio */}
            <div className="lg:col-span-2">
              <div className="relative">
                {/* Left Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('left')}
                  className={`absolute left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 ${
                    portfolioScrollPosition <= 0
                      ? 'opacity-50 cursor-not-allowed'
                      : 'hover:bg-gray-50 hover:shadow-xl'
                  }`}
                  disabled={portfolioScrollPosition <= 0}
                >
                  <ChevronLeft className="h-5 w-5 text-gray-600" />
                </button>

                {/* Right Navigation Button */}
                <button
                  onClick={() => scrollPortfolio('right')}
                  className="absolute right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-200 hover:bg-gray-50 hover:shadow-xl"
                >
                  <ChevronRight className="h-5 w-5 text-gray-600" />
                </button>

                {/* Scrollable Container */}
                <div 
                  ref={portfolioScrollRef}
                  onScroll={handlePortfolioScroll}
                  className="overflow-x-auto whitespace-nowrap py-4 px-12 scrollbar-hide"
                  style={{
                    scrollbarWidth: 'none',
                    msOverflowStyle: 'none',
                  }}
                >
                <div className="flex space-x-6">
                  {portfolioSamples.map((sample, index) => (
                    <div
                      key={index}
                      className="inline-block w-64 bg-white rounded-lg shadow-lg overflow-hidden flex-shrink-0 hover:shadow-xl transition-shadow duration-300"
                    >
                      <img
                        src={sample.image}
                        alt={sample.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="p-6">
                        <h3 className="font-semibold text-gray-900 mb-2 whitespace-normal">
                          {sample.title}
                        </h3>
                        <p className="text-gray-600 text-sm mb-4 whitespace-normal">{sample.description}</p>
                        
                        {/* Star Rating */}
                        <div className="flex items-center mb-4">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className="h-4 w-4 text-yellow-400 fill-current"
                            />
                          ))}
                          <span className="text-sm text-gray-500 ml-2">5.0</span>
                        </div>
                        
                        {/* Reviewer Info */}
                        <div className="flex items-center space-x-3">
                          <img
                            src={sample.reviewerImage}
                            alt={sample.reviewerName}
                            className="w-8 h-8 rounded-full object-cover"
                          />
                          <p className="text-sm font-medium text-gray-900 whitespace-normal">
                            {sample.reviewerName}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
              </div>
            </div>
          </div>

        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <div className="bg-warm-cream p-8 rounded-lg">
              <p className="text-gray-600 mb-4">
                Ready to create your perfect logo?
              </p>
              <Link
                to="/contact"
                className="bg-brand-orange text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors inline-flex items-center"
              >
                Start Your Logo Project
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LogoDesign;