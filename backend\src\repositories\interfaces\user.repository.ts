import {User, UserAddress, UserProfile, UserStats} from '@/types/user.types';
import {PaginationParams, PaginatedResponse} from '@/types/common.types';

export interface IUserRepository {
	// User CRUD operations
	createUser(userData: Partial<User>): Promise<User>;
	findUserById(id: string): Promise<User | null>;
	findUserByEmail(email: string): Promise<User | null>;
	updateUser(id: string, userData: Partial<User>): Promise<User>;
	deleteUser(id: string): Promise<void>;
	listUsers(params: PaginationParams): Promise<PaginatedResponse<User>>;

	// User profile operations
	getUserProfile(id: string): Promise<UserProfile | null>;
	getUserStats(id: string): Promise<UserStats | null>;

	// Address operations
	createAddress(
		userId: string,
		addressData: Partial<UserAddress>
	): Promise<UserAddress>;
	findAddressById(id: string): Promise<UserAddress | null>;
	findAddressesByUserId(userId: string): Promise<UserAddress[]>;
	updateAddress(
		id: string,
		addressData: Partial<UserAddress>
	): Promise<UserAddress>;
	deleteAddress(id: string): Promise<void>;
	setDefaultAddress(userId: string, addressId: string): Promise<void>;

	// Search operations
	searchUsers(
		query: string,
		params: PaginationParams
	): Promise<PaginatedResponse<User>>;

	// Verification operations
	verifyUserEmail(id: string): Promise<User>;
	updateUserVerification(id: string, isVerified: boolean): Promise<User>;
}
