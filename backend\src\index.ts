import {App} from './config/app';

const PORT = parseInt(process.env['PORT'] || '5000', 10);

// Create and start the application
const app = new App();

// Start the server
app.listen(PORT);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
	console.error('Uncaught Exception:', error);
	process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
	console.error('Unhandled Rejection at:', promise, 'reason:', reason);
	process.exit(1);
});
