// Category Routes - Following REST principles
import { Router } from 'express';
import { CategoryController } from '@/controllers/services/category.controller';
import { ValidationChains } from '@/middleware/validation';
import { handleValidation } from '@/middleware/validation/validator';
import { authenticate, adminOnly } from '@/middleware/auth';

const router = Router();

// Initialize controller
const categoryController = new CategoryController();

// GET /api/v1/categories - Get all categories (Public with admin options)
router.get(
  '/',
  categoryController.getCategories.bind(categoryController)
);

// GET /api/v1/categories/popular - Get most popular categories (Public)
router.get(
  '/popular',
  categoryController.getMostPopularCategories.bind(categoryController)
);

// GET /api/v1/categories/:id - Get category by ID (Public)
router.get(
  '/:id',
  categoryController.getCategoryById.bind(categoryController)
);

// POST /api/v1/categories - Create new category (Admin only)
router.post(
  '/',
  authenticate,
  adminOnly,
  ValidationChains.createCategory(),
  handleValidation,
  categoryController.createCategory.bind(categoryController)
);

// PUT /api/v1/categories/:id - Update category (Admin only)
router.put(
  '/:id',
  authenticate,
  adminOnly,
  ValidationChains.updateCategory(),
  handleValidation,
  categoryController.updateCategory.bind(categoryController)
);

// DELETE /api/v1/categories/:id - Delete category (Admin only)
router.delete(
  '/:id',
  authenticate,
  adminOnly,
  categoryController.deleteCategory.bind(categoryController)
);

// PUT /api/v1/categories/:id/activate - Activate category (Admin only)
router.put(
  '/:id/activate',
  authenticate,
  adminOnly,
  categoryController.activateCategory.bind(categoryController)
);

// PUT /api/v1/categories/:id/deactivate - Deactivate category (Admin only)
router.put(
  '/:id/deactivate',
  authenticate,
  adminOnly,
  categoryController.deactivateCategory.bind(categoryController)
);

// PUT /api/v1/categories/reorder - Reorder categories (Admin only)
router.put(
  '/reorder',
  authenticate,
  adminOnly,
  ValidationChains.reorderCategories(),
  handleValidation,
  categoryController.reorderCategories.bind(categoryController)
);

export default router;