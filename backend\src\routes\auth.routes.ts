import {Router} from 'express';
import {ErrorMiddleware} from '@/middleware/error.middleware';

export const authRoutes = (app: any) => {
	const router = Router();
	const {authController, authMiddleware} = app.dependencies;

	// Public routes
	router.post(
		'/register',
		ErrorMiddleware.asyncHandler(authController.register.bind(authController))
	);
	router.post(
		'/login',
		ErrorMiddleware.asyncHandler(authController.login.bind(authController))
	);
	router.post(
		'/google',
		ErrorMiddleware.asyncHandler(
			authController.loginWithGoogle.bind(authController)
		)
	);
	router.post(
		'/refresh',
		ErrorMiddleware.asyncHandler(
			authController.refreshToken.bind(authController)
		)
	);
	router.post(
		'/logout',
		ErrorMiddleware.asyncHandler(authController.logout.bind(authController))
	);

	// Protected routes
	router.get(
		'/me',
		authMiddleware.authenticate,
		ErrorMiddleware.asyncHandler(
			authController.getCurrentUser.bind(authController)
		)
	);

	router.post(
		'/change-password',
		authMiddleware.authenticate,
		ErrorMiddleware.asyncHandler(
			authController.changePassword.bind(authController)
		)
	);

	return router;
};
