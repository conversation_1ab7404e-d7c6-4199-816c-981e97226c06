// Service Controller - Following SRP and REST principles
import { Request, Response } from 'express';
import { ServiceManagementService, IFileUploadService, INotificationService } from '@/services/services/service-management.service';
import { ServiceRepository, CategoryRepository } from '@/repositories/services';
import { ResponseFormatter } from '@/utils/formatting';
import { HTTP_STATUS } from '@/constants';
import { prisma } from '@/config/database';
import { 
  CreateServiceData, 
  UpdateServiceData, 
  ServiceFilters 
} from '@/types/services';

// Mock implementations for dependencies (to be replaced with actual implementations)
class MockFileUploadService implements IFileUploadService {
  async uploadImage(file: Express.Multer.File, folder: string): Promise<string> {
    return `https://example.com/${folder}/${file.filename}`;
  }

  async uploadMultipleImages(files: Express.Multer.File[], folder: string): Promise<string[]> {
    return files.map(file => `https://example.com/${folder}/${file.filename}`);
  }

  async deleteImage(url: string): Promise<void> {
    // Mock implementation
  }

  async deleteMultipleImages(urls: string[]): Promise<void> {
    // Mock implementation
  }
}

class MockNotificationService implements INotificationService {
  async notifyServiceCreated(serviceId: string, providerId: string): Promise<void> {
    console.log(`Service ${serviceId} created by provider ${providerId}`);
  }

  async notifyServiceUpdated(serviceId: string, providerId: string): Promise<void> {
    console.log(`Service ${serviceId} updated by provider ${providerId}`);
  }

  async notifyServiceDeleted(serviceId: string, providerId: string): Promise<void> {
    console.log(`Service ${serviceId} deleted by provider ${providerId}`);
  }
}

// Service Controller (SRP - handles HTTP requests for services)
export class ServiceController {
  private serviceManagementService: ServiceManagementService;

  constructor() {
    // Dependency injection setup
    const serviceRepository = new ServiceRepository(prisma);
    const categoryRepository = new CategoryRepository(prisma);
    const fileUploadService = new MockFileUploadService();
    const notificationService = new MockNotificationService();
    
    this.serviceManagementService = new ServiceManagementService(
      serviceRepository,
      categoryRepository,
      fileUploadService,
      notificationService
    );
  }

  // GET /api/v1/services - Get all services with search and filters
  async getServices(req: Request, res: Response): Promise<void> {
    try {
      const {
        q: query,
        category,
        provider,
        priceType,
        minPrice,
        maxPrice,
        tags,
        sortBy,
        sortOrder = 'desc',
        page = 1,
        limit = 20
      } = req.query;

      const filters: ServiceFilters = {
        ...(category && { categoryId: category as string }),
        ...(provider && { providerId: provider as string }),
        ...(priceType && { priceType: priceType as any }),
        ...(minPrice && { minPrice: parseFloat(minPrice as string) }),
        ...(maxPrice && { maxPrice: parseFloat(maxPrice as string) }),
        ...(tags && { tags: Array.isArray(tags) ? tags as string[] : [tags as string] }),
        ...(sortBy && { sortBy: sortBy as string }),
        ...(sortOrder && { sortOrder: sortOrder as 'asc' | 'desc' }),
      };

      const pagination = {
        page: parseInt(page as string),
        limit: Math.min(parseInt(limit as string), 100), // Max 100 items per page
      };

      const result = await this.serviceManagementService.searchServices(
        query as string,
        filters,
        pagination
      );

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Services retrieved successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Failed to get services',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get services',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // GET /api/v1/services/:id - Get service by ID
  async getServiceById(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const includeInactive = req.user?.role === 'ADMIN';

      const result = await this.serviceManagementService.getServiceById(id, includeInactive);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Service retrieved successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Service not found' ? HTTP_STATUS.NOT_FOUND : HTTP_STATUS.BAD_REQUEST;
        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to get service',
            result.error || 'Unknown error',
            statusCode,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get service',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // POST /api/v1/services - Create new service
  async createService(req: Request, res: Response): Promise<void> {
    try {
      const userId = req.user?.userId;
      const userRole = req.user?.role;
      const providerId = req.body.providerId || userId; // Default to user ID if no provider ID

      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      // Only providers and admins can create services
      if (userRole !== 'PROVIDER' && userRole !== 'ADMIN') {
        res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseFormatter.error(
            'Access denied',
            'Only providers and admins can create services',
            HTTP_STATUS.FORBIDDEN,
            req.path
          )
        );
        return;
      }

      const serviceData: CreateServiceData = req.body;
      const imageFiles = req.files as Express.Multer.File[];

      const result = await this.serviceManagementService.createService(
        serviceData,
        userId,
        providerId,
        imageFiles
      );

      if (result.success) {
        res.status(HTTP_STATUS.CREATED).json(
          ResponseFormatter.success(
            'Service created successfully',
            result.data,
            HTTP_STATUS.CREATED
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Failed to create service',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path,
            result.validationErrors
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to create service',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // PUT /api/v1/services/:id - Update service
  async updateService(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const updateData: UpdateServiceData = req.body;
      const imageFiles = req.files as Express.Multer.File[];

      const result = await this.serviceManagementService.updateService(
        id,
        updateData,
        userId,
        userRole,
        imageFiles
      );

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Service updated successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Service not found' ? HTTP_STATUS.NOT_FOUND : 
                          result.error === 'You are not authorized to modify this service' ? HTTP_STATUS.FORBIDDEN :
                          HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to update service',
            result.error || 'Unknown error',
            statusCode,
            req.path,
            result.validationErrors
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to update service',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // DELETE /api/v1/services/:id - Delete service
  async deleteService(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const result = await this.serviceManagementService.deleteService(id, userId, userRole);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Service deleted successfully',
            null,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Service not found' ? HTTP_STATUS.NOT_FOUND :
                          result.error === 'You are not authorized to delete this service' ? HTTP_STATUS.FORBIDDEN :
                          HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to delete service',
            result.error || 'Unknown error',
            statusCode,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to delete service',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // GET /api/v1/services/featured - Get featured services
  async getFeaturedServices(req: Request, res: Response): Promise<void> {
    try {
      const limit = Math.min(parseInt(req.query.limit as string) || 10, 50);

      const result = await this.serviceManagementService.getFeaturedServices(limit);

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Featured services retrieved successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Failed to get featured services',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get featured services',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // GET /api/v1/services/categories/:categoryId - Get services by category
  async getServicesByCategory(req: Request, res: Response): Promise<void> {
    try {
      const { categoryId } = req.params;
      const page = parseInt(req.query.page as string) || 1;
      const limit = Math.min(parseInt(req.query.limit as string) || 20, 100);

      const result = await this.serviceManagementService.getServicesByCategory(
        categoryId,
        { page, limit }
      );

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Services retrieved successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Failed to get services by category',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get services by category',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // PUT /api/v1/services/:id/featured - Set service featured status (Admin only)
  async setServiceFeatured(req: Request, res: Response): Promise<void> {
    try {
      const { id } = req.params;
      const { featured } = req.body;
      const userId = req.user?.userId;
      const userRole = req.user?.role;

      if (!userId) {
        res.status(HTTP_STATUS.UNAUTHORIZED).json(
          ResponseFormatter.error(
            'Authentication required',
            'User not authenticated',
            HTTP_STATUS.UNAUTHORIZED,
            req.path
          )
        );
        return;
      }

      const result = await this.serviceManagementService.setServiceFeatured(
        id,
        featured,
        userId,
        userRole
      );

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Service featured status updated successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        const statusCode = result.error === 'Service not found' ? HTTP_STATUS.NOT_FOUND :
                          result.error === 'Only administrators can set featured status' ? HTTP_STATUS.FORBIDDEN :
                          HTTP_STATUS.BAD_REQUEST;

        res.status(statusCode).json(
          ResponseFormatter.error(
            'Failed to update featured status',
            result.error || 'Unknown error',
            statusCode,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to update featured status',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }

  // GET /api/v1/services/stats - Get service statistics (Admin only)
  async getServiceStats(req: Request, res: Response): Promise<void> {
    try {
      const userRole = req.user?.role;

      if (userRole !== 'ADMIN') {
        res.status(HTTP_STATUS.FORBIDDEN).json(
          ResponseFormatter.error(
            'Access denied',
            'Only administrators can view service statistics',
            HTTP_STATUS.FORBIDDEN,
            req.path
          )
        );
        return;
      }

      const result = await this.serviceManagementService.getServiceStats();

      if (result.success) {
        res.status(HTTP_STATUS.OK).json(
          ResponseFormatter.success(
            'Service statistics retrieved successfully',
            result.data,
            HTTP_STATUS.OK
          )
        );
      } else {
        res.status(HTTP_STATUS.BAD_REQUEST).json(
          ResponseFormatter.error(
            'Failed to get service statistics',
            result.error || 'Unknown error',
            HTTP_STATUS.BAD_REQUEST,
            req.path
          )
        );
      }
    } catch (error) {
      res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
        ResponseFormatter.error(
          'Failed to get service statistics',
          error instanceof Error ? error.message : 'Internal server error',
          HTTP_STATUS.INTERNAL_SERVER_ERROR,
          req.path
        )
      );
    }
  }
}