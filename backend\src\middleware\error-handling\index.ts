// Error handling middleware - Following SRP and centralized error management
import { Request, Response, NextFunction } from 'express';
import { ResponseFormatter } from '@/utils/formatting';
import { HTTP_STATUS, ERROR_MESSAGES } from '@/constants';

// Custom error class (SRP)
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  
  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    
    Error.captureStackTrace(this, this.constructor);
  }
}

// Error types for specific scenarios (ISP - Interface Segregation)
export class ValidationError extends AppError {
  constructor(message: string = 'Validation failed') {
    super(message, HTTP_STATUS.VALIDATION_ERROR);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = ERROR_MESSAGES.ACCESS_DENIED) {
    super(message, HTTP_STATUS.UNAUTHORIZED);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Insufficient permissions') {
    super(message, HTTP_STATUS.FORBIDDEN);
  }
}

export class NotFoundError extends AppError {
  constructor(resource: string = 'Resource') {
    super(`${resource} not found`, HTTP_STATUS.NOT_FOUND);
  }
}

export class ConflictError extends AppError {
  constructor(message: string = 'Resource conflict') {
    super(message, HTTP_STATUS.CONFLICT);
  }
}

// Error logger (SRP)
export class ErrorLogger {
  static log(error: Error, req?: Request): void {
    const timestamp = new Date().toISOString();
    const method = req?.method || 'Unknown';
    const url = req?.originalUrl || 'Unknown';
    const userId = (req as any)?.user?.userId || 'Anonymous';
    
    console.error(`[${timestamp}] ${method} ${url} - User: ${userId}`);
    console.error(`Error: ${error.message}`);
    if (error.stack) {
      console.error(`Stack: ${error.stack}`);
    }
    
    // In production, you would send this to a logging service
    // like Winston, Sentry, or CloudWatch
  }
}

// Main error handling middleware (SRP)
export const globalErrorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log the error
  ErrorLogger.log(error, req);
  
  // Handle operational errors
  if (error instanceof AppError && error.isOperational) {
    res.status(error.statusCode).json(
      ResponseFormatter.error(
        error.message,
        error.message,
        error.statusCode,
        req.path
      )
    );
    return;
  }
  
  // Handle known error types
  if (error.name === 'ValidationError') {
    res.status(HTTP_STATUS.VALIDATION_ERROR).json(
      ResponseFormatter.error(
        'Validation Error',
        error.message,
        HTTP_STATUS.VALIDATION_ERROR,
        req.path
      )
    );
    return;
  }
  
  if (error.name === 'CastError') {
    res.status(HTTP_STATUS.BAD_REQUEST).json(
      ResponseFormatter.error(
        'Invalid ID format',
        'The provided ID is not valid',
        HTTP_STATUS.BAD_REQUEST,
        req.path
      )
    );
    return;
  }
  
  if (error.name === 'JsonWebTokenError') {
    res.status(HTTP_STATUS.UNAUTHORIZED).json(
      ResponseFormatter.error(
        ERROR_MESSAGES.TOKEN_INVALID,
        'Invalid token',
        HTTP_STATUS.UNAUTHORIZED,
        req.path
      )
    );
    return;
  }
  
  if (error.name === 'TokenExpiredError') {
    res.status(HTTP_STATUS.UNAUTHORIZED).json(
      ResponseFormatter.error(
        ERROR_MESSAGES.TOKEN_EXPIRED,
        'Token has expired',
        HTTP_STATUS.UNAUTHORIZED,
        req.path
      )
    );
    return;
  }
  
  // Handle database errors (example for Prisma)
  if (error.name === 'PrismaClientKnownRequestError') {
    const prismaError = error as any;
    
    if (prismaError.code === 'P2002') {
      res.status(HTTP_STATUS.CONFLICT).json(
        ResponseFormatter.error(
          'Duplicate entry',
          'A record with this information already exists',
          HTTP_STATUS.CONFLICT,
          req.path
        )
      );
      return;
    }
    
    if (prismaError.code === 'P2025') {
      res.status(HTTP_STATUS.NOT_FOUND).json(
        ResponseFormatter.error(
          'Record not found',
          'The requested record does not exist',
          HTTP_STATUS.NOT_FOUND,
          req.path
        )
      );
      return;
    }
  }
  
  // Handle multer errors (file uploads)
  if (error.name === 'MulterError') {
    const multerError = error as any;
    
    if (multerError.code === 'LIMIT_FILE_SIZE') {
      res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseFormatter.error(
          ERROR_MESSAGES.FILE_TOO_LARGE,
          'File size exceeds the maximum allowed limit',
          HTTP_STATUS.BAD_REQUEST,
          req.path
        )
      );
      return;
    }
    
    if (multerError.code === 'LIMIT_UNEXPECTED_FILE') {
      res.status(HTTP_STATUS.BAD_REQUEST).json(
        ResponseFormatter.error(
          'Unexpected file',
          'Unexpected file field',
          HTTP_STATUS.BAD_REQUEST,
          req.path
        )
      );
      return;
    }
  }
  
  // Default error response for unhandled errors
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
    ResponseFormatter.error(
      ERROR_MESSAGES.INTERNAL_ERROR,
      isDevelopment ? error.message : 'Something went wrong',
      HTTP_STATUS.INTERNAL_SERVER_ERROR,
      req.path
    )
  );
};

// 404 handler for undefined routes (SRP)
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.originalUrl}`);
  next(error);
};

// Async error wrapper (DRY principle)
export const catchAsync = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// Request timeout middleware
export const requestTimeout = (timeoutMs: number = 30000) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const timeout = setTimeout(() => {
      if (!res.headersSent) {
        res.status(HTTP_STATUS.INTERNAL_SERVER_ERROR).json(
          ResponseFormatter.error(
            'Request timeout',
            'The request took too long to process',
            HTTP_STATUS.INTERNAL_SERVER_ERROR,
            req.path
          )
        );
      }
    }, timeoutMs);
    
    res.on('finish', () => {
      clearTimeout(timeout);
    });
    
    next();
  };
};