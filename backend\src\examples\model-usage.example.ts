// Model Usage Examples - Demonstrating the new domain model architecture

import { UserModel, ServiceModel, ProviderProfileModel, OrderModel, ModelFactory } from '@/models';
import { UserRole, ServicePriceType, OrderStatus } from '@prisma/client';

/**
 * Example 1: Creating and managing a User with business logic
 */
export async function userModelExample() {
  console.log('=== User Model Example ===');

  // Create a new user using the domain model
  const userResult = await UserModel.createUser({
    name: '<PERSON>',
    email: '<EMAIL>',
    password: 'SecurePassword123!',
    role: UserRole.USER,
    phone: '******-0123',
    city: 'San Francisco',
    state: 'CA',
    country: 'US'
  });

  if (userResult.isFailure) {
    console.error('Failed to create user:', userResult.error);
    return;
  }

  const user = userResult.value;
  console.log('Created user:', user.toPublicObject());

  // Update user profile with business logic validation
  const updateResult = user.updateProfile({
    name: '<PERSON>',
    address: '123 Main Street',
    zipCode: '94105'
  });

  if (updateResult.isSuccess) {
    console.log('Profile updated successfully');
    console.log('Profile completeness:', {
      name: user.getName(),
      email: user.getEmail().getValue(),
      fullAddress: user.getProfile().getFullAddress()
    });
  }

  // Change password with validation
  const passwordResult = await user.changePassword('SecurePassword123!', 'NewSecurePassword456!');
  if (passwordResult.isSuccess) {
    console.log('Password changed successfully');
  }

  // Promote to provider if eligible
  const promotionResult = user.promoteToProvider();
  if (promotionResult.isFailure) {
    console.log('Cannot promote to provider:', promotionResult.error);
  }

  return user;
}

/**
 * Example 2: Creating and managing a Service with business logic
 */
export function serviceModelExample() {
  console.log('\n=== Service Model Example ===');

  // Create a service using the domain model
  const serviceResult = ServiceModel.createService({
    name: 'Professional Logo Design',
    description: 'Custom logo design for your business with unlimited revisions and multiple format deliveries.',
    categoryId: 'cat-123',
    providerId: 'prov-456',
    userId: 'user-789',
    price: 299.99,
    priceType: ServicePriceType.FIXED,
    duration: 240, // 4 hours
    location: 'Remote',
    images: ['https://example.com/portfolio1.jpg', 'https://example.com/portfolio2.jpg'],
    tags: ['logo', 'design', 'branding', 'professional'],
    requirements: 'Please provide brand guidelines and any specific requirements.',
    deliverables: 'Final logo in PNG, SVG, and PDF formats with source files.'
  });

  if (serviceResult.isFailure) {
    console.error('Failed to create service:', serviceResult.error);
    return;
  }

  const service = serviceResult.value;
  console.log('Created service:', service.toSearchResult());

  // Update pricing with validation
  const pricingResult = service.updatePricing(349.99, ServicePriceType.FIXED);
  if (pricingResult.isSuccess) {
    console.log('Pricing updated. New formatted price:', service.getPrice().getFormattedPrice());
  }

  // Record some orders and update rating
  service.recordOrder();
  service.recordOrder();
  service.recordOrder();

  const ratingResult = service.updateRating(4.8, 15);
  if (ratingResult.isSuccess) {
    console.log('Rating updated:', service.getRating().getDisplayRating());
    console.log('Popularity score:', service.getPopularityScore());
    console.log('Eligible for featuring:', service.isEligibleForFeaturing());
  }

  // Feature the service if eligible
  const featureResult = service.feature();
  if (featureResult.isSuccess) {
    console.log('Service featured successfully');
  }

  return service;
}

/**
 * Example 3: Creating and managing a Provider Profile
 */
export function providerProfileExample() {
  console.log('\n=== Provider Profile Example ===');

  // Create a provider profile
  const providerResult = ProviderProfileModel.createProviderProfile({
    userId: 'user-provider-123',
    businessName: 'Creative Design Studio',
    description: 'Professional design services specializing in branding and marketing materials.',
    website: 'https://creativedesignstudio.com',
    businessPhone: '******-0199',
    businessAddress: '456 Design Ave',
    businessCity: 'San Francisco',
    businessState: 'CA',
    businessZip: '94102',
    licenseNumber: 'BUS-2023-12345',
    insuranceNumber: 'INS-567890'
  });

  if (providerResult.isFailure) {
    console.error('Failed to create provider:', providerResult.error);
    return;
  }

  const provider = providerResult.value;
  console.log('Created provider:', provider.toSearchResult());

  // Check profile completeness
  const completeness = provider.getProfileCompleteness();
  console.log('Profile completeness:', completeness);

  // Verify the provider
  const verificationResult = provider.verify('admin-123', 'BUS-2023-12345', 'INS-567890');
  if (verificationResult.isSuccess) {
    console.log('Provider verified successfully');
    console.log('Verification status:', provider.getVerificationInfo().getVerificationStatus());
  }

  // Update ratings
  const ratingUpdateResult = provider.updateRating(4.7, 23, 47);
  if (ratingUpdateResult.isSuccess) {
    console.log('Provider rating updated');
    console.log('Reputation level:', provider.getRating().getReputationLevel());
    console.log('Can accept orders:', provider.canAcceptOrders());
  }

  return provider;
}

/**
 * Example 4: Creating and managing an Order with business logic
 */
export function orderModelExample() {
  console.log('\n=== Order Model Example ===');

  // Create an order
  const orderResult = OrderModel.createOrder({
    serviceId: 'service-123',
    userId: 'user-456',
    providerId: 'provider-789',
    totalAmount: 299.99,
    customerNotes: 'Please include our existing brand colors in the design.',
    scheduledDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    files: ['https://example.com/brief.pdf', 'https://example.com/reference.jpg']
  });

  if (orderResult.isFailure) {
    console.error('Failed to create order:', orderResult.error);
    return;
  }

  const order = orderResult.value;
  console.log('Created order:', order.toCustomerView());
  console.log('Order number:', order.getOrderNumber().getValue());
  console.log('Formatted amount:', order.getAmount().getFormattedPrice());

  // Transition through order states
  const confirmResult = order.updateStatus(OrderStatus.CONFIRMED, 'Order confirmed by provider', 'provider-789');
  if (confirmResult.isSuccess) {
    console.log('Order confirmed');
  }

  const startResult = order.updateStatus(OrderStatus.IN_PROGRESS, 'Work started on the design', 'provider-789');
  if (startResult.isSuccess) {
    console.log('Order in progress');
  }

  // Add files during work
  const filesResult = order.addFiles(['https://example.com/draft1.png', 'https://example.com/draft2.png']);
  if (filesResult.isSuccess) {
    console.log('Files added. Total files:', order.getFiles().getFileCount());
  }

  // Complete the order
  const completeResult = order.complete('provider-789', 'Design completed as requested');
  if (completeResult.isSuccess) {
    console.log('Order completed successfully');
    console.log('Final order state:', order.toProviderView());
  }

  return order;
}

/**
 * Example 5: Using ModelFactory for convenient model creation
 */
export async function modelFactoryExample() {
  console.log('\n=== Model Factory Example ===');

  // Use factory methods for consistent model creation
  const userResult = await ModelFactory.createUser({
    name: 'Jane Designer',
    email: '<EMAIL>',
    password: 'SecurePass123!',
    role: UserRole.PROVIDER
  });

  if (userResult.isSuccess) {
    const user = userResult.value;
    console.log('Created user via factory:', user.toAuthResponse());

    // Create provider profile for the user
    const providerResult = ModelFactory.createProviderProfile({
      userId: user.id,
      businessName: 'Jane\'s Design Works',
      description: 'Freelance graphic designer specializing in modern designs.'
    });

    if (providerResult.isSuccess) {
      const provider = providerResult.value;
      console.log('Created provider via factory:', provider.toPublicObject());
    }
  }
}

/**
 * Example 6: Domain Events and Business Logic
 */
export function domainEventsExample() {
  console.log('\n=== Domain Events Example ===');

  // Subscribe to domain events
  const { DomainEventPublisher } = require('@/models');

  DomainEventPublisher.subscribe('UserCreated', (event: any) => {
    console.log('User created event received:', event.data);
    // Could trigger email sending, analytics, etc.
  });

  DomainEventPublisher.subscribe('OrderStatusChanged', (event: any) => {
    console.log('Order status changed:', event.data);
    // Could trigger notifications, updates to other systems, etc.
  });

  DomainEventPublisher.subscribe('ServiceFeatured', (event: any) => {
    console.log('Service featured:', event.data);
    // Could trigger marketing campaigns, notifications, etc.
  });

  console.log('Domain event subscribers registered');
}

/**
 * Run all examples
 */
export async function runAllExamples() {
  try {
    await userModelExample();
    serviceModelExample();
    providerProfileExample();
    orderModelExample();
    await modelFactoryExample();
    domainEventsExample();
    
    console.log('\n=== All Examples Completed Successfully ===');
  } catch (error) {
    console.error('Example execution failed:', error);
  }
}

// Export for use in other files
export default {
  userModelExample,
  serviceModelExample,
  providerProfileExample,
  orderModelExample,
  modelFactoryExample,
  domainEventsExample,
  runAllExamples
};