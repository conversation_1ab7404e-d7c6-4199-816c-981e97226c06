// API Routes - Mirror frontend routes for API endpoints
export const API_ROUTES = {
  // Base API path
  BASE: '/api/v1',
  
  // Authentication routes
  AUTH: {
    BASE: '/auth',
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    VERIFY_EMAIL: '/auth/verify-email',
    FORGOT_PASSWORD: '/auth/forgot-password',
    RESET_PASSWORD: '/auth/reset-password',
    GOOGLE: '/auth/google',
    PROFILE: '/auth/profile',
  },
  
  // User routes
  USERS: {
    BASE: '/users',
    PROFILE: '/users/profile',
    UPDATE_PROFILE: '/users/profile',
    CHANGE_PASSWORD: '/users/change-password',
    UPLOAD_AVATAR: '/users/upload-avatar',
    PREFERENCES: '/users/preferences',
  },
  
  // Service routes
  SERVICES: {
    BASE: '/services',
    CATEGORIES: '/services/categories',
    SEARCH: '/services/search',
    FEATURED: '/services/featured',
    BY_ID: (id: string) => `/services/${id}`,
    BY_CATEGORY: (category: string) => `/services/category/${category}`,
    REVIEWS: (id: string) => `/services/${id}/reviews`,
  },
  
  // Provider routes
  PROVIDERS: {
    BASE: '/providers',
    SEARCH: '/providers/search',
    BY_ID: (id: string) => `/providers/${id}`,
    SERVICES: (id: string) => `/providers/${id}/services`,
    REVIEWS: (id: string) => `/providers/${id}/reviews`,
    PORTFOLIO: (id: string) => `/providers/${id}/portfolio`,
    DASHBOARD: '/providers/dashboard',
    REGISTER: '/providers/register',
    UPDATE_PROFILE: '/providers/profile',
    UPLOAD_LOGO: '/providers/upload-logo',
    SETTINGS: '/providers/settings',
  },
  
  // Order routes
  ORDERS: {
    BASE: '/orders',
    BY_ID: (id: string) => `/orders/${id}`,
    CUSTOMER_ORDERS: '/orders/customer',
    PROVIDER_ORDERS: '/orders/provider',
    CREATE_QUOTE: '/orders/quote',
    ACCEPT_QUOTE: (id: string) => `/orders/${id}/accept`,
    CANCEL: (id: string) => `/orders/${id}/cancel`,
    COMPLETE: (id: string) => `/orders/${id}/complete`,
    TIMELINE: (id: string) => `/orders/${id}/timeline`,
  },
  
  // Review routes
  REVIEWS: {
    BASE: '/reviews',
    BY_ID: (id: string) => `/reviews/${id}`,
    CREATE: '/reviews',
    RESPOND: (id: string) => `/reviews/${id}/respond`,
    FLAG: (id: string) => `/reviews/${id}/flag`,
  },
  
  // Admin routes
  ADMIN: {
    BASE: '/admin',
    USERS: '/admin/users',
    PROVIDERS: '/admin/providers',
    SERVICES: '/admin/services',
    ORDERS: '/admin/orders',
    REVIEWS: '/admin/reviews',
    ANALYTICS: '/admin/analytics',
    CATEGORIES: '/admin/categories',
    SETTINGS: '/admin/settings',
  },
  
  // File upload routes
  UPLOAD: {
    BASE: '/upload',
    IMAGE: '/upload/image',
    DOCUMENT: '/upload/document',
    AVATAR: '/upload/avatar',
    LOGO: '/upload/logo',
    PORTFOLIO: '/upload/portfolio',
  },
  
  // Utility routes
  HEALTH: '/health',
  PING: '/ping',
  VERSION: '/version',
} as const;

// Frontend route mappings for reference
export const FRONTEND_ROUTES = {
  HOME: '/',
  SERVICES: '/services',
  CONTACT: '/contact',
  OUR_WORK: '/our-work',
  LOGIN: '/login',
  REGISTER: '/register',
  DASHBOARD: '/dashboard',
  CUSTOMER_PROFILE: '/customer-profile',
  PROVIDER_DASHBOARD: '/provider-dashboard',
  ADMIN: '/admin',
  SERVICE_DETAIL: (id: string) => `/service/${id}`,
  PROVIDER_PROFILE: (id: string) => `/provider/${id}`,
} as const;