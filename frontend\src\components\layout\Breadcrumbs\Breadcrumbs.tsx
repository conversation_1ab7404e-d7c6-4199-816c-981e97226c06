import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useServices } from '../../../contexts';
import { useProviders } from '../../../contexts';
import { ChevronRight, Home } from 'lucide-react';

interface BreadcrumbItem {
  label: string;
  path: string;
  isLast: boolean;
}

const Breadcrumbs: React.FC = () => {
  const location = useLocation();
  const { services } = useServices();
  const { providers } = useProviders();

  // Don't show breadcrumbs on home page
  if (location.pathname === '/') {
    return null;
  }

  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const pathname = location.pathname;

    // Define breadcrumb routes with exact patterns
    const breadcrumbRoutes = [
      // Service detail pages - /service/:id
      {
        pattern: /^\/service\/([^\/]+)$/,
        generate: (matches: RegExpMatchArray) => {
          const serviceId = matches[1];
          const service = services.find(s => s.id === serviceId);
          
          if (service) {
            return [
              { label: 'Home', path: '/', isLast: false },
              { label: service.category, path: `/services?category=${encodeURIComponent(service.category)}`, isLast: false },
              { label: service.name, path: `/service/${service.id}`, isLast: false },
              { label: 'Order', path: pathname, isLast: true }
            ];
          }
          return [
            { label: 'Home', path: '/', isLast: false },
            { label: 'Service', path: pathname, isLast: true }
          ];
        }
      },
      
      // Provider detail pages - /provider/:id
      {
        pattern: /^\/provider\/([^\/]+)$/,
        generate: (matches: RegExpMatchArray) => {
          const providerId = matches[1];
          const provider = providers.find(p => p.id === providerId);
          
          return [
            { label: 'Home', path: '/', isLast: false },
            { label: 'Find Providers', path: '/find-providers', isLast: false },
            { label: provider?.businessName || 'Provider', path: pathname, isLast: true }
          ];
        }
      },

      // Design Services nested routes
      {
        pattern: /^\/design-services\/logo-design$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Design Studio', path: '/design-services', isLast: false },
          { label: 'Logo Design', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/design-services\/website-design$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Design Studio', path: '/design-services', isLast: false },
          { label: 'Website Design', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/design-services\/printing-design$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Design Studio', path: '/design-services', isLast: false },
          { label: 'Printing Design', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/design-services\/package-design$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Design Studio', path: '/design-services', isLast: false },
          { label: 'Package Design', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/design-services\/illustrator-art$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Design Studio', path: '/design-services', isLast: false },
          { label: 'Illustrator Art', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/design-services\/vehicle-wraps$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Design Studio', path: '/design-services', isLast: false },
          { label: 'Vehicle Wraps', path: pathname, isLast: true }
        ]
      },

      // Category pages
      {
        pattern: /^\/business-cards$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Business Cards', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/marketing-materials$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Marketing Materials', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/signs-banners$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Signs & Banners', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/invitations-stationery$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Invitations & Stationery', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/stickers-labels$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Stickers & Labels', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/gifts-decor$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Gifts & Décor', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/apparel$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Apparel', path: pathname, isLast: true }
        ]
      },

      // Main pages
      {
        pattern: /^\/services$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Services', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/design-services$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Design Studio', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/provider-services$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Partner With Us', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/find-providers$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Find Providers', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/contact$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Contact', path: pathname, isLast: true }
        ]
      },

      // User pages
      {
        pattern: /^\/login$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Sign In', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/register$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Sign Up', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/dashboard$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Dashboard', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/admin$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Admin Panel', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/provider-dashboard$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'Provider Dashboard', path: pathname, isLast: true }
        ]
      },
      {
        pattern: /^\/customer-profile$/,
        generate: () => [
          { label: 'Home', path: '/', isLast: false },
          { label: 'My Profile', path: pathname, isLast: true }
        ]
      }
    ];

    // Find matching route and generate breadcrumbs
    for (const route of breadcrumbRoutes) {
      const matches = pathname.match(route.pattern);
      if (matches) {
        return route.generate(matches);
      }
    }

    // Fallback for unmatched routes
    const pathSegments = pathname.split('/').filter(segment => segment !== '');
    const lastSegment = pathSegments[pathSegments.length - 1];
    const label = lastSegment
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');

    return [
      { label: 'Home', path: '/', isLast: false },
      { label, path: pathname, isLast: true }
    ];
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <nav className="bg-gray-50 border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center space-x-2 py-3 overflow-x-auto">
          {breadcrumbs.map((breadcrumb, index) => (
            <React.Fragment key={`${breadcrumb.path}-${index}`}>
              {index > 0 && (
                <ChevronRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
              )}
              
              {breadcrumb.isLast ? (
                <span className="text-gray-900 font-medium text-sm whitespace-nowrap">
                  {breadcrumb.label}
                </span>
              ) : (
                <Link
                  to={breadcrumb.path}
                  className="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors flex items-center space-x-1 whitespace-nowrap"
                >
                  {index === 0 && <Home className="h-4 w-4" />}
                  <span>{breadcrumb.label}</span>
                </Link>
              )}
            </React.Fragment>
          ))}
        </div>
      </div>
    </nav>
  );
};

export default Breadcrumbs;