import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../../contexts';
import {
  Printer,
  Menu,
  X,
  User,
  ShoppingCart,
  Settings,
  Search,
} from 'lucide-react';

const Header: React.FC = () => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const handleLogout = () => {
    logout();
    navigate('/');
    setIsUserMenuOpen(false);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/services?search=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };

  const categoryItems = [
    { name: 'All Products', category: 'All Products', route: '/services' },
    { name: 'Our Work', category: 'Gallery', route: '/our-work' },
    {
      name: 'Business Materials',
      category: 'Business Cards',
      route: '/business-cards',
    },
    {
      name: 'Marketing Materials',
      category: 'Marketing Materials',
      route: '/marketing-materials',
    },
    {
      name: 'Large Format',
      category: 'Signs & Banners',
      route: '/signs-banners',
    },
    {
      name: 'Invitations & Gifts',
      category: 'Invitations & Stationery',
      route: '/invitations-stationery',
    },
    {
      name: 'Stickers & Labels',
      category: 'Stickers & Labels',
      route: '/stickers-labels',
    },
    { name: 'Packaging', category: 'Gifts & Décor', route: '/gifts-decor' },
    { name: 'Apparel', category: 'Apparel', route: '/apparel' },
    {
      name: 'Design Studio',
      category: 'Design Services',
      route: '/design-services',
    },
  ];

  return (
    <header className="bg-white shadow-lg sticky top-0 z-50">
      {/* Main header row with logo and user menu - constrained to content width */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <img src="/weditt logo.png" alt="Weditt" className="h-12 w-auto" />
          </Link>

          {/* Search Bar - Desktop */}
          <div className="hidden md:flex flex-1 max-w-lg mx-8">
            <form onSubmit={handleSearch} className="w-full">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search services..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                />
              </div>
            </form>
          </div>

          {/* Right side: Find Providers, Contact, Cart, User Menu, Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            {/* Find Providers and Contact buttons - Desktop only */}
            <Link
              to="/design-services"
              className="hidden md:inline-flex bg-brand-orange text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors font-medium"
            >
              Design Studio
            </Link>
            <Link
              to="/find-providers"
              className="hidden md:inline-flex border-2 border-brand-orange text-brand-orange px-4 py-2 rounded-lg hover:bg-brand-orange hover:text-white transition-colors font-medium"
            >
              Providers
            </Link>

            {/* Shopping Cart */}
            <button className="p-2 text-gray-700 hover:text-orange-500 transition-colors">
              <ShoppingCart className="h-6 w-6" />
            </button>

            {/* User Menu */}
            {user ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <User className="h-6 w-6 text-gray-700" />
                  <span className="text-gray-700 hidden sm:inline">
                    {user.name}
                  </span>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 z-50">
                    <Link
                      to="/dashboard"
                      className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      Dashboard
                    </Link>
                    <Link
                      to="/customer-profile"
                      className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      My Profile
                    </Link>
                    <Link
                      to="/provider-dashboard"
                      className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      Provider Dashboard
                    </Link>
                    {user.role === 'admin' && (
                      <Link
                        to="/admin"
                        className="block px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <div className="flex items-center space-x-2">
                          <Settings className="h-4 w-4" />
                          <span>Admin Panel</span>
                        </div>
                      </Link>
                    )}
                    <hr className="my-2" />
                    <button
                      onClick={handleLogout}
                      className="block w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                      Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link
                  to="/login"
                  className="text-gray-700 hover:text-orange-500 transition-colors"
                >
                  Login
                </Link>
                <Link
                  to="/register"
                  className="bg-brand-orange text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                >
                  Sign Up
                </Link>
              </div>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-gray-700 hover:text-orange-500 transition-colors"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Category Navigation - centered and constrained to content width */}
      <div className="border-t border-gray-200 hidden md:block">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap py-3">
            {categoryItems.map((item) => (
              <Link
                key={item.category}
                to={item.route}
                className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-orange-500 transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <div className="md:hidden border-t">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-4">
              <nav className="flex flex-col space-y-2">
                {/* Search Bar - Mobile */}
                <form onSubmit={handleSearch} className="mb-4">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
                    <input
                      type="text"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      placeholder="Search services..."
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                  </div>
                </form>

                {/* Find Providers and Contact buttons for mobile */}
                <Link
                  to="/find-providers"
                  className="bg-brand-orange text-white px-4 py-3 rounded-lg hover:bg-orange-600 transition-colors font-medium text-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Find Providers
                </Link>
                <Link
                  to="/contact"
                  className="border-2 border-brand-orange text-brand-orange px-4 py-3 rounded-lg hover:bg-brand-orange hover:text-white transition-colors font-medium text-center"
                  onClick={() => setIsMenuOpen(false)}
                >
                  Contact
                </Link>

                {/* Mobile Category Links */}
                <hr className="my-2" />
                <div className="text-sm font-medium text-gray-500 py-1">
                  Categories
                </div>
                {categoryItems.map((item) => (
                  <Link
                    key={item.category}
                    to={item.route}
                    className="text-gray-700 hover:text-orange-500 transition-colors py-2 pl-4"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
              </nav>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
